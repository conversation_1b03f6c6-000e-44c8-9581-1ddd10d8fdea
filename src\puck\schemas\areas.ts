import React from 'react';
import { type Config } from '@measured/puck';
import { Link } from 'react-router-dom';
import { MapPin, Search, Phone } from 'lucide-react';
import { sharedFields, defaultVerticalPadding } from './shared';

interface HeroProps {
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface CountyProps {
  name: string;
  cities: Array<{ text: string }>;
}

interface CountiesProps {
  title?: string;
  counties?: CountyProps[];
  verticalPadding?: string;
}

interface CTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  verticalPadding?: string;
}

export const areasConfig: Config = {
  components: {
    AreasHero: {
      render: ({
        title = 'Areas We Serve',
        subtitle = 'Roofers LLC provides professional roofing services across Florida\'s most populous counties. Contact us today to schedule your free estimate!',
        backgroundImage = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-primary text-white ${verticalPadding}`,
          children: [
            React.createElement('div', {
              key: 'background',
              className: 'absolute inset-0 bg-cover bg-center opacity-20',
              style: { backgroundImage: `url("${backgroundImage}")` }
            }),
            React.createElement('div', {
              key: 'content',
              className: 'container mx-auto px-4 relative',
              children: [
                React.createElement('h1', {
                  key: 'title',
                  className: 'text-5xl font-bold mb-6 text-center'
                }, title),
                React.createElement('p', {
                  key: 'subtitle',
                  className: 'text-xl text-center max-w-2xl mx-auto mb-12'
                }, subtitle),
                React.createElement('div', {
                  key: 'search',
                  className: 'max-w-xl mx-auto relative',
                  children: [
                    React.createElement(Search, {
                      key: 'icon',
                      className: 'absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60'
                    }),
                    React.createElement('input', {
                      key: 'input',
                      type: 'text',
                      placeholder: 'Search for your city or county...',
                      className: 'w-full pl-12 pr-4 py-3 rounded-full bg-white/10 backdrop-blur border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent'
                    })
                  ]
                })
              ]
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        backgroundImage: { type: 'text' }
      }
    },

    AreasGrid: {
      render: ({
        title = 'Service Areas',
        counties = [],
        verticalPadding = defaultVerticalPadding
      }: CountiesProps) => {
        return React.createElement('section', {
          className: `${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4',
            children: React.createElement('div', {
              className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8',
              children: counties.map((county) => 
                React.createElement('div', {
                  key: county.name,
                  className: 'bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-[1.02] transition-transform'
                }, [
                  React.createElement('div', {
                    key: 'content',
                    className: 'p-6',
                    children: [
                      React.createElement('div', {
                        key: 'header',
                        className: 'flex items-center justify-between mb-4',
                        children: [
                          React.createElement('div', {
                            key: 'title',
                            className: 'flex items-center',
                            children: [
                              React.createElement(MapPin, {
                                key: 'icon',
                                className: 'w-6 h-6 text-accent mr-2'
                              }),
                              React.createElement('h3', {
                                key: 'name',
                                className: 'text-xl font-bold text-primary'
                              }, county.name)
                            ]
                          }),
                          React.createElement('span', {
                            key: 'count',
                            className: 'text-gray-500'
                          }, `${county.cities.length} cities`)
                        ]
                      }),
                      React.createElement('ul', {
                        key: 'cities',
                        className: 'grid grid-cols-2 gap-2 mt-4',
                        children: county.cities.map((city, index) => 
                          React.createElement('li', {
                            key: index,
                            className: 'text-gray-600 flex items-center',
                            children: [
                              React.createElement('div', {
                                key: 'dot',
                                className: 'w-1.5 h-1.5 bg-accent rounded-full mr-2'
                              }),
                              city.text
                            ]
                          })
                        )
                      })
                    ]
                  })
                ])
              )
            })
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        counties: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            cities: {
              type: 'array',
              arrayFields: {
                text: { type: 'text' }
              }
            }
          }
        }
      }
    },

    AreasCTA: {
      render: ({
        title = 'Ready to Get Started?',
        description = 'Our expert team is ready to serve you in any of these locations. Contact us today for a free consultation!',
        buttonText = 'Get Free Estimate',
        buttonLink = '/request-estimate',
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4 text-center',
            children: [
              React.createElement('h2', {
                key: 'title',
                className: 'text-3xl font-bold mb-6'
              }, title),
              React.createElement('p', {
                key: 'description',
                className: 'text-xl text-gray-600 mb-8 max-w-2xl mx-auto'
              }, description),
              React.createElement('div', {
                key: 'buttons',
                className: 'flex flex-col sm:flex-row gap-4 justify-center',
                children: [
                  React.createElement(Link, {
                    key: 'cta',
                    to: buttonLink,
                    className: 'bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-flex items-center justify-center'
                  }, buttonText),
                  React.createElement('a', {
                    key: 'phone',
                    href: 'tel:+13053761808',
                    className: 'bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors inline-flex items-center justify-center'
                  }, [
                    React.createElement(Phone, {
                      key: 'icon',
                      className: 'w-5 h-5 mr-2'
                    }),
                    '(*************'
                  ])
                ]
              })
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' }
      }
    }
  }
};