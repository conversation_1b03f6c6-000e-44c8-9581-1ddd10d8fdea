@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-primary hover:bg-primary-dark rounded-md transition-all duration-200;
  }

  .btn-accent {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-accent hover:bg-accent/90 rounded-md transition-all duration-200;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-primary bg-white hover:bg-gray-50 rounded-md transition-all duration-200;
  }

  .btn-white {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-primary bg-white hover:bg-gray-50 rounded-md shadow-md transition-all duration-200;
  }

  .nav-link {
    @apply text-gray-700 hover:text-primary transition-colors duration-200;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fade-in 1s ease-out forwards;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
}

/* Apply smooth scrolling to the entire page */
html {
  scroll-behavior: smooth;
}

/* Hero section enhancements */
.hero-gradient {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

/* Add a subtle text shadow to hero text */
.hero-text-shadow {
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Enhance button hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Add gradient text effect */
.gradient-text {
  background: linear-gradient(45deg, var(--tw-gradient-from), var(--tw-gradient-to));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}