import { useLocation, Link } from 'react-router-dom';
import { CheckCircle } from 'lucide-react';

const EstimateSuccess = () => {
  const location = useLocation();
  const { name = 'there', serviceType = 'roofing', email = '' } = location.state || {};

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
            <h1 className="text-3xl font-bold mb-2">Estimate Request Received!</h1>
            <p className="text-xl text-gray-600">
              Thank you {name} for requesting a free estimate for your {serviceType.toLowerCase()} project.
            </p>
          </div>

          <div className="space-y-6 text-gray-600">
            <div className="border-t border-b border-gray-100 py-4">
              <h2 className="text-xl font-semibold mb-4">What happens next?</h2>
              <ol className="list-decimal list-inside space-y-2">
                <li>Our team will review your estimate request</li>
                <li>We'll contact you within 24 hours at {email}</li>
                <li>We'll schedule a convenient time for an on-site assessment</li>
                <li>You'll receive a detailed, written estimate for your project</li>
              </ol>
            </div>

            <p>
              Have questions? Call us at{' '}
              <a 
                href="tel:+13053761808"
                className="text-primary hover:text-primary-dark font-semibold"
              >
                (*************
              </a>
            </p>

            <div className="text-center pt-6">
              <Link
                to="/"
                className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
              >
                Return to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EstimateSuccess;