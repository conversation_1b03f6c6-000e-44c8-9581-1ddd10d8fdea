import { FC, useState, FormEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { sendEmail } from '../../lib/api';

interface ConsultationFormProps {
  serviceType: string;
}

const ConsultationForm: FC<ConsultationFormProps> = ({ serviceType }) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    const formData = new FormData(e.currentTarget);
    const formFields = Object.fromEntries(formData.entries());

    try {
      // Format the email content
      const emailContent = {
        to: '<EMAIL>',
        subject: `New Consultation Request - ${serviceType}`,
        message: `
          New consultation request details:
          
          Service Type: ${serviceType}
          Name: ${formFields.name}
          Email: ${formFields.email}
          Phone: ${formFields.phone}
          Address: ${formFields.address}
          Additional Details: ${formFields.message || 'No additional details provided'}
        `
      };

      // Send the email using the temporary API service
      await sendEmail(emailContent);

      // Navigate to success page with form data
      navigate('/consultation-success', {
        state: {
          serviceType,
          email: formFields.email,
          name: formFields.name
        }
      });
    } catch (err) {
      setError('Failed to submit form. Please try again or contact us directly at (*************.');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 border border-blue-200 text-blue-800 rounded-lg p-4 mb-6">
          Note: This form is in test mode. No actual emails will be sent during development.
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6">
          <p>{error}</p>
          <div className="mt-2">
            <a
              href="tel:+13053761808"
              className="text-red-600 font-semibold hover:text-red-700"
            >
              Call (*************
            </a>
          </div>
        </div>
      )}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <label htmlFor="name" className="block font-medium text-gray-700">
            Full Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="John Doe"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="block font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="phone" className="block font-medium text-gray-700">
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="(*************"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="address" className="block font-medium text-gray-700">
            Property Address
          </label>
          <input
            type="text"
            id="address"
            name="address"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="123 Main St, City, FL"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="message" className="block font-medium text-gray-700">
            Additional Details
          </label>
          <textarea
            id="message"
            name="message"
            rows={4}
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Please provide any additional information about your roofing needs..."
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full py-3 px-6 text-white bg-primary rounded-md hover:bg-primary-dark transition-colors
            ${isSubmitting ? 'opacity-75 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Request'}
        </button>
      </form>
    </div>
  );
};

export default ConsultationForm;