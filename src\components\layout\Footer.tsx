import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram, MapPin, Phone, Mail } from 'lucide-react';
import Logo from '../Logo';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <div className="mb-4">
              <Logo size="lg" className="text-white" />
            </div>
            <p className="text-gray-400 mb-4">
              Professional roofing services for residential and commercial properties.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com" className="hover:text-primary-light">
                <Facebook size={24} />
              </a>
              <a href="https://twitter.com" className="hover:text-primary-light">
                <Twitter size={24} />
              </a>
              <a href="https://instagram.com" className="hover:text-primary-light">
                <Instagram size={24} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/" className="hover:text-primary-light">Home</Link></li>
              <li><Link to="/about" className="hover:text-primary-light">About Us</Link></li>
              <li><Link to="/residential-roofing" className="hover:text-primary-light">Residential Roofing</Link></li>
              <li><Link to="/commercial-roofing" className="hover:text-primary-light">Commercial Roofing</Link></li>
              <li><Link to="/financing" className="hover:text-primary-light">Financing</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-bold mb-4">Our Services</h3>
            <ul className="space-y-2">
              <li><Link to="/roof-repair" className="hover:text-primary-light">Roof Repair</Link></li>
              <li><Link to="/residential-roofing" className="hover:text-primary-light">Roof Installation</Link></li>
              <li><Link to="/residential-roofing" className="hover:text-primary-light">Roof Inspection</Link></li>
              <li><Link to="/commercial-roofing" className="hover:text-primary-light">Commercial Roofing</Link></li>
              <li><Link to="/contact" className="hover:text-primary-light">Free Estimate</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="mr-2 mt-1" size={20} />
                <span>Florida's Premier Roofing Company</span>
              </li>
              <li className="flex items-center">
                <Phone className="mr-2" size={20} />
                <a href="tel:+13053761808" className="hover:text-primary-light">************</a>
              </li>
              <li className="flex items-center">
                <Mail className="mr-2" size={20} />
                <a href="mailto:<EMAIL>" className="hover:text-primary-light"><EMAIL></a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-6 mt-6">
          <div className="text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Roofers LLC. All rights reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;