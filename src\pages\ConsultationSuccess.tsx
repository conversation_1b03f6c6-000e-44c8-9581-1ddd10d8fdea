import { FC } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { CheckCircle } from 'lucide-react';

const ConsultationSuccess: FC = () => {
  const location = useLocation();
  const { serviceType, email } = location.state || {};

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="w-16 h-16 text-green-500" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Thank You for Your Request!
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            We've received your consultation request for {serviceType || 'our roofing services'}.
          </p>
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">What happens next?</h2>
            <div className="space-y-4 text-left">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                  1
                </div>
                <p className="text-gray-600">
                  Our team will review your request and prepare a customized consultation plan.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                  2
                </div>
                <p className="text-gray-600">
                  Within the next 24 hours, one of our roofing experts will contact you at the provided email ({email}) to schedule your consultation.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white font-bold">
                  3
                </div>
                <p className="text-gray-600">
                  During the consultation, we'll assess your needs, provide expert recommendations, and give you a detailed quote.
                </p>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <p className="text-gray-600">
              If you need immediate assistance, please don't hesitate to call us:
            </p>
            <a
              href="tel:+13053761808"
              className="btn-primary inline-block py-3 px-6 rounded-md hover:shadow-lg transition-shadow"
            >
              (*************
            </a>
          </div>
          <div className="mt-8 pt-8 border-t">
            <Link
              to="/"
              className="text-primary hover:text-primary-dark font-medium transition-colors"
            >
              Return to Homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsultationSuccess;