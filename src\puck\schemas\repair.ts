import React from 'react';
import { type Config } from '@measured/puck';
import { Link } from 'react-router-dom';
import { sharedFields, defaultVerticalPadding } from './shared';

interface HeroProps {
  title?: string;
  subtitle?: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface ServicesProps {
  title?: string;
  services?: Array<{
    title: string;
    description: string;
    image: string;
  }>;
  verticalPadding?: string;
}

interface RepairTypeProps {
  name: string;
  description: string;
  features: Array<{ text: string }>;
  image: string;
}

interface RepairTypesProps {
  title?: string;
  repairTypes?: Array<RepairTypeProps>;
  verticalPadding?: string;
}

interface CTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  verticalPadding?: string;
}

export const repairConfig: Config = {
  components: {
    RepairHero: {
      render: ({
        title = 'Expert Roof Repair Services by Roofers LLC',
        subtitle = 'Fast, reliable roof repair solutions for all roofing types',
        buttonText = 'Get Free Estimate',
        buttonLink = '/request-estimate',
        backgroundImage = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-primary text-white bg-cover bg-center ${verticalPadding}`,
          style: {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("${backgroundImage}")`
          }
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('div', {
              key: 'content',
              className: 'max-w-2xl'
            }, [
              React.createElement('h1', {
                key: 'title',
                className: 'text-4xl md:text-5xl font-bold mb-6'
              }, title),
              React.createElement('p', {
                key: 'subtitle',
                className: 'text-xl mb-8'
              }, subtitle),
              React.createElement(Link, {
                key: 'button',
                to: buttonLink,
                className: 'bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-block'
              }, buttonText)
            ])
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'text' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' },
        backgroundImage: { type: 'text' }
      }
    },

    RepairServices: {
      render: ({
        title = 'Our Repair Services',
        services = [],
        verticalPadding = defaultVerticalPadding
      }: ServicesProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold text-center mb-12'
            }, title),
            React.createElement('div', {
              key: 'grid',
              className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            }, services.map((service, index) => 
              React.createElement('div', {
                key: index,
                className: 'bg-white rounded-lg shadow-lg overflow-hidden'
              }, [
                React.createElement('img', {
                  key: 'image',
                  src: service.image,
                  alt: `${service.title} by Roofers LLC in Florida`,
                  className: 'w-full h-48 object-cover'
                }),
                React.createElement('div', {
                  key: 'content',
                  className: 'p-6'
                }, [
                  React.createElement('h3', {
                    key: 'title',
                    className: 'text-xl font-bold mb-2'
                  }, service.title),
                  React.createElement('p', {
                    key: 'description',
                    className: 'text-gray-600'
                  }, service.description)
                ])
              ])
            ))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        services: {
          type: 'array',
          arrayFields: {
            title: { type: 'text' },
            description: { type: 'textarea' },
            image: { type: 'text' }
          }
        }
      }
    },

    RepairTypes: {
      render: ({
        title = 'Common Repair Types',
        repairTypes = [],
        verticalPadding = defaultVerticalPadding
      }: RepairTypesProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold text-center mb-12'
            }, title),
            React.createElement('div', {
              key: 'grid',
              className: 'grid grid-cols-1 md:grid-cols-3 gap-8'
            }, repairTypes.map((type, index) => 
              React.createElement('div', {
                key: index,
                className: 'bg-white rounded-lg shadow-lg overflow-hidden'
              }, [
                React.createElement('img', {
                  key: 'image',
                  src: type.image,
                  alt: `${type.name} by Roofers LLC`,
                  className: 'w-full h-48 object-cover'
                }),
                React.createElement('div', {
                  key: 'content',
                  className: 'p-6'
                }, [
                  React.createElement('h3', {
                    key: 'name',
                    className: 'text-xl font-bold mb-2'
                  }, type.name),
                  React.createElement('p', {
                    key: 'description',
                    className: 'text-gray-600 mb-4'
                  }, type.description),
                  React.createElement('ul', {
                    key: 'features',
                    className: 'space-y-2'
                  }, type.features.map((feature, i) => 
                    React.createElement('li', {
                      key: i,
                      className: 'flex items-center text-gray-600'
                    }, [
                      React.createElement('svg', {
                        key: 'icon',
                        className: 'w-4 h-4 mr-2 text-primary',
                        viewBox: '0 0 20 20',
                        fill: 'currentColor'
                      }, [
                        React.createElement('path', {
                          key: 'path',
                          fillRule: 'evenodd',
                          d: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z',
                          clipRule: 'evenodd'
                        })
                      ]),
                      feature.text
                    ])
                  ))
                ])
              ])
            ))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        repairTypes: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            description: { type: 'textarea' },
            features: {
              type: 'array',
              arrayFields: {
                text: { type: 'text' }
              }
            },
            image: { type: 'text' }
          }
        }
      }
    },

    RepairCTA: {
      render: ({
        title = 'Need Roof Repairs?',
        description = 'Contact Roofers LLC today for fast, professional roof repair services.',
        buttonText = 'Request Service',
        buttonLink = '/request-estimate',
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `bg-primary ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4 text-center text-white'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold mb-6'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-xl mb-8'
            }, description),
            React.createElement(Link, {
              key: 'button',
              to: buttonLink,
              className: 'bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-block'
            }, buttonText)
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' }
      }
    }
  }
};