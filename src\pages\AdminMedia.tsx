import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { api } from '@/lib/api';
import { 
  Upload, 
  Image, 
  FileText, 
  Video, 
  Download,
  Trash2,
  Search,
  Filter,
  Grid3X3,
  List,
  Eye,
  Edit,
  Copy,
  FolderOpen,
  Sparkles
} from 'lucide-react';

interface MediaFile {
  id: string;
  filename: string;
  original_filename: string;
  file_url: string;
  file_type: 'image' | 'video' | 'document' | 'other';
  file_size: number;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  upload_date: string;
  uploaded_by_name: string;
  categories?: string;
  tags?: string;
}

interface MediaStats {
  total_files: number;
  total_size: number;
  image_count: number;
  video_count: number;
  document_count: number;
  other_count: number;
}

export default function AdminMedia() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [mediaStats, setMediaStats] = useState<MediaStats>({
    total_files: 0,
    total_size: 0,
    image_count: 0,
    video_count: 0,
    document_count: 0,
    other_count: 0
  });
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingFile, setEditingFile] = useState<MediaFile | null>(null);
  const [fileMetadata, setFileMetadata] = useState({
    alt_text: '',
    caption: '',
    description: '',
    tags: ''
  });
  const [showAIGenerator, setShowAIGenerator] = useState(false);
  const [aiPrompt, setAiPrompt] = useState('');
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Load media files and statistics
  const loadMediaData = async () => {
    try {
      setLoading(true);

      // Load media files
      const filesResponse = await api.media.getFiles();
      if (filesResponse.success) {
        setMediaFiles(filesResponse.data || []);
      }

      // Load media statistics
      const statsResponse = await api.media.getStatistics();
      if (statsResponse.success) {
        setMediaStats(statsResponse.data || mediaStats);
      }
    } catch (error) {
      console.error('Error loading media data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadMediaData();
  }, []);

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="h-5 w-5 text-blue-600" />;
      case 'video': return <Video className="h-5 w-5 text-purple-600" />;
      case 'document': return <FileText className="h-5 w-5 text-green-600" />;
      default: return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'bg-blue-100 text-blue-700 border-blue-300';
      case 'video': return 'bg-purple-100 text-purple-700 border-purple-300';
      case 'document': return 'bg-green-100 text-green-700 border-green-300';
      default: return 'bg-gray-100 text-gray-700 border-gray-300';
    }
  };

  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const handleFileUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = 'image/*,video/*,.pdf,.doc,.docx,.txt';

    input.onchange = async (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;

      setUploading(true);
      setUploadProgress(0);

      try {
        // Create FormData with all files
        const formData = new FormData();
        Array.from(files).forEach((file, index) => {
          formData.append(`files`, file);
        });

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 200);

        // Upload files using API
        const response = await api.media.uploadFile(formData);

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (response.success) {
          // Reload media data to show new files
          await loadMediaData();
          alert(`Successfully uploaded ${files.length} file(s)`);
        } else {
          throw new Error(response.error || 'Upload failed');
        }

      } catch (error) {
        console.error('Upload error:', error);
        alert('Upload failed. Please try again.');
      } finally {
        setUploading(false);
        setUploadProgress(0);
      }
    };

    input.click();
  };

  const handleEditFile = (file: MediaFile) => {
    setEditingFile(file);
    setFileMetadata({
      alt_text: file.alt_text || '',
      caption: file.caption || '',
      description: file.description || '',
      tags: file.tags || ''
    });
    setShowEditModal(true);
  };

  const handleSaveFileMetadata = async () => {
    if (!editingFile) return;

    try {
      const response = await api.media.updateFile(editingFile.id, fileMetadata);
      if (response.success) {
        await loadMediaData();
        setShowEditModal(false);
        alert('File metadata updated successfully');
      }
    } catch (error) {
      console.error('Error updating file metadata:', error);
      alert('Failed to update file metadata');
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    if (window.confirm('Are you sure you want to delete this file?')) {
      try {
        const response = await api.media.deleteFile(fileId);
        if (response.success) {
          await loadMediaData();
          setSelectedFiles(prev => prev.filter(id => id !== fileId));
          alert('File deleted successfully');
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        alert('Failed to delete file');
      }
    }
  };

  const handleBulkDelete = async () => {
    if (selectedFiles.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedFiles.length} files?`)) {
      try {
        for (const fileId of selectedFiles) {
          await api.media.deleteFile(fileId);
        }
        await loadMediaData();
        setSelectedFiles([]);
        alert(`${selectedFiles.length} files deleted successfully`);
      } catch (error) {
        console.error('Error deleting files:', error);
        alert('Failed to delete some files');
      }
    }
  };

  const handleCopyUrls = () => {
    const urls = selectedFiles
      .map(fileId => {
        const file = mediaFiles.find(f => f.id === fileId);
        return file ? file.file_url : '';
      })
      .filter(url => url)
      .join('\n');

    navigator.clipboard.writeText(urls);
    alert('File URLs copied to clipboard');
  };

  const handleDownloadFiles = () => {
    selectedFiles.forEach(fileId => {
      const file = mediaFiles.find(f => f.id === fileId);
      if (file) {
        const link = document.createElement('a');
        link.href = file.file_url;
        link.download = file.original_filename || file.filename;
        link.click();
      }
    });
  };

  const roofingPrompts = [
    'Modern residential roof installation with shingles',
    'Commercial flat roof with TPO membrane',
    'Metal roofing installation on industrial building',
    'Emergency roof repair during storm',
    'Professional roofer working on steep roof',
    'Before and after roof replacement',
    'Solar panels on residential roof',
    'Roof inspection with drone technology'
  ];

  const handleGenerateAIImages = async () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    try {
      // Mock AI image generation - in real app, this would call OpenAI DALL-E or DeepSeek API
      const mockImages = [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
        'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=300&fit=crop',
        'https://images.unsplash.com/photo-1590736969955-71cc94901144?w=400&h=300&fit=crop'
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      setGeneratedImages(mockImages);
    } catch (error) {
      console.error('Error generating AI images:', error);
      alert('Failed to generate AI images');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSelectAIImage = async (imageUrl: string) => {
    try {
      // In a real app, this would download the image and upload it to the media library
      const newFile = {
        id: Date.now().toString(),
        filename: `ai-generated-${Date.now()}.jpg`,
        original_filename: `ai-generated-${aiPrompt.slice(0, 20)}.jpg`,
        file_url: imageUrl,
        file_type: 'image/jpeg',
        file_size: 245760, // Mock size
        alt_text: aiPrompt,
        caption: `AI Generated: ${aiPrompt}`,
        description: `Generated using AI with prompt: ${aiPrompt}`,
        tags: 'ai-generated,roofing',
        uploaded_at: new Date().toISOString(),
        uploaded_by: 'Current User'
      };

      // Add to media files
      setMediaFiles(prev => [newFile, ...prev]);

      // Close AI generator
      setShowAIGenerator(false);
      setAiPrompt('');
      setGeneratedImages([]);

      alert('AI generated image added to media library successfully!');
    } catch (error) {
      console.error('Error adding AI image to library:', error);
      alert('Failed to add AI image to library');
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <AdminPageHeader
        title="Media Library"
        description="Manage your images, documents, and media files"
      >
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleFileUpload}
            disabled={uploading}
            className="flex items-center space-x-2"
          >
            <Upload className="h-4 w-4" />
            <span>{uploading ? `Uploading... ${uploadProgress}%` : 'Upload Files'}</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowAIGenerator(true)}
            className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-none hover:from-purple-600 hover:to-pink-600 flex items-center space-x-2"
          >
            <Sparkles className="h-4 w-4" />
            <span>Generate AI Image</span>
          </Button>
        </div>
      </AdminPageHeader>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Files</p>
                <p className="text-2xl font-bold">{loading ? '...' : mediaStats.total_files}</p>
              </div>
              <FolderOpen className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Images</p>
                <p className="text-2xl font-bold">{loading ? '...' : mediaStats.image_count}</p>
              </div>
              <Image className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Documents</p>
                <p className="text-2xl font-bold">{loading ? '...' : mediaStats.document_count}</p>
              </div>
              <FileText className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Videos</p>
                <p className="text-2xl font-bold">{loading ? '...' : mediaStats.video_count}</p>
              </div>
              <Video className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search files..."
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Files Actions */}
      {selectedFiles.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-blue-900">
                {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected
              </p>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="outline" onClick={handleDownloadFiles}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button size="sm" variant="outline" onClick={handleCopyUrls}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy URLs
                </Button>
                <Button size="sm" variant="destructive" onClick={handleBulkDelete}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Grid/List */}
      <Card>
        <CardHeader>
          <CardTitle>Files</CardTitle>
          <CardDescription>
            Click on files to select them, or use the actions to manage your media
          </CardDescription>
        </CardHeader>
        <CardContent>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {mediaFiles.map((file) => (
                <div
                  key={file.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                    selectedFiles.includes(file.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => toggleFileSelection(file.id)}
                >
                  <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                    {file.file_type === 'image' ? (
                      <img src={file.file_url} alt={file.alt_text || file.original_filename} className="w-full h-full object-cover rounded-lg" />
                    ) : (
                      getFileIcon(file.file_type)
                    )}
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium truncate">{file.original_filename || file.filename}</p>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className={getFileTypeColor(file.file_type)}>
                        {file.file_type}
                      </Badge>
                      <span className="text-xs text-gray-500">{formatFileSize(file.file_size)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(file.file_url, '_blank');
                        }}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditFile(file);
                        }}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          const link = document.createElement('a');
                          link.href = file.file_url;
                          link.download = file.original_filename || file.filename;
                          link.click();
                        }}
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {mediaFiles.map((file) => (
                <div
                  key={file.id}
                  className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all hover:shadow-sm ${
                    selectedFiles.includes(file.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => toggleFileSelection(file.id)}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      {file.file_type === 'image' ? (
                        <img src={file.file_url} alt={file.alt_text || file.original_filename} className="w-full h-full object-cover rounded-lg" />
                      ) : (
                        getFileIcon(file.file_type)
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium">{file.original_filename || file.filename}</p>
                      <p className="text-xs text-gray-500">
                        {file.width && file.height && `${file.width}x${file.height} • `}{formatFileSize(file.file_size)} • {new Date(file.upload_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={getFileTypeColor(file.file_type)}>
                      {file.file_type}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(file.file_url, '_blank');
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditFile(file);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 px-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          const link = document.createElement('a');
                          link.href = file.file_url;
                          link.download = file.original_filename || file.filename;
                          link.click();
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit File Modal */}
      {showEditModal && editingFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Edit File Metadata</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Alt Text</label>
                <input
                  type="text"
                  value={fileMetadata.alt_text}
                  onChange={(e) => setFileMetadata({ ...fileMetadata, alt_text: e.target.value })}
                  placeholder="Describe the image for accessibility"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Caption</label>
                <input
                  type="text"
                  value={fileMetadata.caption}
                  onChange={(e) => setFileMetadata({ ...fileMetadata, caption: e.target.value })}
                  placeholder="Short caption for the file"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={fileMetadata.description}
                  onChange={(e) => setFileMetadata({ ...fileMetadata, description: e.target.value })}
                  placeholder="Detailed description of the file"
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tags</label>
                <input
                  type="text"
                  value={fileMetadata.tags}
                  onChange={(e) => setFileMetadata({ ...fileMetadata, tags: e.target.value })}
                  placeholder="Comma-separated tags"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSaveFileMetadata}>
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* AI Image Generation Modal */}
      {showAIGenerator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center space-x-2 mb-4">
              <Sparkles className="h-6 w-6 text-purple-600" />
              <h3 className="text-lg font-semibold">Generate AI Image</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Describe the image you want to generate
                </label>
                <textarea
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  placeholder="e.g., Modern residential roof installation with shingles, professional roofer working..."
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Quick suggestions:</p>
                <div className="flex flex-wrap gap-2">
                  {roofingPrompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => setAiPrompt(prompt)}
                      className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors"
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </div>

              {generatedImages.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-3">Generated images (click to select):</p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {generatedImages.map((imageUrl, index) => (
                      <div
                        key={index}
                        className="relative cursor-pointer group"
                        onClick={() => handleSelectAIImage(imageUrl)}
                      >
                        <img
                          src={imageUrl}
                          alt={`Generated option ${index + 1}`}
                          className="w-full h-48 object-cover rounded-lg border-2 border-gray-200 group-hover:border-purple-500 transition-colors"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                          <span className="text-white font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                            Select this image
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowAIGenerator(false);
                    setAiPrompt('');
                    setGeneratedImages([]);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleGenerateAIImages}
                  disabled={isGenerating || !aiPrompt.trim()}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {isGenerating ? (
                    <>
                      <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Images
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
