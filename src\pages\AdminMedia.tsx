import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { 
  Upload, 
  Image, 
  FileText, 
  Video, 
  Download,
  Trash2,
  Search,
  Filter,
  Grid3X3,
  List,
  Eye,
  Edit,
  Copy,
  FolderOpen
} from 'lucide-react';

export default function AdminMedia() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Mock media data - in a real app, this would come from an API
  const mediaFiles = [
    {
      id: '1',
      name: 'hero-roofing-1.jpg',
      type: 'image',
      size: '2.4 MB',
      dimensions: '1920x1080',
      uploadDate: '2024-12-01',
      url: '/images/hero-roofing-1.jpg',
      thumbnail: '/images/thumbnails/hero-roofing-1.jpg'
    },
    {
      id: '2',
      name: 'commercial-project-1.jpg',
      type: 'image',
      size: '1.8 MB',
      dimensions: '1600x900',
      uploadDate: '2024-12-01',
      url: '/images/commercial-project-1.jpg',
      thumbnail: '/images/thumbnails/commercial-project-1.jpg'
    },
    {
      id: '3',
      name: 'roofing-guide.pdf',
      type: 'document',
      size: '3.2 MB',
      uploadDate: '2024-11-28',
      url: '/documents/roofing-guide.pdf'
    },
    {
      id: '4',
      name: 'installation-video.mp4',
      type: 'video',
      size: '45.6 MB',
      duration: '5:32',
      uploadDate: '2024-11-25',
      url: '/videos/installation-video.mp4',
      thumbnail: '/images/thumbnails/installation-video.jpg'
    },
    {
      id: '5',
      name: 'residential-before-after.jpg',
      type: 'image',
      size: '2.1 MB',
      dimensions: '1800x1200',
      uploadDate: '2024-11-20',
      url: '/images/residential-before-after.jpg',
      thumbnail: '/images/thumbnails/residential-before-after.jpg'
    },
    {
      id: '6',
      name: 'company-logo.svg',
      type: 'image',
      size: '24 KB',
      uploadDate: '2024-11-15',
      url: '/images/company-logo.svg'
    }
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="h-5 w-5 text-blue-600" />;
      case 'video': return <Video className="h-5 w-5 text-purple-600" />;
      case 'document': return <FileText className="h-5 w-5 text-green-600" />;
      default: return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'bg-blue-100 text-blue-700 border-blue-300';
      case 'video': return 'bg-purple-100 text-purple-700 border-purple-300';
      case 'document': return 'bg-green-100 text-green-700 border-green-300';
      default: return 'bg-gray-100 text-gray-700 border-gray-300';
    }
  };

  const toggleFileSelection = (fileId: string) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const handleFileUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = 'image/*,video/*,.pdf,.doc,.docx,.txt';

    input.onchange = async (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;

      setUploading(true);
      setUploadProgress(0);

      try {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];

          // Simulate upload progress
          for (let progress = 0; progress <= 100; progress += 10) {
            setUploadProgress(progress);
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // In a real app, you would upload to your server or cloud storage
          console.log('Uploading file:', file.name, 'Size:', file.size, 'Type:', file.type);

          // Here you would typically:
          // 1. Create FormData with the file
          // 2. Send POST request to your upload endpoint
          // 3. Handle the response and update the media files list
          // 4. Show success/error messages

          // Example API call structure:
          // const formData = new FormData();
          // formData.append('file', file);
          // const response = await fetch('/api/media/upload', {
          //   method: 'POST',
          //   body: formData
          // });
          // const result = await response.json();
        }

        // Show success message
        alert(`Successfully uploaded ${files.length} file(s)`);

      } catch (error) {
        console.error('Upload error:', error);
        alert('Upload failed. Please try again.');
      } finally {
        setUploading(false);
        setUploadProgress(0);
      }
    };

    input.click();
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <AdminPageHeader
        title="Media Library"
        description="Manage your images, documents, and media files"
      >
        <Button
          onClick={handleFileUpload}
          disabled={uploading}
          className="flex items-center space-x-2"
        >
          <Upload className="h-4 w-4" />
          <span>{uploading ? `Uploading... ${uploadProgress}%` : 'Upload Files'}</span>
        </Button>
      </AdminPageHeader>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Files</p>
                <p className="text-2xl font-bold">{mediaFiles.length}</p>
              </div>
              <FolderOpen className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Images</p>
                <p className="text-2xl font-bold">{mediaFiles.filter(f => f.type === 'image').length}</p>
              </div>
              <Image className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Documents</p>
                <p className="text-2xl font-bold">{mediaFiles.filter(f => f.type === 'document').length}</p>
              </div>
              <FileText className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Videos</p>
                <p className="text-2xl font-bold">{mediaFiles.filter(f => f.type === 'video').length}</p>
              </div>
              <Video className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search files..."
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Files Actions */}
      {selectedFiles.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-blue-900">
                {selectedFiles.length} file{selectedFiles.length > 1 ? 's' : ''} selected
              </p>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button size="sm" variant="outline">
                  <Copy className="h-4 w-4 mr-2" />
                  Copy URLs
                </Button>
                <Button size="sm" variant="destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Grid/List */}
      <Card>
        <CardHeader>
          <CardTitle>Files</CardTitle>
          <CardDescription>
            Click on files to select them, or use the actions to manage your media
          </CardDescription>
        </CardHeader>
        <CardContent>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {mediaFiles.map((file) => (
                <div
                  key={file.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                    selectedFiles.includes(file.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => toggleFileSelection(file.id)}
                >
                  <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                    {file.thumbnail ? (
                      <img src={file.thumbnail} alt={file.name} className="w-full h-full object-cover rounded-lg" />
                    ) : (
                      getFileIcon(file.type)
                    )}
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className={getFileTypeColor(file.type)}>
                        {file.type}
                      </Badge>
                      <span className="text-xs text-gray-500">{file.size}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button size="sm" variant="ghost" className="h-6 px-2">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="ghost" className="h-6 px-2">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="ghost" className="h-6 px-2">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {mediaFiles.map((file) => (
                <div
                  key={file.id}
                  className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-all hover:shadow-sm ${
                    selectedFiles.includes(file.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => toggleFileSelection(file.id)}
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      {file.thumbnail ? (
                        <img src={file.thumbnail} alt={file.name} className="w-full h-full object-cover rounded-lg" />
                      ) : (
                        getFileIcon(file.type)
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-gray-500">
                        {file.dimensions && `${file.dimensions} • `}{file.size} • {file.uploadDate}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={getFileTypeColor(file.type)}>
                      {file.type}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Button size="sm" variant="ghost" className="h-8 px-2">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="h-8 px-2">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="h-8 px-2">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
