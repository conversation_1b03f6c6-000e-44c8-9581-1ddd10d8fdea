# System Patterns

## Environment Configuration Pattern

```mermaid
graph TD
    A[Environment Files] --> B[Environment Config]
    B --> C[API Client]
    B --> D[Server Manager]
    
    E[.env Files] --> A
    F[Validation] --> B
    G[Port Management] --> D
```

### Configuration Loading
1. Load environment variables based on mode (development/production)
2. Validate required variables
3. Create typed configuration object
4. Export singleton for app-wide use

### URL Management Pattern
```typescript
interface EnvironmentConfig {
  appUrl: string;
  apiBaseUrl: string;
  authCallbackUrl: string;
}

// Configuration singleton
export const config: EnvironmentConfig = {
  appUrl: import.meta.env.VITE_APP_URL,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  authCallbackUrl: import.meta.env.VITE_AUTH_CALLBACK_URL,
};
```

## API Client Pattern

```mermaid
graph LR
    A[API Request] --> B[API Client]
    B --> C[Environment Config]
    B --> D[Error Handling]
    B --> E[Response Processing]
```

### Request Pattern
1. Use environment-based URLs
2. Include proper headers
3. Handle errors consistently
4. Parse responses

## Server Management Pattern

```mermaid
graph TD
    A[Start Server] --> B{Port Available?}
    B -->|Yes| C[Start on Port]
    B -->|No| D[Find Next Port]
    D --> E[Update Config]
    E --> C
```

### Port Management
1. Check port availability
2. Handle conflicts
3. Auto-select alternative ports
4. Update configuration

## CORS Configuration Pattern

```mermaid
graph TD
    A[Environment Variables] --> B[CORS Config]
    B --> C[Parse Origins]
    C --> D[Apply Rules]
    D --> E[Request Handling]
```

### Origin Management
1. Parse comma-separated origins
2. Support multiple environments
3. Validate origins
4. Apply security rules

## Component Relationships

### Frontend Components
```mermaid
graph TD
    A[Environment Config] --> B[API Client]
    A --> C[Server Manager]
    B --> D[Form Components]
    B --> E[Auth Components]
```

### Backend Components
```mermaid
graph TD
    A[Environment Config] --> B[CORS Handler]
    A --> C[API Routes]
    A --> D[Database Config]
```

## Design Patterns

### Singleton Pattern
- Environment configuration
- API client instance
- Server manager

### Factory Pattern
- API request creation
- Environment configuration loading
- Server instance management

### Observer Pattern
- Server status monitoring
- Port availability checking
- Configuration changes

## Critical Implementation Paths

### Environment Setup
1. Load environment variables
2. Validate configuration
3. Initialize services
4. Start server

### API Request Flow
1. Get environment configuration
2. Build request URL
3. Execute request
4. Handle response

### Error Handling Flow
1. Detect error type
2. Log with environment context
3. Format response
4. Return to client

## Security Patterns

### Environment Validation
```typescript
const validateConfig = (config: EnvironmentConfig) => {
  // Validate URLs
  const urlFields = ['appUrl', 'apiBaseUrl', 'authCallbackUrl'];
  urlFields.forEach(field => {
    try {
      new URL(config[field]);
    } catch {
      throw new Error(`Invalid URL for ${field}`);
    }
  });
};
```

### CORS Security
```php
public function __construct() {
    // Parse and validate origins
    $origins = explode(',', $_ENV['CORS_ORIGIN'] ?? '');
    $this->allowedOrigins = array_filter(array_map('trim', $origins));
}
```

## Testing Patterns

### Configuration Testing
1. Validate environment loading
2. Check required variables
3. Test URL parsing
4. Verify security rules

### API Testing
1. Test relative paths
2. Verify environment URLs
3. Check error handling
4. Validate responses

### Integration Testing
1. Test cross-origin requests
2. Verify port management
3. Check environment switching
4. Validate security rules