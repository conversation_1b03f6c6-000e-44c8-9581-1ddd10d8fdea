import { createServer } from 'net';
import config from '../config/environment';

interface ProcessInfo {
  pid: number;
  port: number;
}

/**
 * Server Manager
 * Handles port usage and server process management
 */
export class ServerManager {
  private static processInfo: ProcessInfo | null = null;

  /**
   * Check if a port is in use
   */
  static isPortInUse(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const server = createServer()
        .once('error', () => {
          // Port is in use
          resolve(true);
        })
        .once('listening', () => {
          // Port is free
          server.close();
          resolve(false);
        })
        .listen(port);
    });
  }

  /**
   * Find an available port starting from the given port
   */
  static async findAvailablePort(startPort: number): Promise<number> {
    let port = startPort;
    while (await this.isPortInUse(port)) {
      port++;
      // Prevent infinite loop
      if (port > startPort + 100) {
        throw new Error('Could not find an available port');
      }
    }
    return port;
  }

  /**
   * Start the server on a specific port
   */
  static async startServer(port: number = config.port): Promise<void> {
    if (this.processInfo) {
      console.log(`Server already running on port ${this.processInfo.port}`);
      return;
    }

    // Check if port is available
    const isInUse = await this.isPortInUse(port);
    if (isInUse) {
      const newPort = await this.findAvailablePort(port + 1);
      console.log(`Port ${port} is in use. Using port ${newPort} instead.`);
      port = newPort;
    }

    try {
      // Set environment port
      process.env.PORT = port.toString();
      
      // Store process information
      this.processInfo = {
        pid: process.pid,
        port,
      };

      console.log(`Server started on port ${port}`);
    } catch (error) {
      console.error('Failed to start server:', error);
      throw error;
    }
  }

  /**
   * Stop the server
   */
  static async stopServer(): Promise<void> {
    if (!this.processInfo) {
      console.log('No server running');
      return;
    }

    try {
      // Clean up process information
      this.processInfo = null;
      console.log('Server stopped successfully');
    } catch (error) {
      console.error('Failed to stop server:', error);
      throw error;
    }
  }

  /**
   * Restart the server
   */
  static async restartServer(): Promise<void> {
    if (this.processInfo) {
      await this.stopServer();
    }
    await this.startServer(config.port);
  }

  /**
   * Get current server status
   */
  static getStatus(): { running: boolean; port?: number; pid?: number } {
    if (!this.processInfo) {
      return { running: false };
    }
    return {
      running: true,
      port: this.processInfo.port,
      pid: this.processInfo.pid,
    };
  }
}

export default ServerManager;
