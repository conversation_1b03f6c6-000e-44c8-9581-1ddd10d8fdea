import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Data } from '@measured/puck';
import { financingConfig } from '@/puck/schemas/financing';
import { financingImages } from '@/lib/image-paths';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface FinancingData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const financingDefaultData: FinancingData = {
  root: {
    title: 'Financing Options',
    description: 'Affordable financing solutions for your roofing project'
  },
    content: [
      {
        type: 'FinancingHero',
        props: {
          title: 'Make Your Roofing Project More Affordable',
          description: "With over 30 years of experience, we have helped thousands of homeowners finance their roofing projects",
          backgroundImage: financingImages.hero,
          verticalPadding: 'py-20'
        }
      },
      {
        type: 'FinancingOptions',
        props: {
          title: 'Our Financing Options',
          options: [
            {
              title: "Fixed Monthly Payments",
              description: "Predictable monthly payments with competitive interest rates",
              features: [
                { text: "Fixed interest rates" },
                { text: "Terms up to 12 years" },
                { text: "Quick approval process" },
                { text: "No prepayment penalties" }
              ],
              image: financingImages.fixed
            },
            {
              title: "Same as Cash",
              description: "No interest if paid in full within promotional period",
              features: [
                { text: "0% interest option" },
                { text: "6-18 month terms" },
                { text: "Flexible payment options" },
                { text: "Easy qualification" }
              ],
              image: financingImages.sameAsCash
            },
            {
              title: "Low-Interest Financing",
              description: "Affordable long-term financing solutions",
              features: [
                { text: "Competitive rates" },
                { text: "Longer terms available" },
                { text: "Low monthly payments" },
                { text: "Multiple term options" }
              ],
              image: financingImages.lowInterest
            }
          ],
          verticalPadding: 'py-16'
        }
      },
      {
        type: 'FinancingBenefits',
        props: {
          title: 'Why Choose Our Financing?',
          description: 'We partner with trusted financial institutions to provide hassle-free financing solutions tailored to your needs.',
          benefits: [
            {
              icon: '⚡',
              title: 'Simple Process',
              description: 'Easy application with minimal paperwork'
            },
            {
              icon: '💳',
              title: 'Flexible Terms',
              description: 'Multiple financing options to choose from'
            },
            {
              icon: '📊',
              title: 'Competitive Rates',
              description: 'Access to favorable interest rates'
            },
            {
              icon: '🤝',
              title: 'Dedicated Support',
              description: 'Guidance throughout the process'
            }
          ],
          verticalPadding: 'py-16'
        }
      },
      {
        type: 'FinancingFormSection',
        props: {
          title: 'Get Started Today',
          description: 'Complete the simple form below and our financing team will contact you to discuss your options within 24-48 hours.',
          verticalPadding: 'py-16'
        }
      }
    ]
};

const Financing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<FinancingData>(financingDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={financingConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as FinancingData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={financingConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default Financing;
