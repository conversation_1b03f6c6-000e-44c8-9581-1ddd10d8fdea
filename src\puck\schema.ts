import { type Config } from '@measured/puck';
import { homeConfig } from './schemas/home';
import { residentialConfig } from './schemas/residential';
import { commercialConfig } from './schemas/commercial';
import { emergencyConfig } from './schemas/emergency';

// Combine all schemas by spreading their components
export const config: Config = {
  components: {
    ...homeConfig.components,
    ...residentialConfig.components,
    ...commercialConfig.components,
    ...emergencyConfig.components
  }
};