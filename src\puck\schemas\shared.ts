import { type Fields } from '@measured/puck';

export const defaultVerticalPadding = 'py-16';

export const sharedFields: Record<string, Fields[string]> = {
  verticalPadding: {
    type: 'select',
    options: [
      { label: 'None', value: 'py-0' },
      { label: 'Small', value: 'py-8' },
      { label: 'Medium', value: defaultVerticalPadding },
      { label: 'Large', value: 'py-20' },
      { label: 'Extra Large', value: 'py-24' }
    ]
  }
};