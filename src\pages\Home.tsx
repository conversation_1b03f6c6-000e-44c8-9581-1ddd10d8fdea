import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shield, Star, Award, CheckCircle } from 'lucide-react';
import { Puck, Render, Data } from '@measured/puck';
import { homeConfig } from '@/puck/schemas/home';
import ReviewCard from '@/components/ui/ReviewCard';
import ImageSlider from '@/components/ui/ImageSlider';

// Define the data type for this page
type HomeData = Data;

interface Review {
  name: string;
  rating: number;
  review: string;
  date: string;
  location: string;
}

// Gallery Images
const galleryImages = [
  {
    url: 'https://images.unsplash.com/photo-1632778144849-611a9353439c?q=80&w=1920',
    alt: 'Beautiful modern house with new roof installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1620641622503-21c824c15063?q=80&w=1920',
    alt: 'Professional roofers working on a new roof'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778634162-e67c259bad89?q=80&w=1920',
    alt: 'Metal roofing with perfect installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1600585553490-76fb20a32601?q=80&w=1920',
    alt: 'Modern house exterior with premium roofing'
  },
  {
    url: 'https://images.unsplash.com/photo-1638184984605-af1f05249a56?q=80&w=1920',
    alt: 'Residential roofing project completion'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778145681-61e902bb3ba3?q=80&w=1920',
    alt: 'Commercial roofing installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778661365-bdbe47fc2336?q=80&w=1920',
    alt: 'TPO roofing system on commercial building'
  },
  {
    url: 'https://images.unsplash.com/photo-1623834776026-8da7ba9a8ce8?q=80&w=1920',
    alt: 'Emergency roof repair project'
  }
];

// Customer Reviews
const reviews: Review[] = [
  {
    name: "Michael Rodriguez",
    rating: 5,
    review: "Outstanding service from start to finish! The team was professional, punctual, and did an amazing job with our new roof. They cleaned up thoroughly after the job and the results exceeded our expectations.",
    date: "January 15, 2024",
    location: "Miami, FL"
  },
  {
    name: "Sarah Thompson",
    rating: 5,
    review: "After Hurricane Ian, we needed emergency roof repairs. They responded quickly and fixed everything perfectly. Their team explained everything clearly and made sure we were comfortable with the process.",
    date: "February 1, 2024",
    location: "Fort Lauderdale, FL"
  },
  {
    name: "David Martinez",
    rating: 5,
    review: "The best roofing company in Florida! Their attention to detail and quality of work is unmatched. They installed our metal roof and it looks fantastic. Great value for the investment.",
    date: "December 28, 2023",
    location: "Hollywood, FL"
  },
  {
    name: "Jennifer Adams",
    rating: 5,
    review: "We had our entire roof replaced and couldn't be happier with the results. The crew was efficient, professional, and kept us informed throughout the process.",
    date: "January 30, 2024",
    location: "Naples, FL"
  },
  {
    name: "Carlos Mendoza",
    rating: 5,
    review: "Excellent work on our commercial building's roof. The team was thorough and professional, completing the project ahead of schedule.",
    date: "January 5, 2024",
    location: "Cape Coral, FL"
  },
  {
    name: "Emily Chen",
    rating: 5,
    review: "Very impressed with their professionalism and quality of work. They installed our new metal roof perfectly and were always responsive to our questions.",
    date: "December 15, 2023",
    location: "Lehigh Acres, FL"
  },
  {
    name: "Robert Wilson",
    rating: 5,
    review: "Great experience with this company. They handled our TPO roof installation professionally and efficiently. Highly recommend their commercial services.",
    date: "February 3, 2024",
    location: "Sarasota, FL"
  },
  {
    name: "Maria Sanchez",
    rating: 5,
    review: "They did an amazing job replacing our old shingle roof. The team was professional, clean, and completed the work on schedule.",
    date: "January 22, 2024",
    location: "Tampa, FL"
  },
  {
    name: "James Parker",
    rating: 5,
    review: "Outstanding emergency repair service after a storm. They came out quickly and fixed the issue, preventing any water damage to our home. Their 24/7 emergency service is truly reliable.",
    date: "January 18, 2024",
    location: "West Palm Beach, FL"
  },
  {
    name: "Luis Ramirez",
    rating: 5,
    review: "Professional team that delivered exceptional quality. Our new roof looks amazing and the entire process was smooth and well-managed.",
    date: "January 25, 2024",
    location: "Hialeah, FL"
  },
  {
    name: "Amanda Thompson",
    rating: 5,
    review: "Fantastic experience with their residential roofing services. The team was knowledgeable, efficient, and maintained excellent communication throughout.",
    date: "February 2, 2024",
    location: "Weston, FL"
  },
  {
    name: "Richard Brown",
    rating: 5,
    review: "They replaced our commercial building's roof with minimal disruption to our business. Excellent workmanship and project management.",
    date: "January 10, 2024",
    location: "Sunrise, FL"
  }
];

// Export default data for Puck editor
export const homeDefaultData: HomeData = {
  root: {
    title: 'Roofers LLC - Home',
    description: 'Florida\'s most trusted roofing experts'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        backgroundImage: galleryImages[0].url,
        tagline: '#1 Trusted Roofing Company in Florida',
        title: 'Transform Your Roof, Protect Your Home',
        subtitle: "Florida's most trusted roofing experts with over 30+ years of experience. Free estimates and financing available.",
        features: [
          { icon: 'shield', text: 'Licensed & Insured' },
          { icon: 'star', text: '5-Star Service' },
          { icon: 'award', text: 'BBB A+ Rated' },
          { icon: 'check', text: '100% Satisfaction' }
        ]
      }
    },
    {
      type: 'ServicesGrid',
      props: {
        title: 'Our Services',
        description: 'Expert roofing solutions tailored to your needs, delivered with unmatched quality and professionalism.',
        services: [
          {
            image: galleryImages[0].url,
            title: 'Residential Roofing',
            description: 'Expert roofing solutions for your home, from repairs to complete replacements.',
            link: '/residential-roofing'
          },
          {
            image: galleryImages[1].url,
            title: 'Commercial Roofing',
            description: 'Comprehensive roofing services for businesses and commercial properties.',
            link: '/commercial-roofing'
          },
          {
            image: galleryImages[2].url,
            title: 'Emergency Services',
            description: '24/7 emergency roofing repairs when you need them most.',
            link: '/emergency-roofing'
          }
        ]
      }
    },
    {
      type: 'GallerySection',
      props: {
        title: 'Project Gallery',
        description: 'Some of our completed roofing projects across Florida.',
        images: galleryImages,
        imagesPerView: 4
      }
    },
    {
      type: 'ReviewsSection',
      props: {
        title: 'Customer Reviews',
        description: 'See what our satisfied customers have to say about their experience with us.',
        reviews: reviews
      }
    },
    {
      type: 'CTASection',
      props: {
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        title: 'Ready to Get Started?',
        description: 'Contact Roofers LLC today for a free estimate on your roofing project. Professional service and quality guaranteed.',
        primaryButtonText: 'Get Free Estimate',
        primaryButtonLink: '/contact',
        secondaryButtonText: 'Call (*************',
        secondaryButtonLink: 'tel:+13053761808'
      }
    }
  ]
};

const HomePage: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<HomeData>(homeDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={homeConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as HomeData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={homeConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default HomePage;
