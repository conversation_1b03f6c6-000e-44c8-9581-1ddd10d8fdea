import { FC } from 'react';
import { Link } from 'react-router-dom';
import { Shield, Star, Award, CheckCircle } from 'lucide-react';
import ReviewCard from '@/components/ui/ReviewCard';
import ImageSlider from '@/components/ui/ImageSlider';

interface Review {
  name: string;
  rating: number;
  review: string;
  date: string;
  location: string;
}

// Gallery Images
const galleryImages = [
  {
    url: 'https://images.unsplash.com/photo-1632778144849-611a9353439c?q=80&w=1920',
    alt: 'Beautiful modern house with new roof installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1620641622503-21c824c15063?q=80&w=1920',
    alt: 'Professional roofers working on a new roof'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778634162-e67c259bad89?q=80&w=1920',
    alt: 'Metal roofing with perfect installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1600585553490-76fb20a32601?q=80&w=1920',
    alt: 'Modern house exterior with premium roofing'
  },
  {
    url: 'https://images.unsplash.com/photo-1638184984605-af1f05249a56?q=80&w=1920',
    alt: 'Residential roofing project completion'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778145681-61e902bb3ba3?q=80&w=1920',
    alt: 'Commercial roofing installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778661365-bdbe47fc2336?q=80&w=1920',
    alt: 'TPO roofing system on commercial building'
  },
  {
    url: 'https://images.unsplash.com/photo-1623834776026-8da7ba9a8ce8?q=80&w=1920',
    alt: 'Emergency roof repair project'
  }
];

// Customer Reviews
const reviews: Review[] = [
  {
    name: "Michael Rodriguez",
    rating: 5,
    review: "Outstanding service from start to finish! The team was professional, punctual, and did an amazing job with our new roof. They cleaned up thoroughly after the job and the results exceeded our expectations.",
    date: "January 15, 2024",
    location: "Miami, FL"
  },
  {
    name: "Sarah Thompson",
    rating: 5,
    review: "After Hurricane Ian, we needed emergency roof repairs. They responded quickly and fixed everything perfectly. Their team explained everything clearly and made sure we were comfortable with the process.",
    date: "February 1, 2024",
    location: "Fort Lauderdale, FL"
  },
  {
    name: "David Martinez",
    rating: 5,
    review: "The best roofing company in Florida! Their attention to detail and quality of work is unmatched. They installed our metal roof and it looks fantastic. Great value for the investment.",
    date: "December 28, 2023",
    location: "Hollywood, FL"
  },
  {
    name: "Jennifer Adams",
    rating: 5,
    review: "We had our entire roof replaced and couldn't be happier with the results. The crew was efficient, professional, and kept us informed throughout the process.",
    date: "January 30, 2024",
    location: "Naples, FL"
  },
  {
    name: "Carlos Mendoza",
    rating: 5,
    review: "Excellent work on our commercial building's roof. The team was thorough and professional, completing the project ahead of schedule.",
    date: "January 5, 2024",
    location: "Cape Coral, FL"
  },
  {
    name: "Emily Chen",
    rating: 5,
    review: "Very impressed with their professionalism and quality of work. They installed our new metal roof perfectly and were always responsive to our questions.",
    date: "December 15, 2023",
    location: "Lehigh Acres, FL"
  },
  {
    name: "Robert Wilson",
    rating: 5,
    review: "Great experience with this company. They handled our TPO roof installation professionally and efficiently. Highly recommend their commercial services.",
    date: "February 3, 2024",
    location: "Sarasota, FL"
  },
  {
    name: "Maria Sanchez",
    rating: 5,
    review: "They did an amazing job replacing our old shingle roof. The team was professional, clean, and completed the work on schedule.",
    date: "January 22, 2024",
    location: "Tampa, FL"
  },
  {
    name: "James Parker",
    rating: 5,
    review: "Outstanding emergency repair service after a storm. They came out quickly and fixed the issue, preventing any water damage to our home. Their 24/7 emergency service is truly reliable.",
    date: "January 18, 2024",
    location: "West Palm Beach, FL"
  },
  {
    name: "Luis Ramirez",
    rating: 5,
    review: "Professional team that delivered exceptional quality. Our new roof looks amazing and the entire process was smooth and well-managed.",
    date: "January 25, 2024",
    location: "Hialeah, FL"
  },
  {
    name: "Amanda Thompson",
    rating: 5,
    review: "Fantastic experience with their residential roofing services. The team was knowledgeable, efficient, and maintained excellent communication throughout.",
    date: "February 2, 2024",
    location: "Weston, FL"
  },
  {
    name: "Richard Brown",
    rating: 5,
    review: "They replaced our commercial building's roof with minimal disruption to our business. Excellent workmanship and project management.",
    date: "January 10, 2024",
    location: "Sunrise, FL"
  }
];

const HomePage: FC = () => {
  return (
    <div>
      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center bg-cover bg-center bg-fixed py-32 overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url("${galleryImages[0].url}")`
        }}
      >
        <div className="absolute inset-0 hero-gradient animate-pulse-subtle"></div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-3xl animate-fade-in">
            <div className="mb-8 inline-block">
              <span className="bg-accent/90 text-white px-6 py-2 rounded-full text-lg font-medium backdrop-blur">
                #1 Trusted Roofing Company in Florida
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight hero-text-shadow">
              Transform Your Roof,
              <br />
              <span className="gradient-text from-accent to-primary">Protect Your Home</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed hero-text-shadow">
              Florida's most trusted roofing experts with over 30+ years of experience.
              Free estimates and financing available.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Link
                to="/contact"
                className="btn-accent text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift text-center"
              >
                Get Free Estimate
              </Link>
              <a
                href="tel:+13053761808"
                className="btn-secondary text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift flex items-center justify-center backdrop-blur"
              >
                Call (*************
              </a>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 bg-white/10 backdrop-blur rounded-xl p-6 animate-fade-in" style={{ animationDelay: '0.3s' }}>
              <div className="flex flex-col items-center text-center text-white hover-lift">
                <Shield className="w-10 h-10 text-accent mb-2" />
                <span className="font-bold">Licensed & Insured</span>
              </div>
              <div className="flex flex-col items-center text-center text-white hover-lift">
                <Star className="w-10 h-10 text-accent mb-2" />
                <span className="font-bold">5-Star Service</span>
              </div>
              <div className="flex flex-col items-center text-center text-white hover-lift">
                <Award className="w-10 h-10 text-accent mb-2" />
                <span className="font-bold">BBB A+ Rated</span>
              </div>
              <div className="flex flex-col items-center text-center text-white hover-lift">
                <CheckCircle className="w-10 h-10 text-accent mb-2" />
                <span className="font-bold">100% Satisfaction</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4">Our Services</h2>
          <p className="text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto">
            Expert roofing solutions tailored to your needs, delivered with unmatched quality and professionalism.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300 group">
              <div className="relative overflow-hidden">
                <img src={galleryImages[0].url} alt="Residential Roofing Services" className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">Residential Roofing</h3>
                <p className="text-gray-600 mb-6">Expert roofing solutions for your home, from repairs to complete replacements.</p>
                <Link to="/residential-roofing" className="text-primary font-semibold hover:text-primary-dark flex items-center group">
                  Learn More
                  <svg className="w-4 h-4 ml-2 transform transition-transform group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300 group">
              <div className="relative overflow-hidden">
                <img src={galleryImages[1].url} alt="Commercial Roofing Services" className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">Commercial Roofing</h3>
                <p className="text-gray-600 mb-6">Comprehensive roofing services for businesses and commercial properties.</p>
                <Link to="/commercial-roofing" className="text-primary font-semibold hover:text-primary-dark flex items-center group">
                  Learn More
                  <svg className="w-4 h-4 ml-2 transform transition-transform group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300 group">
              <div className="relative overflow-hidden">
                <img src={galleryImages[2].url} alt="Emergency Roofing Services" className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="p-8">
                <h3 className="text-2xl font-bold mb-4 group-hover:text-primary transition-colors">Emergency Services</h3>
                <p className="text-gray-600 mb-6">24/7 emergency roofing repairs when you need them most.</p>
                <Link to="/emergency-roofing" className="text-primary font-semibold hover:text-primary-dark flex items-center group">
                  Learn More
                  <svg className="w-4 h-4 ml-2 transform transition-transform group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4">Project Gallery</h2>
          <p className="text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto">
            Some of our completed roofing projects across Florida.
          </p>
          <div className="max-w-full">
            <ImageSlider images={galleryImages} imagesPerView={4} />
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4">Customer Reviews</h2>
          <p className="text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto">
            See what our satisfied customers have to say about their experience with us.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {reviews.map((review, index) => (
              <ReviewCard key={index} {...review} />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10 pattern-grid"></div>
        <div className="container mx-auto px-4 text-center relative">
          <h2 className="text-4xl font-bold text-white mb-6 hero-text-shadow">Ready to Get Started?</h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Contact Roofers LLC today for a free estimate on your roofing project.
            Professional service and quality guaranteed.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="btn-accent text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift"
            >
              Get Free Estimate
            </Link>
            <a
              href="tel:+13053761808"
              className="btn-white text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift"
            >
              Call (*************
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
