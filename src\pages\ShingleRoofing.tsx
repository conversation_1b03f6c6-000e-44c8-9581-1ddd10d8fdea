import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { shingleConfig } from '../puck/schemas/shingle';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface ShingleData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: ShingleData = {
  root: {
    title: 'Shingle Roofing Services',
    description: 'Expert shingle roofing solutions in Florida'
  },
  content: [
    {
      type: 'ShingleHero',
      props: {
        title: 'Shingle Roofing Solutions by Roofers LLC',
        subtitle: 'Quality shingle roofing installation and replacement services',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'ShingleServices',
      props: {
        title: 'Our Shingle Roofing Services',
        services: [
          {
            title: "New Shingle Installation",
            description: "Expert installation of high-quality shingle roofing systems",
            image: "/placeholder.svg"
          },
          {
            title: "Shingle Roof Replacement",
            description: "Complete shingle roof replacement with premium materials",
            image: "/placeholder.svg"
          },
          {
            title: "Shingle Repair & Maintenance",
            description: "Professional repair and maintenance services for shingle roofs",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ShingleTypes',
      props: {
        title: 'Shingle Roofing Options',
        shingles: [
          {
            name: "3-Tab Shingles",
            description: "Traditional, cost-effective roofing solution",
            features: [
              { text: "20-25 year lifespan" },
              { text: "Budget-friendly" },
              { text: "Classic appearance" },
              { text: "Easy maintenance" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Architectural Shingles",
            description: "Premium dimensional shingles for enhanced aesthetics",
            features: [
              { text: "30-50 year lifespan" },
              { text: "Beautiful layered look" },
              { text: "Superior durability" },
              { text: "Various color options" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Designer Shingles",
            description: "Luxury shingles that replicate slate or wood shake",
            features: [
              { text: "Lifetime warranty" },
              { text: "Premium aesthetics" },
              { text: "Maximum protection" },
              { text: "Unique designs" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ShingleCTA',
      props: {
        title: 'Ready to Upgrade Your Roof?',
        description: 'Contact Roofers LLC today for a consultation about our premium shingle roofing solutions.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

const ShingleRoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<ShingleData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={shingleConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as ShingleData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={shingleConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default ShingleRoofing;