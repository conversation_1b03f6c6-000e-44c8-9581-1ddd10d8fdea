import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, ChevronDown, Phone } from 'lucide-react';
import Logo from '../Logo';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => setIsOpen(!isOpen);

  const services = [
    { path: "/residential-roofing", name: "Residential Roofing" },
    { path: "/commercial-roofing", name: "Commercial Roofing" },
    { path: "/metal-roofing", name: "Metal Roofing" },
    { path: "/shingle-roofing", name: "Shingle Roofing" },
    { path: "/tpo-roofing", name: "TPO Roofing" },
    { path: "/emergency-roofing", name: "Emergency Services" },
    { path: "/roof-repair", name: "Roof Repair" },
  ];

  return (
    <>
      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleMenu}
        />
      )}

      <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md">
        <div className="container mx-auto px-4">
          {/* Top Bar */}
          <div className="hidden md:flex justify-end py-2 border-b">
            <a href="tel:+13053761808" className="flex items-center text-primary hover:text-primary-dark transition-colors">
              <Phone size={18} className="mr-2" />
              <span>Call Now: (*************</span>
            </a>
          </div>

          {/* Main Navigation */}
          <div className="flex justify-between items-center py-4">
            <Link to="/" className="flex items-center">
              <Logo size="lg" />
            </Link>

            {/* Mobile Menu Button */}
            <button 
              className="md:hidden z-50 relative"
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              {isOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-6">
              <Link to="/" className="nav-link">Home</Link>
              <div className="relative group">
                <button className="nav-link flex items-center">
                  Services <ChevronDown size={16} className="ml-1" />
                </button>
                <div className="absolute hidden group-hover:block w-56 bg-white shadow-lg py-2 z-50">
                  {services.map((service, index) => (
                    <Link 
                      key={index}
                      to={service.path} 
                      className="block px-4 py-2 hover:bg-gray-100 transition-colors"
                    >
                      {service.name}
                    </Link>
                  ))}
                </div>
              </div>
              <Link to="/areas-we-serve" className="nav-link">Areas We Serve</Link>
              <Link to="/gallery" className="nav-link">Gallery & Reviews</Link>
              <Link to="/about" className="nav-link">About Us</Link>
              <Link to="/financing" className="nav-link">Financing</Link>
              <Link to="/contact" className="nav-link">Contact</Link>
              <Link 
                to="/contact" 
                className="btn-primary transition-colors duration-200 hover:bg-primary-dark"
              >
                Get Free Estimate
              </Link>
            </div>

            {/* Mobile Menu */}
            <div 
              className={`
                md:hidden fixed top-0 left-0 right-0 bottom-0 bg-white z-45
                transform transition-transform duration-300 ease-in-out
                ${isOpen ? 'translate-x-0' : 'translate-x-full'}
              `}
            >
              {/* Mobile Menu Header with Call Now */}
              <div className="bg-primary text-white p-6 pt-20">
                <a 
                  href="tel:+13053761808" 
                  className="flex items-center justify-center text-lg font-medium mb-4"
                >
                  <Phone size={20} className="mr-2" />
                  Call Now: (*************
                </a>
                <Link 
                  to="/contact" 
                  className="block w-full bg-accent text-white text-center py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors"
                  onClick={toggleMenu}
                >
                  Get Free Estimate
                </Link>
              </div>

              {/* Mobile Menu Links */}
              <div className="overflow-y-auto h-[calc(100%-160px)] p-6">
                <div className="flex flex-col space-y-4">
                  <Link to="/" className="nav-link" onClick={toggleMenu}>Home</Link>
                  <div className="space-y-2">
                    <div className="font-medium">Services</div>
                    {services.map((service, index) => (
                      <Link 
                        key={index}
                        to={service.path} 
                        className="block pl-4 py-2 text-gray-600 hover:text-primary" 
                        onClick={toggleMenu}
                      >
                        {service.name}
                      </Link>
                    ))}
                  </div>
                  <Link to="/areas-we-serve" className="nav-link" onClick={toggleMenu}>Areas We Serve</Link>
                  <Link to="/gallery" className="nav-link" onClick={toggleMenu}>Gallery & Reviews</Link>
                  <Link to="/about" className="nav-link" onClick={toggleMenu}>About Us</Link>
                  <Link to="/financing" className="nav-link" onClick={toggleMenu}>Financing</Link>
                  <Link to="/contact" className="nav-link" onClick={toggleMenu}>Contact</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
