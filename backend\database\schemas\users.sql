-- User Management Database Schema
-- Roofers LLC - Enhanced User System

-- Users Table (Enhanced)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    avatar_url VARCHAR(500) NULL,
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255) NULL,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45) NULL,
    password_changed_at TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_last_login_at (last_login_at),
    INDEX idx_created_at (created_at)
);

-- User Roles Table
CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    description TEXT NULL,
    permissions JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_is_active (is_active)
);

-- User Role Assignments Table
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    assigned_by INT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE KEY unique_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    INDEX idx_assigned_by (assigned_by),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
);

-- User Sessions Table (for authentication)
CREATE TABLE IF NOT EXISTS user_auth_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User Activity Log Table
CREATE TABLE IF NOT EXISTS user_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action_type ENUM('login', 'logout', 'password_change', 'profile_update', 'role_change', 'page_edit', 'media_upload', 'other') NOT NULL,
    action_description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User Preferences Table
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    theme ENUM('light', 'dark', 'auto') DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    email_notifications BOOLEAN DEFAULT TRUE,
    dashboard_layout JSON NULL,
    editor_preferences JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert Default Roles
INSERT INTO user_roles (name, slug, description, permissions) VALUES
('Administrator', 'admin', 'Full system access with all permissions', 
 '{"pages": {"create": true, "read": true, "update": true, "delete": true}, "users": {"create": true, "read": true, "update": true, "delete": true}, "media": {"create": true, "read": true, "update": true, "delete": true}, "analytics": {"read": true}, "settings": {"read": true, "update": true}}'),

('Editor', 'editor', 'Content management and page editing permissions',
 '{"pages": {"create": true, "read": true, "update": true, "delete": false}, "users": {"create": false, "read": true, "update": false, "delete": false}, "media": {"create": true, "read": true, "update": true, "delete": true}, "analytics": {"read": true}, "settings": {"read": true, "update": false}}'),

('Viewer', 'viewer', 'Read-only access to content and analytics',
 '{"pages": {"create": false, "read": true, "update": false, "delete": false}, "users": {"create": false, "read": false, "update": false, "delete": false}, "media": {"create": false, "read": true, "update": false, "delete": false}, "analytics": {"read": true}, "settings": {"read": false, "update": false}}');

-- Insert Users (5 total: 2 Admins, 1 Editor, 2 Viewers)
-- Note: In production, passwords should be properly hashed using bcrypt or similar
INSERT INTO users (name, email, password_hash, phone, address, status, email_verified, created_at) VALUES
-- Admin Users
('John Smith', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZpjpWy', '(*************', 'Miami, Florida', 'active', TRUE, '2024-01-15 09:00:00'),
('Sarah Johnson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZpjpWy', '(*************', 'Miami, Florida', 'active', TRUE, '2024-02-01 10:30:00'),

-- Editor User
('Mike Rodriguez', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZpjpWy', '(*************', 'Miami, Florida', 'active', TRUE, '2024-03-15 14:20:00'),

-- Viewer Users
('Lisa Chen', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZpjpWy', '(*************', 'Miami, Florida', 'active', TRUE, '2024-04-10 11:45:00'),
('David Wilson', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZpjpWy', '(*************', 'Miami, Florida', 'active', TRUE, '2024-05-20 16:15:00');

-- Assign Roles to Users
INSERT INTO user_role_assignments (user_id, role_id, assigned_by) VALUES
-- Admin roles
(1, 1, 1), -- John Smith as Admin
(2, 1, 1), -- Sarah Johnson as Admin

-- Editor role
(3, 2, 1), -- Mike Rodriguez as Editor

-- Viewer roles
(4, 3, 1), -- Lisa Chen as Viewer
(5, 3, 1); -- David Wilson as Viewer

-- Insert User Preferences
INSERT INTO user_preferences (user_id, theme, language, timezone, email_notifications, dashboard_layout) VALUES
(1, 'light', 'en', 'America/New_York', TRUE, '{"sidebar": "expanded", "widgets": ["analytics", "recent_pages", "media"]}'),
(2, 'dark', 'en', 'America/New_York', TRUE, '{"sidebar": "collapsed", "widgets": ["analytics", "users", "settings"]}'),
(3, 'light', 'en', 'America/New_York', TRUE, '{"sidebar": "expanded", "widgets": ["pages", "media", "analytics"]}'),
(4, 'auto', 'en', 'America/New_York', FALSE, '{"sidebar": "expanded", "widgets": ["analytics"]}'),
(5, 'light', 'en', 'America/New_York', TRUE, '{"sidebar": "collapsed", "widgets": ["analytics", "pages"]}');

-- Insert Sample Activity Log
INSERT INTO user_activity_log (user_id, action_type, action_description, ip_address, created_at) VALUES
(1, 'login', 'User logged in successfully', '*************', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(1, 'page_edit', 'Edited Home page content', '*************', DATE_SUB(NOW(), INTERVAL 45 MINUTE)),
(2, 'login', 'User logged in successfully', '*************', DATE_SUB(NOW(), INTERVAL 2 HOURS)),
(2, 'media_upload', 'Uploaded 3 images to media library', '*************', DATE_SUB(NOW(), INTERVAL 90 MINUTE)),
(3, 'login', 'User logged in successfully', '*************', DATE_SUB(NOW(), INTERVAL 3 HOURS)),
(3, 'page_edit', 'Edited About page content', '*************', DATE_SUB(NOW(), INTERVAL 150 MINUTE)),
(4, 'login', 'User logged in successfully', '*************', DATE_SUB(NOW(), INTERVAL 4 HOURS)),
(5, 'login', 'User logged in successfully', '*************', DATE_SUB(NOW(), INTERVAL 5 HOURS));

-- Create Views for User Management

-- User Details View
CREATE VIEW user_details AS
SELECT 
    u.id,
    u.name,
    u.email,
    u.phone,
    u.address,
    u.status,
    u.email_verified,
    u.last_login_at,
    u.created_at,
    GROUP_CONCAT(ur.name) as roles,
    GROUP_CONCAT(ur.slug) as role_slugs,
    up.theme,
    up.language,
    up.timezone,
    up.email_notifications
FROM users u
LEFT JOIN user_role_assignments ura ON u.id = ura.user_id AND ura.is_active = TRUE
LEFT JOIN user_roles ur ON ura.role_id = ur.id
LEFT JOIN user_preferences up ON u.id = up.user_id
WHERE u.status != 'deleted'
GROUP BY u.id;

-- User Activity Summary View
CREATE VIEW user_activity_summary AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    COUNT(ual.id) as total_activities,
    MAX(ual.created_at) as last_activity,
    COUNT(CASE WHEN ual.action_type = 'login' THEN 1 END) as login_count,
    COUNT(CASE WHEN ual.action_type = 'page_edit' THEN 1 END) as page_edit_count,
    COUNT(CASE WHEN ual.action_type = 'media_upload' THEN 1 END) as media_upload_count
FROM users u
LEFT JOIN user_activity_log ual ON u.id = ual.user_id
WHERE u.status = 'active'
GROUP BY u.id, u.name, u.email
ORDER BY last_activity DESC;

-- Role Permissions View
CREATE VIEW role_permissions AS
SELECT 
    ur.id,
    ur.name,
    ur.slug,
    ur.description,
    ur.permissions,
    COUNT(ura.user_id) as user_count,
    ur.is_active
FROM user_roles ur
LEFT JOIN user_role_assignments ura ON ur.id = ura.role_id AND ura.is_active = TRUE
GROUP BY ur.id, ur.name, ur.slug, ur.description, ur.permissions, ur.is_active
ORDER BY ur.name;
