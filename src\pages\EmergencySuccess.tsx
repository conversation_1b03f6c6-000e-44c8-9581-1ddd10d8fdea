import { FC } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { CheckCircle, AlertTriangle, Clock } from 'lucide-react';

const EmergencySuccess: FC = () => {
  const location = useLocation();
  const { name, date, time, type } = location.state || {};

  const formattedDate = new Date(date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="w-16 h-16 text-green-500" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Emergency Repair Scheduled!
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            We've received your emergency repair request and a team will be dispatched as scheduled.
          </p>

          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Appointment Details</h2>
            <div className="space-y-4 text-left">
              <div className="flex items-center gap-3 text-gray-700">
                <Clock className="w-5 h-5 text-primary" />
                <span>
                  Scheduled for: {formattedDate} at {time}
                </span>
              </div>
              <div className="flex items-center gap-3 text-gray-700">
                <AlertTriangle className="w-5 h-5 text-primary" />
                <span>
                  Emergency Type: {type}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-100 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Important Information</h2>
            <div className="space-y-4 text-left">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white font-bold">
                  1
                </div>
                <p className="text-gray-600">
                  Our team will arrive at your property at the scheduled time. Please ensure safe access to the affected area.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white font-bold">
                  2
                </div>
                <p className="text-gray-600">
                  Keep your phone nearby - our team will call you before arrival to confirm details.
                </p>
              </div>
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full bg-blue-500 text-white font-bold">
                  3
                </div>
                <p className="text-gray-600">
                  If your situation worsens before the scheduled time, please call our emergency hotline immediately.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <p className="text-gray-600 font-semibold">
              For immediate assistance or to modify your appointment:
            </p>
            <a
              href="tel:+13053761808"
              className="btn-primary inline-block py-3 px-6 rounded-md hover:shadow-lg transition-shadow"
            >
              (*************
            </a>
          </div>

          <div className="mt-8 pt-8 border-t">
            <Link
              to="/"
              className="text-primary hover:text-primary-dark font-medium transition-colors"
            >
              Return to Homepage
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmergencySuccess;