import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { residentialConfig } from '../puck/schemas/residential';
import { useEditButton } from '@/hooks/useEditButton';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface ResidentialData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const residentialDefaultData: ResidentialData = {
  root: {
    title: 'Residential Roofing Services',
    description: 'Expert residential roofing services in Florida'
  },
  content: [
    {
      type: 'ResidentialHero',
      props: {
        title: 'Residential Roofing Services by Roofers LLC',
        subtitle: 'Protect your home with quality roofing solutions from Florida\'s trusted experts',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'ResidentialServices',
      props: {
        title: 'Our Residential Services',
        services: [
          {
            title: "Roof Installation",
            description: "Complete installation of new roofing systems by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Replacement",
            description: "Full roof replacement with quality materials by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Maintenance",
            description: "Regular maintenance to extend roof life by Roofers LLC",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ResidentialMaterials',
      props: {
        title: 'Roofing Materials',
        materials: [
          {
            name: "Asphalt Shingles",
            description: "Durable and cost-effective roofing solution",
            features: [
              { text: "30-50 year lifespan" },
              { text: "Wide variety of colors" },
              { text: "Affordable option" },
              { text: "Easy maintenance" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Metal Roofing",
            description: "Long-lasting and energy-efficient option",
            features: [
              { text: "50+ year lifespan" },
              { text: "Energy efficient" },
              { text: "Fire resistant" },
              { text: "Environmentally friendly" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Tile Roofing",
            description: "Beautiful and durable ceramic or concrete tiles",
            features: [
              { text: "50+ year lifespan" },
              { text: "Lasting beauty" },
              { text: "Weather resistant" },
              { text: "Excellent insulation" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Flat Roofing",
            description: "Modern and versatile roofing solution for flat or low-slope roofs",
            features: [
              { text: "20-30 year lifespan" },
              { text: "Cost-effective" },
              { text: "Perfect for patios" },
              { text: "Energy efficient" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Wood Roofing",
            description: "Natural and aesthetically pleasing wood shakes and shingles",
            features: [
              { text: "20-25 year lifespan" },
              { text: "Unique natural look" },
              { text: "Excellent insulation" },
              { text: "Weather resistant" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ResidentialCTA',
      props: {
        title: 'Ready to Get Started?',
        description: 'Contact Roofers LLC today for a comprehensive consultation for your residential roofing project.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

const ResidentialRoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<ResidentialData>(residentialDefaultData);

  const { showEditButton } = useEditButton();

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={residentialConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as ResidentialData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={residentialConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default ResidentialRoofing;