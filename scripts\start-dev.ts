import { createServer } from 'vite';
import { ServerManager } from '../src/lib/server-manager';
import config from '../src/config/environment';

async function startDevServer() {
  try {
    // Parse port from environment or config
    const defaultPort = config.port;
    
    // Check port availability and start server manager
    await ServerManager.startServer(defaultPort);
    const { port } = ServerManager.getStatus();

    if (!port) {
      throw new Error('Failed to get port from ServerManager');
    }

    // Create and start Vite server
    const server = await createServer({
      // Load config file
      configFile: './vite.config.ts',
      // Override port with the one from ServerManager
      server: {
        port,
        host: true,
      },
    });

    await server.listen();
    
    server.printUrls();
    
    // Handle process termination
    ['SIGINT', 'SIGTERM'].forEach((signal) => {
      process.on(signal, async () => {
        console.log(`\nReceived ${signal}, shutting down...`);
        await server.close();
        process.exit(0);
      });
    });
  } catch (error) {
    console.error('Failed to start development server:', error);
    process.exit(1);
  }
}

startDevServer();
