# Roofers LLC - Professional Roofing Website

A comprehensive website for a professional roofing company built with React, TypeScript, and modern web technologies. This application serves as both an informational platform and lead generation tool, designed for future integration with a PHP-based CRM system.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Git for version control

### Development Setup

```bash
# 1. Clone the repository
git clone <YOUR_GIT_URL>
cd <YOUR_PROJECT_NAME>

# 2. Install dependencies
npm install

# 3. Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# 4. Start development server (runs on port 8080)
npm run dev

# 5. Open your browser
open http://localhost:8080
```

## 📋 Available Scripts

```bash
npm run dev          # Start development server on port 8080
npm run build        # Build for production
npm run build:dev    # Build for development
npm run build:prod   # Build for production with optimizations
npm run preview      # Preview production build
npm run lint         # Run ESLint checks
npm run typecheck    # Run TypeScript validation
npm run validate     # Run both typecheck and lint
```

## 🛠 Technology Stack

### Core Technologies
- **React 18.3.1** - Modern React with hooks and concurrent features
- **TypeScript 5.5.3** - Type-safe JavaScript development
- **Vite 5.4.1** - Fast build tool and development server
- **Node.js** - JavaScript runtime for development tools

### UI & Styling
- **Tailwind CSS 3.4.11** - Utility-first CSS framework
- **shadcn/ui** - High-quality component library
- **Radix UI** - Primitive components for accessibility
- **Lucide React** - Beautiful icon library

### Form Handling & Validation
- **React Hook Form 7.53.0** - Performant form library
- **Zod 3.23.8** - TypeScript-first schema validation
- **@hookform/resolvers** - Form validation integration

### Development Tools
- **ESLint** - Code linting and quality checks
- **TypeScript ESLint** - TypeScript-specific linting
- **SWC** - Fast JavaScript/TypeScript compiler
- **PostCSS & Autoprefixer** - CSS processing

## 📁 Project Structure

```
/
├── docs/                  # Comprehensive documentation
│   ├── ENVIRONMENT.md     # Environment configuration guide
│   └── USER_GUIDELINES.md # Development guidelines
├── memory-bank/           # Project context and planning documents
├── src/                   # Source code
│   ├── components/        # React components
│   ├── config/           # Environment configuration
│   ├── lib/              # Utilities and API clients
│   ├── pages/            # Page components
│   ├── hooks/            # Custom React hooks
│   └── styles/           # Global styles
├── public/               # Static assets
├── backend/              # Future CRM integration
├── scripts/              # Development and build scripts
└── .env.example          # Environment variables template
```

## 🔧 Environment Configuration

This project uses a centralized, type-safe environment configuration system. All environment variables are managed through `src/config/environment.ts`.

### Required Environment Variables
```bash
PORT=8080                           # Development server port
NODE_ENV=development               # Environment type
APP_URL=http://localhost:8080      # Main application URL
API_BASE_URL=http://localhost:3001 # API server URL (for future CRM)
AUTH_CALLBACK_URL=http://localhost:8080/auth/callback
CORS_ORIGIN=http://localhost:8080  # Allowed CORS origins
```

**Important**: Never hardcode URLs or ports in your code. Always use the environment configuration module.

## 📖 Documentation

- **[Environment Configuration Guide](docs/ENVIRONMENT.md)** - Comprehensive environment setup and configuration
- **[User Guidelines](docs/USER_GUIDELINES.md)** - Development rules and best practices
- **[Project Context](memory-bank/)** - Project planning and technical context

## 🚀 Deployment

### Build Process
```bash
# Production build
npm run build:prod

# Preview the build
npm run preview
```

### Deployment Platforms
- **Netlify** - Recommended for static hosting
- **Vercel** - Excellent for React applications
- **GitHub Pages** - Free hosting for open source projects

### Environment Setup
1. Set up production environment variables
2. Configure build settings on your platform
3. Enable HTTPS and security headers
4. Set up custom domain (if applicable)

## 🔒 Security & Best Practices

- Environment variables are validated on startup
- No hardcoded URLs or sensitive data in code
- Type-safe development with TypeScript
- ESLint rules enforce code quality
- Responsive design for all devices
- Accessibility compliance (WCAG guidelines)

## 🤝 Development Guidelines

1. **Use Environment Configuration**: Always import from `@/config/environment`
2. **Type Safety**: Use TypeScript strictly, avoid `any` types
3. **Component Patterns**: Follow established component structure
4. **Code Quality**: Pass all ESLint and TypeScript checks
5. **Testing**: Test components and forms thoroughly
6. **Documentation**: Update docs when adding features

## 🐛 Troubleshooting

### Port Conflicts
The development server automatically handles port conflicts. If port 8080 is in use, it will find the next available port.

### Environment Issues
- Ensure `.env` file exists and is properly formatted
- Check that all required variables are defined
- Validate URLs include protocol (http:// or https://)

### Build Issues
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for errors
npm run typecheck
npm run lint
```

## 📞 Support

For development questions and issues:
1. Check the [documentation](docs/)
2. Review [project context](memory-bank/)
3. Validate environment configuration
4. Check console for detailed error messages

---

Built with ❤️ for professional roofing services
