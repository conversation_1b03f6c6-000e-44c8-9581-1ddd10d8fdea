# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URLs
APP_URL=https://roofers.llc
API_BASE_URL=https://api.roofers.llc
AUTH_CALLBACK_URL=https://roofers.llc/auth/callback

# Database Configuration
DATABASE_HOST=localhost
DATABASE_USER=production_user
DATABASE_PASSWORD=production_password
DATABASE_NAME=roofers_production
DATABASE_PORT=3306

# Security
CORS_ORIGIN=https://roofers.llc
API_KEY=your_production_api_key_here

# Auth
AUTH_SECRET=your_production_auth_secret_here
GOOGLE_ID=your_production_google_client_id
GOOGLE_SECRET=your_production_google_client_secret

# Emergency Contact
EMERGENCY_PHONE=${EMERGENCY_PHONE}
EMERGENCY_EMAIL=${EMERGENCY_EMAIL}

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

