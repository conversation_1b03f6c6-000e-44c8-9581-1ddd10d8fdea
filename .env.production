# Server Configuration
PORT=3001
NODE_ENV=production

# Frontend URLs
APP_URL=https://roofers.llc
API_BASE_URL=https://api.roofers.llc
AUTH_CALLBACK_URL=https://roofers.llc/auth/callback

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=production_user
DB_PASSWORD=production_password
DB_NAME=roofers_production

# Security
CORS_ORIGIN=https://roofers.llc
API_KEY=your_production_api_key_here

# Emergency Contact
EMERGENCY_PHONE=${EMERGENCY_PHONE}
EMERGENCY_EMAIL=${EMERGENCY_EMAIL}

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100