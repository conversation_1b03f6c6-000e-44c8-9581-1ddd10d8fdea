import mysql, { RowDataPacket, FieldPacket } from 'mysql2/promise'
import * as fs from 'fs/promises'
import * as path from 'path'
import * as dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// Load environment variables
dotenv.config()

// Get current file path in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

interface MigrationRecord extends RowDataPacket {
  id: string
  name: string
  executed_at: Date
}

async function createDatabase() {
  let connection
  try {
    console.log('Creating connection for database setup...')
    console.log('Database config:', {
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      database: process.env.DATABASE_NAME,
      port: process.env.DATABASE_PORT,
    })

    // Create a connection without database selected
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    })

    console.log('Connection successful')
    console.log(`Creating database if not exists: ${process.env.DATABASE_NAME}`)
    await connection.query(
      `CREATE DATABASE IF NOT EXISTS ${process.env.DATABASE_NAME}`
    )
    console.log('Database setup complete')
  } catch (error) {
    if (error instanceof Error) {
      console.error('Failed to create database:', error.message)
      console.error('Stack trace:', error.stack)
    } else {
      console.error('Failed to create database:', error)
    }
    throw error
  } finally {
    if (connection) {
      await connection.end().catch(console.error)
    }
  }
}

async function runMigrations() {
  let pool
  try {
    // Ensure database exists
    await createDatabase()

    console.log('\nCreating connection pool for migrations...')
    // Create connection pool
    pool = mysql.createPool({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    })

    // Test connection
    console.log('Testing pool connection...')
    await pool.getConnection()
    console.log('Pool connection successful')

    // Create migrations table if it doesn't exist
    console.log('\nCreating migrations table if not exists...')
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(36) PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Get list of migration files
    console.log('\nReading migration files...')
    const migrationsDir = path.join(__dirname, 'migrations')
    const files = await fs.readdir(migrationsDir)
    const sqlFiles = files.filter(f => f.endsWith('.sql')).sort()
    console.log(`Found ${sqlFiles.length} migration files`)

    // Get executed migrations
    const [rows] = await pool.execute('SELECT name FROM migrations') as [MigrationRecord[], FieldPacket[]]
    const executedMigrations = new Set(rows.map(row => row.name))
    console.log(`Found ${executedMigrations.size} executed migrations`)

    // Run pending migrations
    for (const file of sqlFiles) {
      if (!executedMigrations.has(file)) {
        console.log(`\nRunning migration: ${file}`)
        
        // Read and split migration file
        const sql = await fs.readFile(path.join(migrationsDir, file), 'utf8')
        const statements = sql
          .split(';')
          .map(s => s.trim())
          .filter(s => s.length > 0)

        // Start transaction
        const connection = await pool.getConnection()
        await connection.beginTransaction()

        try {
          // Execute each statement
          for (const statement of statements) {
            console.log('Executing statement:', statement.substring(0, 50) + '...')
            await connection.execute(statement)
          }

          // Record migration
          await connection.execute(
            'INSERT INTO migrations (id, name) VALUES (UUID(), ?)',
            [file]
          )

          // Commit transaction
          await connection.commit()
          console.log(`Completed migration: ${file}`)
        } catch (error) {
          // Rollback on error
          await connection.rollback()
          console.error(`Migration failed: ${file}`)
          if (error instanceof Error) {
            console.error('Error:', error.message)
            console.error('Stack trace:', error.stack)
          } else {
            console.error('Error:', error)
          }
          throw error
        } finally {
          connection.release()
        }
      } else {
        console.log(`Skipping already executed migration: ${file}`)
      }
    }

    console.log('\nAll migrations completed successfully')
  } catch (error) {
    if (error instanceof Error) {
      console.error('Migration failed:', error.message)
      console.error('Stack trace:', error.stack)
    } else {
      console.error('Migration failed:', error)
    }
    throw error
  } finally {
    if (pool) {
      console.log('\nClosing connection pool...')
      await pool.end().catch(err => console.error('Error closing pool:', err))
      console.log('Connection pool closed')
    }
  }
}

// Run migrations if executed directly
if (import.meta.url.endsWith(process.argv[1])) {
  runMigrations()
    .then(() => {
      console.log('\nMigration process complete')
      process.exit(0)
    })
    .catch(error => {
      console.error('\nMigration process failed:', error)
      process.exit(1)
    })
}