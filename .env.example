# Server Configuration
PORT=8080
NODE_ENV=development

# Frontend URLs
APP_URL=http://localhost:8080
API_BASE_URL=http://localhost:3001
AUTH_CALLBACK_URL=http://localhost:8080/auth/callback

# Database Configuration
DATABASE_HOST=localhost
DATABASE_USER=root
DATABASE_PASSWORD=
DATABASE_NAME=roofers
DATABASE_PORT=3306

# Security
CORS_ORIGIN=http://localhost:8080
API_KEY=your-api-key-here

# Auth
AUTH_SECRET=your-auth-secret-here
GOOGLE_ID=your-google-client-id
GOOGLE_SECRET=your-google-client-secret


