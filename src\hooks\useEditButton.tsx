import { useAuth } from './useAuth';

/**
 * Custom hook to determine if edit buttons should be visible
 * Edit buttons are shown only to authenticated users with admin or editor roles
 */
export function useEditButton() {
  const { isAuthenticated, canEditPages } = useAuth();
  
  // Show edit button if user is authenticated and has edit permissions
  const showEditButton = isAuthenticated && canEditPages();
  
  return {
    showEditButton,
    canEdit: showEditButton
  };
}
