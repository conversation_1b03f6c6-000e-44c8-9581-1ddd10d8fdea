import React, { useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import UserProfileModal from './UserProfileModal';
import {
  LogOut,
  User,
  Shield,
  Edit3,
  Users,
  Settings,
  Home,
  FileText,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Share2,
  Search
} from 'lucide-react';

export default function AdminLayout() {
  const { user, logout, canEditPages, canManageUsers } = useAuth();
  const navigate = useNavigate();
  const [sidebarVisible, setSidebarVisible] = useState(true); // Start with sidebar visible
  const [profileModalOpen, setProfileModalOpen] = useState(false);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'editor':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'viewer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 p-2 rounded-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Roofers LLC Admin
                </h1>
                <p className="text-sm text-gray-500">Content Management System</p>
              </div>

              {/* Sidebar Toggle Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={toggleSidebar}
                className="flex items-center space-x-2 ml-4"
                title={sidebarVisible ? 'Hide Sidebar' : 'Show Sidebar'}
              >
                {sidebarVisible ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                <span className="hidden sm:inline">
                  {sidebarVisible ? 'Hide' : 'Show'} Sidebar
                </span>
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              {/* User Info */}
              <button
                onClick={() => setProfileModalOpen(true)}
                className="flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 transition-colors"
              >
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                  <div className="flex space-x-1">
                    {user?.roles.map((role) => (
                      <Badge
                        key={role}
                        variant="outline"
                        className={getRoleBadgeColor(role)}
                      >
                        {role}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="bg-gray-200 p-2 rounded-full">
                  <User className="h-5 w-5 text-gray-600" />
                </div>
              </button>

              {/* Logout Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
        {/* Floating Toggle Button (visible when sidebar is hidden) */}
        {!sidebarVisible && (
          <Button
            variant="outline"
            size="sm"
            onClick={toggleSidebar}
            className="fixed top-20 left-4 z-50 bg-white shadow-lg hover:shadow-xl transition-all duration-200 border-gray-300"
            title="Show Admin Sidebar"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        <div className={`grid gap-8 transition-all duration-300 ease-in-out ${
          sidebarVisible ? 'grid-cols-1 lg:grid-cols-4' : 'grid-cols-1'
        }`}>
          {/* Sidebar */}
          {sidebarVisible && (
            <div className={`lg:col-span-1 transition-all duration-300 ease-in-out ${
              sidebarVisible ? 'opacity-100 transform translate-x-0' : 'opacity-0 transform -translate-x-full'
            }`}>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/')}
                  >
                    <Home className="mr-2 h-4 w-4" />
                    View Website
                  </Button>

                  {canEditPages() && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/pages')}
                    >
                      <Edit3 className="mr-2 h-4 w-4" />
                      Edit Pages
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/admin/analytics')}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Analytics
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => navigate('/admin/media')}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Media Library
                  </Button>

                  {(canEditPages() || canManageUsers()) && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/social')}
                    >
                      <Share2 className="mr-2 h-4 w-4" />
                      Social Media
                    </Button>
                  )}

                  {(canEditPages() || canManageUsers()) && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/seo')}
                    >
                      <Search className="mr-2 h-4 w-4" />
                      SEO Management
                    </Button>
                  )}

                  {canManageUsers() && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/users')}
                    >
                      <Users className="mr-2 h-4 w-4" />
                      Manage Users
                    </Button>
                  )}

                  {canManageUsers() && (
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => navigate('/admin/settings')}
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Settings
                    </Button>
                  )}
                </CardContent>
              </Card>


            </div>
          )}

          {/* Content Area */}
          <div className={`transition-all duration-300 ease-in-out ${
            sidebarVisible ? 'lg:col-span-3' : 'col-span-1'
          }`}>
            <Outlet />
          </div>
        </div>
      </main>

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={profileModalOpen}
        onClose={() => setProfileModalOpen(false)}
      />
    </div>
  );
}
