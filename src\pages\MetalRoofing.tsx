import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { metalConfig } from '../puck/schemas/metal';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface MetalData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: MetalData = {
  root: {
    title: 'Metal Roofing Services',
    description: 'Premium metal roofing solutions in Florida'
  },
  content: [
    {
      type: 'MetalHero',
      props: {
        title: 'Metal Roofing Solutions by Roofers LLC',
        subtitle: 'Premium metal roofing systems for durability and energy efficiency',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'MetalServices',
      props: {
        title: 'Our Metal Roofing Services',
        services: [
          {
            title: "Metal Roof Installation",
            description: "Expert installation of premium metal roofing systems for superior durability",
            image: "/placeholder.svg"
          },
          {
            title: "Metal Roof Replacement",
            description: "Complete metal roof replacement services with quality materials",
            image: "/placeholder.svg"
          },
          {
            title: "Metal Roof Repair",
            description: "Professional repair services to maintain your metal roof's integrity",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'MetalSystems',
      props: {
        title: 'Metal Roofing Systems',
        systems: [
          {
            name: "Standing Seam Metal",
            description: "Premium concealed fastener system for maximum durability",
            features: [
              { text: "50+ year lifespan" },
              { text: "Weather resistant" },
              { text: "Energy efficient" },
              { text: "Low maintenance" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Metal Shingles",
            description: "Elegant metal shingles that combine durability with style",
            features: [
              { text: "Traditional look" },
              { text: "Impact resistant" },
              { text: "Multiple colors" },
              { text: "Long lasting" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Corrugated Metal",
            description: "Cost-effective and durable exposed fastener system",
            features: [
              { text: "Economical choice" },
              { text: "Quick installation" },
              { text: "Strong & durable" },
              { text: "Various profiles" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'MetalCTA',
      props: {
        title: 'Ready to Upgrade Your Roof?',
        description: 'Contact Roofers LLC today for a consultation about our premium metal roofing solutions.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

const MetalRoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<MetalData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={metalConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as MetalData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={metalConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default MetalRoofing;