import { type Data } from '@measured/puck';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface CommercialData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const commercialDefaultData: CommercialData = {
  root: {
    title: 'Commercial Roofing Services',
    description: 'Expert commercial roofing services in Florida'
  },
  content: [
    {
      type: 'CommercialHero',
      props: {
        title: 'Commercial Roofing Solutions by Roofers LLC',
        subtitle: 'Professional roofing services for businesses of all sizes across Florida',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'CommercialServices',
      props: {
        title: 'Our Commercial Services',
        services: [
          {
            title: "Flat Roof Installation",
            description: "Expert installation of commercial flat roofing systems by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Replacement",
            description: "Complete commercial roof replacement services by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Preventive Maintenance",
            description: "Regular maintenance programs by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Emergency Repairs",
            description: "24/7 emergency repair services by Roofers LLC",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'CommercialSystems',
      props: {
        title: 'Commercial Roofing Systems',
        systems: [
          {
            name: "Built-Up Roofing (BUR)",
            description: "Traditional and reliable multi-layer system",
            features: [
              { text: "Multiple protective layers" },
              { text: "Excellent waterproofing" },
              { text: "Long-lasting durability" },
              { text: "Superior protection" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Modified Bitumen",
            description: "Durable and weather-resistant solution",
            features: [
              { text: "High tensile strength" },
              { text: "Weather resistance" },
              { text: "Easy maintenance" },
              { text: "Excellent durability" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "TPO/PVC Systems",
            description: "Energy-efficient single-ply membranes",
            features: [
              { text: "Energy efficient" },
              { text: "UV resistant" },
              { text: "Heat reflective" },
              { text: "Cost effective" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'CommercialCTA',
      props: {
        title: 'Ready to Discuss Your Project?',
        description: 'Contact Roofers LLC today for a comprehensive consultation for your commercial roofing needs.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};
