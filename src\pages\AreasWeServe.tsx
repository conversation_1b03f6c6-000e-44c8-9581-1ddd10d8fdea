import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>der, type Data } from '@measured/puck';
import { areasConfig } from '../puck/schemas/areas';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface AreasData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const transformCounties = (counties: Array<{ name: string; cities: string[] }>) => {
  return counties.map(county => ({
    name: county.name,
    cities: county.cities.map(city => ({ text: city }))
  }));
};

export const areasDefaultData: AreasData = {
  root: {
    title: 'Areas We Serve',
    description: 'Our service areas across Florida'
  },
  content: [
    {
      type: 'AreasHero',
      props: {
        title: 'Areas We Serve',
        subtitle: 'Roofers LLC provides professional roofing services across Florida\'s most populous counties. Contact us today to schedule your free estimate!',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'AreasGrid',
      props: {
        title: 'Service Areas',
        counties: transformCounties([
          {
            name: "Miami-Dade County",
            cities: [
              "Miami", "Miami Beach", "Coral Gables", "Doral", "Hialeah", "Homestead", "Kendall",
              "Aventura", "Bal Harbour", "Coconut Grove", "Cutler Bay", "Florida City", "Key Biscayne",
              "Miami Gardens", "Miami Lakes", "Miami Shores", "Miami Springs", "North Bay Village",
              "North Miami", "North Miami Beach", "Opa-locka", "Palmetto Bay", "Pinecrest",
              "South Miami", "Sunny Isles Beach", "Surfside", "Sweetwater", "West Miami"
            ]
          },
          {
            name: "Broward County",
            cities: [
              "Fort Lauderdale", "Hollywood", "Pompano Beach", "Coral Springs", "Miramar", "Pembroke Pines",
              "Cooper City", "Coconut Creek", "Dania Beach", "Davie", "Deerfield Beach", "Hallandale Beach",
              "Lauderdale Lakes", "Lauderdale-by-the-Sea", "Lauderhill", "Lighthouse Point", "Margate",
              "North Lauderdale", "Oakland Park", "Parkland", "Plantation", "Southwest Ranches",
              "Sunrise", "Tamarac", "Weston", "Wilton Manors"
            ]
          },
          {
            name: "Palm Beach County",
            cities: [
              "West Palm Beach", "Boca Raton", "Boynton Beach", "Delray Beach", "Jupiter", "Wellington",
              "Belle Glade", "Greenacres", "Highland Beach", "Hypoluxo", "Juno Beach", "Jupiter Inlet Colony",
              "Lake Clarke Shores", "Lake Park", "Lake Worth Beach", "Lantana", "Loxahatchee Groves",
              "Manalapan", "Ocean Ridge", "Pahokee", "Palm Beach", "Palm Beach Gardens", "Palm Springs",
              "Riviera Beach", "Royal Palm Beach", "South Bay", "South Palm Beach", "Tequesta"
            ]
          },
          {
            name: "Lee County",
            cities: [
              "Fort Myers", "Cape Coral", "Sanibel", "Bonita Springs", "Estero", "Fort Myers Beach",
              "North Fort Myers", "Lehigh Acres", "Pine Island", "Saint James City", "Matlacha",
              "Alva", "Boca Grande", "Bokeelia", "Captiva", "Pine Manor", "San Carlos Park", "Tice"
            ]
          },
          {
            name: "Charlotte County",
            cities: [
              "Punta Gorda", "Port Charlotte", "Englewood", "Babcock Ranch", "Boca Grande",
              "Charlotte Harbor", "Cleveland", "Grove City", "Harbour Heights", "Manasota Key",
              "Placida", "Rotonda West", "South Gulf Cove"
            ]
          },
          {
            name: "Hendry County",
            cities: [
              "LaBelle", "Clewiston", "Felda", "Harlem", "Montura", "Pioneer Plantation",
              "Port LaBelle", "Flaghole", "Hookers Point", "Ladeca"
            ]
          },
          {
            name: "Collier County",
            cities: [
              "Naples", "Marco Island", "Immokalee", "Ave Maria", "East Naples", "Golden Gate",
              "Lely", "Naples Manor", "Naples Park", "North Naples", "Pine Ridge", "Pelican Bay",
              "Orange Tree", "Vineyards", "Golden Gate Estates", "Chokoloskee", "Everglades City"
            ]
          },
          {
            name: "Sarasota County",
            cities: [
              "Sarasota", "Venice", "North Port", "Siesta Key", "Longboat Key", "Osprey",
              "Nokomis", "Casey Key", "Englewood", "Laurel", "Gulf Gate Estates", "Vamo",
              "South Venice", "Lake Sarasota", "Ridge Wood Heights", "South Gate Ridge"
            ]
          },
          {
            name: "Manatee County",
            cities: [
              "Bradenton", "Anna Maria Island", "Longboat Key", "Palmetto", "Holmes Beach",
              "Anna Maria", "Bradenton Beach", "Ellenton", "Memphis", "Myakka City", "Parrish",
              "Samoset", "South Bradenton", "West Bradenton", "Whitfield"
            ]
          },
          {
            name: "Hardee County",
            cities: [
              "Wauchula", "Bowling Green", "Zolfo Springs", "Gardner", "Limestone", "Fort Green",
              "Fort Green Springs", "Ona", "Vandolah", "Lake Dale"
            ]
          },
          {
            name: "Glades County",
            cities: [
              "Moore Haven", "Buckhead Ridge", "Palmdale", "Lakeport", "Ortona", "Port LaBelle",
              "Venus", "Muse", "Maple Grove", "Brighton Seminole Indian Reservation"
            ]
          },
          {
            name: "Pinellas County",
            cities: [
              "St. Petersburg", "Clearwater", "Largo", "Dunedin", "Tarpon Springs", "Belleair",
              "Belleair Beach", "Belleair Bluffs", "Belleair Shore", "Gulfport", "Indian Rocks Beach",
              "Indian Shores", "Kenneth City", "Madeira Beach", "North Redington Beach",
              "Oldsmar", "Pinellas Park", "Redington Beach", "Redington Shores", "Safety Harbor",
              "Seminole", "South Pasadena", "St. Pete Beach", "Treasure Island"
            ]
          }
        ])
      }
    },
    {
      type: 'AreasCTA',
      props: {
        title: 'Ready to Get Started?',
        description: 'Our expert team is ready to serve you in any of these locations. Contact us today for a free consultation!',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

const AreasWeServe: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<AreasData>(areasDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={areasConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as AreasData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={areasConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default AreasWeServe;