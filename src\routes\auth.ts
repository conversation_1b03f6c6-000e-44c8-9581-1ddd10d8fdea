import { Auth, type AuthConfig } from '@auth/core'
import type { Session, User, Account, Profile } from '@auth/core/types'
import Google from '@auth/core/providers/google'
import mysql from 'mysql2/promise'
import { config } from '../config/environment'

// MySQL connection pool for the auth adapter
const pool = mysql.createPool({
  host: process.env.DATABASE_HOST,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  port: parseInt(process.env.DATABASE_PORT || '3306'),
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
})

// Define base session types
interface CustomUser extends User, Record<string, unknown> {
  id: string
  email: string
  name?: string | null
  image?: string | null
  role?: string
}

interface CustomSession extends Session {
  user: {
    id: string
    email: string
    name?: string
    image?: string
    role?: string
  }
}

// Type for JWT token with role
type CustomToken = {
  name?: string | null
  email?: string | null
  picture?: string | null
  sub?: string
  role?: string
  iat?: number
  exp?: number
  jti?: string
  [key: string]: unknown
}

// Auth configuration
const authConfig: AuthConfig = {
  trustHost: true,
  providers: [
    Google({
      clientId: process.env.GOOGLE_ID || '',
      clientSecret: process.env.GOOGLE_SECRET || '',
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt(params: {
      token: CustomToken
      user?: User | null
      account?: Account | null
      profile?: Profile | null
      trigger?: 'signIn' | 'signUp' | 'update'
    }): Promise<CustomToken> {
      if (params.user && 'role' in params.user) {
        params.token.role = params.user.role as string
      }
      return params.token
    },
    async session(params: { 
      session: Session
      token: CustomToken
    }): Promise<CustomSession> {
      const email = params.session.user?.email
      if (!email) throw new Error('No user email in session')

      return {
        ...params.session,
        user: {
          id: params.token.sub || '',
          email,
          name: params.session.user?.name || undefined,
          image: params.session.user?.image || undefined,
          role: params.token.role,
        },
      }
    },
    async signIn(params: { 
      user: User
      account: Account | null
      profile?: Profile | null
    }): Promise<boolean> {
      if (!params.user?.email) return false
      // Add custom sign in validation if needed
      return true
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
    signOut: '/auth/signout',
  },
  debug: config.nodeEnv === 'development',
}

// Route handler for auth requests
export async function authHandler(request: Request) {
  return Auth(request, authConfig)
}

// Auth helpers
export const auth = {
  signIn: async (provider: string): Promise<Response> => {
    const response = await fetch(`/api/auth/signin/${provider}`, {
      method: 'POST',
    })
    return response
  },
  signOut: async (): Promise<Response> => {
    const response = await fetch('/api/auth/signout', {
      method: 'POST',
    })
    return response
  },
  getSession: async (): Promise<CustomSession | null> => {
    const response = await fetch('/api/auth/session')
    if (!response.ok) return null
    return response.json()
  },
}

// Export types
export type { CustomSession, CustomUser, CustomToken }