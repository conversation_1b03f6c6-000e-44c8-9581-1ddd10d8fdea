import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { api } from '@/lib/api';
import {
  Plus,
  Edit,
  Eye,
  Trash2,
  Globe,
  FileText,
  Settings,
  Calendar,
  User,
  Search,
  Filter,
  MoreVertical
} from 'lucide-react';

interface Page {
  id: string;
  title: string;
  slug: string;
  path: string;
  status: 'draft' | 'published' | 'archived';
  template_name?: string;
  created_by_name: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
  is_homepage: boolean;
}

interface PageTemplate {
  id: string;
  name: string;
  slug: string;
  description: string;
  is_active: boolean;
}

export default function AdminPages() {
  const [pages, setPages] = useState<Page[]>([]);
  const [templates, setTemplates] = useState<PageTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPage, setSelectedPage] = useState<Page | null>(null);

  // Load pages and templates
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load pages
      const pagesResponse = await api.pages.getPages();
      if (pagesResponse.success && Array.isArray(pagesResponse.data)) {
        setPages(pagesResponse.data);
      }

      // Load templates
      const templatesResponse = await api.pages.getTemplates();
      if (templatesResponse.success && Array.isArray(templatesResponse.data)) {
        setTemplates(templatesResponse.data);
      }
    } catch (error) {
      console.error('Error loading pages data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Filter pages based on search and status
  const filteredPages = pages.filter(page => {
    const matchesSearch = page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         page.path.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || page.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Handle page creation
  const handleCreatePage = () => {
    setShowCreateModal(true);
  };

  // Handle page editing
  const handleEditPage = (page: Page) => {
    // Navigate to Puck editor
    window.open(`/admin/edit${page.path}`, '_blank');
  };

  // Handle page preview
  const handlePreviewPage = (page: Page) => {
    window.open(page.path, '_blank');
  };

  // Handle page deletion
  const handleDeletePage = async (page: Page) => {
    if (window.confirm(`Are you sure you want to delete "${page.title}"?`)) {
      try {
        const response = await api.pages.deletePage(page.id);
        if (response.success) {
          await loadData();
          alert('Page deleted successfully');
        } else {
          alert('Failed to delete page');
        }
      } catch (error) {
        console.error('Error deleting page:', error);
        alert('Failed to delete page');
      }
    }
  };

  // Handle status change
  const handleStatusChange = async (page: Page, newStatus: string) => {
    try {
      const response = await api.pages.updatePage(page.id, { status: newStatus });
      if (response.success) {
        await loadData();
        alert(`Page ${newStatus} successfully`);
      } else {
        alert('Failed to update page status');
      }
    } catch (error) {
      console.error('Error updating page status:', error);
      alert('Failed to update page status');
    }
  };

  return (
    <div className="space-y-8">
      <AdminPageHeader
        title="Page Management"
        description="Create, edit, and manage your website pages dynamically"
      >
        <Button onClick={handleCreatePage} className="flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Create Page</span>
        </Button>
      </AdminPageHeader>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Pages</p>
                <p className="text-2xl font-bold">{loading ? '...' : pages.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold">{loading ? '...' : pages.filter(p => p.status === 'published').length}</p>
              </div>
              <Globe className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold">{loading ? '...' : pages.filter(p => p.status === 'draft').length}</p>
              </div>
              <Edit className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Templates</p>
                <p className="text-2xl font-bold">{loading ? '...' : templates.length}</p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Pages</CardTitle>
          <CardDescription>
            Manage all your website pages from this central location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search pages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>

          {/* Pages List */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading pages...</p>
              </div>
            ) : filteredPages.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No pages found</p>
              </div>
            ) : (
              filteredPages.map((page) => (
                <Card key={page.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{page.title}</h3>
                          <Badge variant="outline" className={getStatusColor(page.status)}>
                            {page.status}
                          </Badge>
                          {page.is_homepage && (
                            <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                              Homepage
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{page.path}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{page.created_by_name}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(page.created_at).toLocaleDateString()}</span>
                          </div>
                          {page.template_name && (
                            <div className="flex items-center space-x-1">
                              <Settings className="h-3 w-3" />
                              <span>{page.template_name}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handlePreviewPage(page)}
                          className="flex items-center space-x-1"
                        >
                          <Eye className="h-3 w-3" />
                          <span>Preview</span>
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleEditPage(page)}
                          className="flex items-center space-x-1"
                        >
                          <Edit className="h-3 w-3" />
                          <span>Edit</span>
                        </Button>
                        <div className="relative group">
                          <Button size="sm" variant="ghost">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                          <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                            <div className="py-1">
                              {page.status !== 'published' && (
                                <button
                                  onClick={() => handleStatusChange(page, 'published')}
                                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  Publish
                                </button>
                              )}
                              {page.status !== 'draft' && (
                                <button
                                  onClick={() => handleStatusChange(page, 'draft')}
                                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  Move to Draft
                                </button>
                              )}
                              {page.status !== 'archived' && (
                                <button
                                  onClick={() => handleStatusChange(page, 'archived')}
                                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                >
                                  Archive
                                </button>
                              )}
                              <button
                                onClick={() => handleDeletePage(page)}
                                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
