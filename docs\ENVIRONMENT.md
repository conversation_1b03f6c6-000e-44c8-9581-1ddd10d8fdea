# Environment Configuration Guide

## Overview

This application uses environment-based configuration to support different deployment environments (development, staging, production) without hardcoded values. The frontend application is built with React/TypeScript, and will later be integrated with a PHP-based CRM system.

## Environment Files

### `.env.example`
Template file showing all required environment variables. Copy this file to create new environment configurations.

### `.env`
Local development configuration. Not committed to version control.

### `.env.production`
Production environment configuration. Contains production-specific values.

## Required Environment Variables

```bash
# Server Configuration
PORT=8080                     # Development server port (default: 3001)
NODE_ENV=development         # Environment (development|production)

# Frontend URLs
VITE_APP_URL                # Main application URL
VITE_API_BASE_URL          # API server URL (will point to CRM API when integrated)
VITE_AUTH_CALLBACK_URL     # Authentication callback URL

# Security
CORS_ORIGIN                # Allowed CORS origins (comma-separated)
API_KEY                    # API key for protected endpoints
```

## Environment Setup

1. Development Setup
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your local configuration
nano .env

# Start development server
npm run dev
```

2. Production Setup
```bash
# Ensure production environment variables are set
cp .env.example .env.production

# Edit production configuration
nano .env.production

# Build and start production server
npm run build:prod
```

## URL Configuration

### Development
```
Frontend: http://localhost:5173
API: TBD (will be configured when CRM is integrated)
Auth Callback: http://localhost:5173/auth/callback
```

### Production
```
Frontend: https://roofers.llc
API: TBD (will be configured when CRM is integrated)
Auth Callback: https://roofers.llc/auth/callback
```

## CORS Configuration

CORS configuration will be implemented when the CRM backend is integrated. For now, the configuration is prepared in the environment files for future use.

## Security Considerations

1. Never commit .env files containing sensitive data
2. Use different API keys for each environment
3. Restrict CORS origins to known domains
4. Enable HTTPS in production
5. Validate environment variables on startup

## Future Integration Notes

The application is prepared for integration with a PHP-based CRM system:
1. API client is configured to handle backend integration
2. Environment variables are structured for API endpoints
3. CORS configuration is prepared for cross-origin requests
4. Authentication flow is ready for backend integration

## Troubleshooting

1. Port Conflicts
```bash
# Check if port is in use
lsof -i :3001

# Kill existing process
kill -9 <PID>
```

2. Environment Variables
```bash
# Verify environment variables are loaded
npm run dev -- --debug

# Check current configuration
node -e 'console.log(process.env)'