# Environment Configuration Guidelines for Roofers LLC Project

## Overview

This document provides comprehensive guidelines for Augment Chat when working with the Roofers LLC codebase, focusing on environment configuration best practices. The application uses a centralized, type-safe environment configuration system that eliminates hardcoded URLs and supports multiple deployment environments.

## 1. Centralized Environment Configuration

### Core Principles
- **Single Source of Truth**: All environment configuration is managed through `src/config/environment.ts`
- **Type Safety**: Strongly typed configuration with runtime validation
- **Cross-Platform Support**: Handles both Vite's `import.meta.env` and Node.js `process.env`
- **Startup Validation**: Throws clear errors when required variables are missing
- **No Hardcoded URLs**: All URL references must use the environment configuration module

### Environment Configuration Module
The `src/config/environment.ts` module provides:
- Validation of all required environment variables on application startup
- Strongly typed access to environment configuration
- Singleton configuration object for app-wide use
- Support for both development and production environments
- Clear error messages when required variables are missing

## 2. Environment Variable Structure

### Required Environment Variables

```bash
# Server Configuration
PORT=8080                           # Development server port (changed from 5173)
NODE_ENV=development               # Environment (development|production|test)

# Application URLs
APP_URL=http://localhost:8080      # Main application URL
API_BASE_URL=http://localhost:3001 # API server URL (for future CRM integration)
AUTH_CALLBACK_URL=http://localhost:8080/auth/callback # Authentication callback URL

# Database Configuration (for future use)
DATABASE_HOST=localhost
DATABASE_USER=root
DATABASE_PASSWORD=
DATABASE_NAME=roofers
DATABASE_PORT=3306

# Security
CORS_ORIGIN=http://localhost:8080  # Allowed CORS origins (comma-separated)
API_KEY=your-api-key-here          # API key for protected endpoints
AUTH_SECRET=your-auth-secret-here  # Authentication secret

# Third-party Integration (for future use)
GOOGLE_ID=your-google-client-id
GOOGLE_SECRET=your-google-client-secret
```

### Environment Files Structure

#### `.env.example` (Template)
Contains all required environment variables with example values. This file is committed to version control and serves as documentation.

#### `.env` (Development)
Local development configuration. **Never committed to version control.**

#### `.env.production` (Production)
Production environment configuration with production-specific values.

### Environment-Specific Configuration

#### Development Environment
```bash
PORT=8080
NODE_ENV=development
APP_URL=http://localhost:8080
API_BASE_URL=http://localhost:3001
AUTH_CALLBACK_URL=http://localhost:8080/auth/callback
CORS_ORIGIN=http://localhost:8080
```

#### Production Environment
```bash
PORT=80
NODE_ENV=production
APP_URL=https://roofers.llc
API_BASE_URL=https://api.roofers.llc
AUTH_CALLBACK_URL=https://roofers.llc/auth/callback
CORS_ORIGIN=https://roofers.llc
```

## 3. No-Hardcoded URLs Policy

### Strict Guidelines
1. **All URL references must use the environment configuration module**
2. **Use relative paths for API endpoints** (e.g., `/api/users` instead of `http://localhost:3001/users`)
3. **API client must automatically use correct base URL** from environment config
4. **Vite development server proxy settings** handle API requests properly
5. **Server-side CORS configuration** reads allowed origins from environment variables

### Implementation Examples

#### ✅ CORRECT - Using Environment Configuration
```typescript
import { config } from '@/config/environment';

// In React components
const handleSubmit = async (data: FormData) => {
  const response = await fetch(`${config.apiBaseUrl}/api/submit`, {
    method: 'POST',
    body: data,
  });
};

// In API client
export const apiClient = {
  baseURL: config.apiBaseUrl,
  get: (path: string) => fetch(`${config.apiBaseUrl}${path}`),
  post: (path: string, data: any) => fetch(`${config.apiBaseUrl}${path}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  }),
};
```

#### ❌ INCORRECT - Hardcoded URLs
```typescript
// Never do this
const response = await fetch('http://localhost:3001/api/submit', {
  method: 'POST',
  body: data,
});

// Or this
const API_BASE = 'http://localhost:3001';
```

## 4. Implementation Patterns

### Accessing Environment Configuration in React Components
```typescript
import { config } from '@/config/environment';

export function MyComponent() {
  const handleRedirect = () => {
    window.location.href = `${config.appUrl}/dashboard`;
  };

  return (
    <div>
      <p>Environment: {config.nodeEnv}</p>
      <button onClick={handleRedirect}>Go to Dashboard</button>
    </div>
  );
}
```

### API Client Implementation
```typescript
// src/lib/api-client.ts
import { config } from '@/config/environment';

class ApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = config.apiBaseUrl;
  }

  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    return response.json();
  }

  get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint);
  }

  post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const apiClient = new ApiClient();
```

### Server Manager Implementation
The `ServerManager` class handles port conflicts and server management:

```typescript
import { ServerManager } from '@/lib/server-manager';
import config from '@/config/environment';

// Start server with automatic port resolution
await ServerManager.startServer(config.port);
const { port } = ServerManager.getStatus();
```

### Vite Configuration for API Proxying
```typescript
// vite.config.ts
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    server: {
      port: parseInt(env.PORT || '8080'),
      proxy: {
        '/api': {
          target: env.API_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
  };
});
```

## 5. Port Configuration

### Default Port Change
The development server port has been changed from **5173** to **8080** to avoid conflicts and provide consistency.

### Files Updated for Port Change
- `vite.config.ts`: Server port configuration
- `.env.example`: Default port value
- `src/config/environment.ts`: Default port handling
- `scripts/start-dev.ts`: Development server startup
- Documentation references

### Port Conflict Resolution
The `ServerManager` class automatically handles port conflicts:
1. Checks if the configured port is available
2. If occupied, finds the next available port
3. Updates environment variables accordingly
4. Provides clear console output about port changes

**Important**: If port 8080 is in use, it's likely another instance of the project is running. To avoid multiple instances:

```bash
# Check what's using port 8080
netstat -ano | findstr :8080

# Kill the process (replace PID with actual process ID)
taskkill /PID <PID> /F

# Then start the server normally
npm run start
```

This ensures only one instance runs on the intended port 8080.

## 6. Development Workflow

### Setup Instructions
```bash
# 1. Copy environment template
cp .env.example .env

# 2. Edit environment variables for your setup
nano .env

# 3. Install dependencies
npm install

# 4. Start development server (now on port 8080)
npm run dev

# 5. Access application
open http://localhost:8080
```

### Build and Deployment
```bash
# Development build
npm run build:dev

# Production build
npm run build:prod

# Preview production build
npm run preview
```

### Environment Validation
The application validates environment configuration on startup:
- Missing required variables throw clear error messages
- Invalid URLs are detected and reported
- Port conflicts are automatically resolved
- Configuration is logged in development mode

## 7. Security Considerations

### Environment Variable Security
1. **Never commit `.env` files** containing sensitive data
2. **Use different API keys** for each environment
3. **Restrict CORS origins** to known domains
4. **Enable HTTPS** in production
5. **Validate all environment variables** on startup
6. **Use strong authentication secrets** in production

### CORS Configuration
```typescript
// Backend CORS configuration (for future CRM integration)
const corsOptions = {
  origin: config.corsOrigins,
  credentials: true,
  optionsSuccessStatus: 200,
};
```

## 8. Troubleshooting

### Port Conflicts
```bash
# Check if port is in use (Windows)
netstat -ano | findstr :8080

# Check if port is in use (macOS/Linux)
lsof -i :8080

# Kill process using port
taskkill /PID <PID> /F  # Windows
kill -9 <PID>           # macOS/Linux
```

### Environment Variable Issues
```bash
# Verify environment variables are loaded
npm run dev -- --debug

# Check current configuration
node -e "console.log(process.env)"

# Validate environment configuration
npm run typecheck
```

### Common Issues and Solutions

#### Issue: "Missing required environment variable"
**Solution**: Ensure all required variables are defined in your `.env` file

#### Issue: "Invalid URL for [field]"
**Solution**: Check that URLs are properly formatted with protocol (http:// or https://)

#### Issue: "Port already in use"
**Solution**: The ServerManager will automatically find an available port, or manually kill the process using the port

## 9. Future Integration Preparation

### CRM Backend Integration
The environment configuration is prepared for future PHP/CodeIgniter CRM integration:

1. **API endpoints** are configured through environment variables
2. **CORS settings** support cross-origin requests
3. **Authentication flow** is ready for backend integration
4. **Database configuration** is prepared for CRM database connection

### Scalability Considerations
- Environment configuration supports multiple deployment environments
- API client is designed for easy backend integration
- Security settings are production-ready
- Performance optimizations are environment-aware

## 10. Best Practices Summary

### For Developers
1. **Always use the environment configuration module** for any URL or configuration value
2. **Never hardcode URLs, ports, or environment-specific values**
3. **Test in multiple environments** before deploying
4. **Validate environment variables** in your code
5. **Use the API client** for all backend communication
6. **Follow the established patterns** for consistency

### For Deployment
1. **Set up environment variables** before deployment
2. **Use environment-specific configuration files**
3. **Validate configuration** in each environment
4. **Monitor for configuration errors** in production
5. **Keep sensitive data secure** and never commit to version control

This comprehensive guide ensures consistent, secure, and maintainable environment configuration across all development and deployment scenarios.