import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'full' | 'icon' | 'text';
  className?: string;
  showText?: boolean;
}

export default function Logo({ 
  size = 'md', 
  variant = 'full', 
  className = '', 
  showText = true 
}: LogoProps) {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  const LogoIcon = () => (
    <svg
      className={`${sizeClasses[size]} ${className}`}
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Roof Shape */}
      <path
        d="M32 8L8 28H16V52H48V28H56L32 8Z"
        fill="currentColor"
        className="text-blue-600"
      />
      
      {/* Roof Ridge */}
      <path
        d="M32 8L8 28H12L32 12L52 28H56L32 8Z"
        fill="currentColor"
        className="text-blue-700"
      />
      
      {/* House Body */}
      <rect
        x="16"
        y="28"
        width="32"
        height="24"
        fill="currentColor"
        className="text-gray-100"
        stroke="currentColor"
        strokeWidth="1"
      />
      
      {/* Door */}
      <rect
        x="28"
        y="40"
        width="8"
        height="12"
        fill="currentColor"
        className="text-blue-800"
      />
      
      {/* Windows */}
      <rect
        x="20"
        y="32"
        width="6"
        height="6"
        fill="currentColor"
        className="text-blue-300"
      />
      <rect
        x="38"
        y="32"
        width="6"
        height="6"
        fill="currentColor"
        className="text-blue-300"
      />
      
      {/* Roof Shingles Detail */}
      <path
        d="M12 28L32 12L52 28L48 24L32 12L16 24L12 28Z"
        fill="currentColor"
        className="text-blue-800"
        opacity="0.3"
      />
      
      {/* Chimney */}
      <rect
        x="40"
        y="16"
        width="4"
        height="8"
        fill="currentColor"
        className="text-red-600"
      />
      
      {/* Chimney Cap */}
      <rect
        x="39"
        y="15"
        width="6"
        height="2"
        fill="currentColor"
        className="text-red-700"
      />
    </svg>
  );

  if (variant === 'icon') {
    return <LogoIcon />;
  }

  if (variant === 'text') {
    return (
      <span className={`font-bold ${textSizeClasses[size]} text-blue-600 ${className}`}>
        Roofers
      </span>
    );
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <LogoIcon />
      {showText && (
        <span className={`font-bold ${textSizeClasses[size]} text-blue-600`}>
          Roofers
        </span>
      )}
    </div>
  );
}

// Export individual components for flexibility
export const LogoIcon = ({ size = 'md', className = '' }: Pick<LogoProps, 'size' | 'className'>) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  return (
    <svg
      className={`${sizeClasses[size]} ${className}`}
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Roof Shape */}
      <path
        d="M32 8L8 28H16V52H48V28H56L32 8Z"
        fill="currentColor"
        className="text-blue-600"
      />
      
      {/* Roof Ridge */}
      <path
        d="M32 8L8 28H12L32 12L52 28H56L32 8Z"
        fill="currentColor"
        className="text-blue-700"
      />
      
      {/* House Body */}
      <rect
        x="16"
        y="28"
        width="32"
        height="24"
        fill="currentColor"
        className="text-gray-100"
        stroke="currentColor"
        strokeWidth="1"
      />
      
      {/* Door */}
      <rect
        x="28"
        y="40"
        width="8"
        height="12"
        fill="currentColor"
        className="text-blue-800"
      />
      
      {/* Windows */}
      <rect
        x="20"
        y="32"
        width="6"
        height="6"
        fill="currentColor"
        className="text-blue-300"
      />
      <rect
        x="38"
        y="32"
        width="6"
        height="6"
        fill="currentColor"
        className="text-blue-300"
      />
      
      {/* Roof Shingles Detail */}
      <path
        d="M12 28L32 12L52 28L48 24L32 12L16 24L12 28Z"
        fill="currentColor"
        className="text-blue-800"
        opacity="0.3"
      />
      
      {/* Chimney */}
      <rect
        x="40"
        y="16"
        width="4"
        height="8"
        fill="currentColor"
        className="text-red-600"
      />
      
      {/* Chimney Cap */}
      <rect
        x="39"
        y="15"
        width="6"
        height="2"
        fill="currentColor"
        className="text-red-700"
      />
    </svg>
  );
};

export const LogoText = ({ size = 'md', className = '' }: Pick<LogoProps, 'size' | 'className'>) => {
  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  return (
    <span className={`font-bold ${textSizeClasses[size]} text-blue-600 ${className}`}>
      Roofers
    </span>
  );
};
