import { useLocation } from 'react-router-dom';
import EstimateForm from '@/components/forms/EstimateForm';

const RequestEstimate = () => {
  const location = useLocation();
  const { serviceType = 'General Roofing' } = location.state || {};

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-2">Request Free Estimate</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Fill out the form below to receive a free, no-obligation estimate for your {serviceType.toLowerCase()} project.
          </p>
        </div>
        
        <EstimateForm serviceType={serviceType} />
      </div>
    </div>
  );
};

export default RequestEstimate;