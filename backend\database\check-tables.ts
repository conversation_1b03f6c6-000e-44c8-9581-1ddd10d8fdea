import mysql, { RowDataPacket, FieldPacket } from 'mysql2/promise'
import * as dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

// Load environment variables
dotenv.config()

// Get current file path in ESM
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

interface TableRow extends RowDataPacket {
  [key: string]: unknown
  Tables_in_roofers: string
}

interface ColumnInfo extends RowDataPacket {
  Field: string
  Type: string
  Null: string
  Key: string
  Default: string | null
  Extra: string
}

interface MigrationRecord extends RowDataPacket {
  id: string
  name: string
  executed_at: Date
}

async function checkTables() {
  let pool
  try {
    console.log('Creating database connection pool...')
    console.log('Database config:', {
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      database: process.env.DATABASE_NAME,
      port: process.env.DATABASE_PORT,
    })

    // Create connection pool
    pool = mysql.createPool({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
      waitForConnections: true,
      connectionLimit: 1,
      queueLimit: 0,
    })

    // Test connection
    console.log('Testing connection...')
    await pool.getConnection()
    console.log('Connection successful')

    // Get list of tables
    console.log('\nQuerying database structure...')
    const [tables] = await pool.execute('SHOW TABLES') as [TableRow[], FieldPacket[]]
    
    if (tables.length === 0) {
      console.log('No tables found in database')
      return
    }

    console.log('\nAvailable tables:')
    const tableNames = tables.map(row => row.Tables_in_roofers)
    console.log(tableNames.join('\n'))

    // Check each table structure
    for (const table of tables) {
      const tableName = table.Tables_in_roofers
      console.log(`\nStructure for table: ${tableName}`)
      try {
        const [columns] = await pool.execute(`DESCRIBE ${tableName}`) as [ColumnInfo[], FieldPacket[]]
        console.log('Columns:')
        columns.forEach(col => {
          console.log(`  ${col.Field} (${col.Type})${col.Key ? ` [${col.Key}]` : ''}${col.Null === 'YES' ? ' NULL' : ' NOT NULL'}${col.Default ? ` DEFAULT ${col.Default}` : ''}`)
        })
      } catch (error) {
        console.error(`Error describing table ${tableName}:`, error)
      }
    }

    // Check migrations status
    try {
      const [migrations] = await pool.execute(
        'SELECT * FROM migrations ORDER BY executed_at'
      ) as [MigrationRecord[], FieldPacket[]]
      
      console.log('\nExecuted migrations:')
      if (migrations.length === 0) {
        console.log('No migrations found')
      } else {
        migrations.forEach(migration => {
          console.log(`  ${migration.name} (${migration.executed_at.toISOString()})`)
        })
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error querying migrations:', error.message)
      } else {
        console.error('Error querying migrations:', error)
      }
    }

  } catch (error) {
    if (error instanceof Error) {
      console.error('Database connection error:', error.message)
      console.error('Stack trace:', error.stack)
    } else {
      console.error('Database connection error:', error)
    }
    throw error
  } finally {
    if (pool) {
      console.log('\nClosing database connection...')
      await pool.end().catch(err => console.error('Error closing connection:', err))
      console.log('Connection closed')
    }
  }
}

// Run check if executed directly
if (import.meta.url.endsWith(process.argv[1])) {
  checkTables()
    .then(() => {
      console.log('\nCheck complete')
      process.exit(0)
    })
    .catch(error => {
      console.error('\nCheck failed:', error)
      process.exit(1)
    })
}