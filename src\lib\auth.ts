import { Auth, type AuthConfig } from '@auth/core'
import type { Session, User, Account, Profile } from '@auth/core/types'
import { DrizzleAdapter } from '@auth/drizzle-adapter'
import Google from '@auth/core/providers/google'
import Credentials from '@auth/core/providers/credentials'
import { db } from './db/connection'
import { users, userRoles, roles } from './db/schema'
import { eq } from 'drizzle-orm'
import { config } from '../config/environment'

// Extend Session type to include roles
interface ExtendedSession extends Session {
  user?: {
    id: string
    roles: string[]
  } & Session['user']
}

// Extend User type to include roles
interface ExtendedUser extends User {
  id: string
  roles?: string[]
}

// Auth configuration
const authConfig: AuthConfig = {
  adapter: DrizzleAdapter(db),
  trustHost: true, // Required for middleware authentication
  providers: [
    Google({
      clientId: process.env.GOOGLE_ID || '',
      clientSecret: process.env.GOOGLE_SECRET || '',
    }),
    Credentials({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find user by email
          const user = await db
            .select()
            .from(users)
            .where(eq(users.email, credentials.email as string))
            .limit(1);

          if (!user.length) {
            return null;
          }

          // For development, allow simple password check
          // In production, use proper password hashing
          if (credentials.password === 'admin123') {
            return {
              id: user[0].id,
              email: user[0].email,
              name: user[0].name,
              image: user[0].image,
            };
          }

          return null;
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;

        // Fetch user roles
        try {
          const userWithRoles = await db
            .select({
              roleName: roles.name,
            })
            .from(userRoles)
            .innerJoin(roles, eq(userRoles.roleId, roles.id))
            .where(eq(userRoles.userId, user.id));

          token.roles = userWithRoles.map(r => r.roleName);
        } catch (error) {
          console.error('Error fetching user roles:', error);
          token.roles = [];
        }
      }
      return token;
    },
    async session({ session, token }): Promise<ExtendedSession> {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.roles = token.roles as string[] || [];
      }
      return session;
    },
    async signIn({ user, account, profile, isNewUser }): Promise<boolean> {
      // Allow sign in
      return true;
    },
  },
  events: {
    async signIn({ user, account, profile, isNewUser }) {
      // Log sign in event
      console.log(`User signed in: ${user.email}`);

      // If new user and using OAuth, assign default role
      if (isNewUser && account?.provider !== 'credentials') {
        try {
          // Find the 'viewer' role
          const viewerRole = await db
            .select()
            .from(roles)
            .where(eq(roles.name, 'viewer'))
            .limit(1);

          if (viewerRole.length > 0) {
            // Assign viewer role to new user
            await db.insert(userRoles).values({
              userId: user.id!,
              roleId: viewerRole[0].id,
            });
          }
        } catch (error) {
          console.error('Error assigning default role:', error);
        }
      }
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
    signOut: '/auth/signout',
  },
  debug: config.nodeEnv === 'development',
}

const authHandler = Auth(authConfig)

// Export the auth handler
export { authHandler as auth }

// Helper functions for role checking
export function hasRole(userRoles: string[], requiredRole: string): boolean {
  return userRoles.includes(requiredRole);
}

export function hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
  return requiredRoles.some(role => userRoles.includes(role));
}

export function canEditPages(userRoles: string[]): boolean {
  return hasAnyRole(userRoles, ['admin', 'editor']);
}

export function canManageUsers(userRoles: string[]): boolean {
  return hasRole(userRoles, 'admin');
}

// Export types
export type { ExtendedSession as Session, ExtendedUser as User }