import { Auth, type AuthConfig } from '@auth/core'
import type { Session, User, Account, Profile } from '@auth/core/types'
import Google from '@auth/core/providers/google'
import mysql from 'mysql2/promise'
import { config } from '../config/environment'

// MySQL connection pool for the auth adapter
const pool = mysql.createPool({
  host: process.env.DATABASE_HOST,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  port: parseInt(process.env.DATABASE_PORT || '3306'),
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
})

// Extend Session type to include role
interface ExtendedSession extends Session {
  user?: {
    role?: string
  } & Session['user']
}

// Extend User type to include role
interface ExtendedUser extends User {
  role?: string
}

// Auth configuration
const authHandler = Auth({
  trustHost: true, // Required for middleware authentication
  providers: [
    Google({
      clientId: process.env.GOOGLE_ID || '',
      clientSecret: process.env.GOOGLE_SECRET || '',
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async session({ session, user }: { session: ExtendedSession; user: ExtendedUser }): Promise<ExtendedSession> {
      if (session.user && user.role) {
        session.user.role = user.role
      }
      return session
    },
    async signIn({ user, account, profile }: { 
      user: ExtendedUser;
      account: Account | null;
      profile?: Profile;
    }): Promise<boolean> {
      // Add custom sign in validation if needed
      const isAllowed = true // Add your validation logic here
      if (!isAllowed) {
        console.log('User not allowed:', user.email)
      }
      return isAllowed
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
    signOut: '/auth/signout',
  },
  debug: config.nodeEnv === 'development',
})

// Export the auth handler
export { authHandler as auth }

// Export types
export type { ExtendedSession as Session, ExtendedUser as User }