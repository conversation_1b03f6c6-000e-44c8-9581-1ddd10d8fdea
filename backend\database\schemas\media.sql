-- Media Management Database Schema
-- Roofers LLC - Media Gallery System

-- Media Files Table
CREATE TABLE IF NOT EXISTS media_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_type ENUM('image', 'video', 'document', 'other') NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL, -- Size in bytes
    width INT NULL, -- For images/videos
    height INT NULL, -- For images/videos
    duration INT NULL, -- For videos (in seconds)
    alt_text TEXT NULL, -- For accessibility
    caption TEXT NULL,
    description TEXT NULL,
    uploaded_by INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
    metadata JSON NULL, -- Additional metadata (EXIF, etc.)
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_upload_date (upload_date),
    INDEX idx_status (status),
    INDEX idx_is_public (is_public),
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Media Categories Table
CREATE TABLE IF NOT EXISTS media_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (parent_id) REFERENCES media_categories(id) ON DELETE SET NULL
);

-- Media File Categories Junction Table
CREATE TABLE IF NOT EXISTS media_file_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    media_file_id INT NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_file_category (media_file_id, category_id),
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES media_categories(id) ON DELETE CASCADE
);

-- Media Tags Table
CREATE TABLE IF NOT EXISTS media_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    slug VARCHAR(50) NOT NULL UNIQUE,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_usage_count (usage_count)
);

-- Media File Tags Junction Table
CREATE TABLE IF NOT EXISTS media_file_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    media_file_id INT NOT NULL,
    tag_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_file_tag (media_file_id, tag_id),
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES media_tags(id) ON DELETE CASCADE
);

-- Media Usage Tracking Table
CREATE TABLE IF NOT EXISTS media_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    media_file_id INT NOT NULL,
    used_in_type ENUM('page', 'post', 'component', 'email', 'other') NOT NULL,
    used_in_id VARCHAR(100) NOT NULL, -- Page path, post ID, etc.
    used_in_title VARCHAR(255) NULL,
    usage_context VARCHAR(100) NULL, -- hero, gallery, thumbnail, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_media_file_id (media_file_id),
    INDEX idx_used_in_type (used_in_type),
    INDEX idx_used_in_id (used_in_id),
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE
);

-- Media Access Log Table
CREATE TABLE IF NOT EXISTS media_access_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    media_file_id INT NOT NULL,
    access_type ENUM('view', 'download', 'embed') NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    referer VARCHAR(500) NULL,
    access_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_media_file_id (media_file_id),
    INDEX idx_access_type (access_type),
    INDEX idx_access_date (access_date),
    INDEX idx_user_id (user_id),
    FOREIGN KEY (media_file_id) REFERENCES media_files(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert Default Categories
INSERT INTO media_categories (name, slug, description, sort_order) VALUES
('Images', 'images', 'All image files including photos, graphics, and illustrations', 1),
('Documents', 'documents', 'PDF files, brochures, contracts, and other documents', 2),
('Videos', 'videos', 'Video files including promotional content and tutorials', 3),
('Logos & Branding', 'logos-branding', 'Company logos, brand assets, and marketing materials', 4),
('Project Photos', 'project-photos', 'Before and after photos of roofing projects', 5),
('Certificates', 'certificates', 'Insurance certificates, licenses, and certifications', 6),
('Marketing Materials', 'marketing-materials', 'Flyers, brochures, and promotional content', 7);

-- Insert Default Tags
INSERT INTO media_tags (name, slug) VALUES
('roofing', 'roofing'),
('residential', 'residential'),
('commercial', 'commercial'),
('repair', 'repair'),
('installation', 'installation'),
('maintenance', 'maintenance'),
('emergency', 'emergency'),
('metal', 'metal'),
('shingle', 'shingle'),
('tpo', 'tpo'),
('before', 'before'),
('after', 'after'),
('hero', 'hero'),
('gallery', 'gallery'),
('thumbnail', 'thumbnail'),
('logo', 'logo'),
('certificate', 'certificate'),
('license', 'license'),
('insurance', 'insurance'),
('marketing', 'marketing');

-- Create Views for Easy Querying

-- Active Media Files View
CREATE VIEW active_media_files AS
SELECT 
    mf.*,
    u.name as uploaded_by_name,
    GROUP_CONCAT(DISTINCT mc.name) as categories,
    GROUP_CONCAT(DISTINCT mt.name) as tags
FROM media_files mf
LEFT JOIN users u ON mf.uploaded_by = u.id
LEFT JOIN media_file_categories mfc ON mf.id = mfc.media_file_id
LEFT JOIN media_categories mc ON mfc.category_id = mc.id
LEFT JOIN media_file_tags mft ON mf.id = mft.media_file_id
LEFT JOIN media_tags mt ON mft.tag_id = mt.id
WHERE mf.status = 'active'
GROUP BY mf.id;

-- Media Statistics View
CREATE VIEW media_statistics AS
SELECT 
    COUNT(*) as total_files,
    SUM(file_size) as total_size,
    COUNT(CASE WHEN file_type = 'image' THEN 1 END) as image_count,
    COUNT(CASE WHEN file_type = 'video' THEN 1 END) as video_count,
    COUNT(CASE WHEN file_type = 'document' THEN 1 END) as document_count,
    COUNT(CASE WHEN file_type = 'other' THEN 1 END) as other_count,
    AVG(file_size) as average_file_size,
    MAX(upload_date) as last_upload_date
FROM media_files 
WHERE status = 'active';

-- Popular Media View
CREATE VIEW popular_media AS
SELECT
    mf.*,
    COUNT(mal.id) as access_count,
    COUNT(CASE WHEN mal.access_type = 'download' THEN 1 END) as download_count,
    COUNT(CASE WHEN mal.access_type = 'view' THEN 1 END) as view_count
FROM media_files mf
LEFT JOIN media_access_log mal ON mf.id = mal.media_file_id
WHERE mf.status = 'active'
GROUP BY mf.id
ORDER BY access_count DESC;

-- Dynamic Pages Management Schema

-- Page Templates Table
CREATE TABLE IF NOT EXISTS page_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT NULL,
    schema_config JSON NOT NULL, -- Puck schema configuration
    default_data JSON NULL, -- Default page data
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_is_active (is_active)
);

-- Dynamic Pages Table
CREATE TABLE IF NOT EXISTS pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    path VARCHAR(500) NOT NULL UNIQUE,
    template_id INT NULL,
    meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    meta_keywords TEXT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    is_homepage BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_by INT NOT NULL,
    updated_by INT NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_path (path),
    INDEX idx_status (status),
    INDEX idx_template_id (template_id),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (template_id) REFERENCES page_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Page Content Table (stores Puck data)
CREATE TABLE IF NOT EXISTS page_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    content_data JSON NOT NULL, -- Puck content data
    version INT DEFAULT 1,
    is_current BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_version (version),
    INDEX idx_is_current (is_current),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Page Schemas Table (stores custom Puck schemas)
CREATE TABLE IF NOT EXISTS page_schemas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    schema_config JSON NOT NULL, -- Custom Puck schema for this page
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Page Navigation Table
CREATE TABLE IF NOT EXISTS page_navigation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_id INT NOT NULL,
    parent_id INT NULL,
    navigation_label VARCHAR(100) NOT NULL,
    navigation_order INT DEFAULT 0,
    is_visible BOOLEAN DEFAULT TRUE,
    is_in_main_menu BOOLEAN DEFAULT FALSE,
    is_in_footer BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_id (page_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_navigation_order (navigation_order),
    INDEX idx_is_visible (is_visible),
    INDEX idx_is_in_main_menu (is_in_main_menu),
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES page_navigation(id) ON DELETE SET NULL
);

-- Insert Default Page Templates
INSERT INTO page_templates (name, slug, description, schema_config, default_data) VALUES
('Service Page', 'service-page', 'Template for service-related pages',
 '{"components": {"ServiceHero": {}, "ServiceFeatures": {}, "ServiceCTA": {}}}',
 '{"content": [{"type": "ServiceHero", "props": {"title": "Our Service", "subtitle": "Professional service description"}}]}'),

('Landing Page', 'landing-page', 'Template for marketing landing pages',
 '{"components": {"LandingHero": {}, "LandingFeatures": {}, "LandingTestimonials": {}, "LandingCTA": {}}}',
 '{"content": [{"type": "LandingHero", "props": {"title": "Welcome", "subtitle": "Your landing page"}}]}'),

('Content Page', 'content-page', 'Template for general content pages',
 '{"components": {"ContentHero": {}, "ContentSection": {}, "ContentCTA": {}}}',
 '{"content": [{"type": "ContentHero", "props": {"title": "Content Page", "subtitle": "General content"}}]}'),

('Gallery Page', 'gallery-page', 'Template for image galleries',
 '{"components": {"GalleryHero": {}, "GalleryGrid": {}, "GalleryCTA": {}}}',
 '{"content": [{"type": "GalleryHero", "props": {"title": "Gallery", "subtitle": "View our work"}}]}');

-- Insert Current Static Pages into Dynamic System
INSERT INTO pages (title, slug, path, status, is_homepage, created_by, published_at) VALUES
('Home Page', 'home', '/', 'published', TRUE, 1, NOW()),
('About Us', 'about', '/about', 'published', FALSE, 1, NOW()),
('Contact', 'contact', '/contact', 'published', FALSE, 1, NOW()),
('Residential Roofing', 'residential-roofing', '/residential-roofing', 'published', FALSE, 1, NOW()),
('Commercial Roofing', 'commercial-roofing', '/commercial-roofing', 'published', FALSE, 1, NOW()),
('Emergency Roofing', 'emergency-roofing', '/emergency-roofing', 'published', FALSE, 1, NOW()),
('Roof Repair', 'roof-repair', '/roof-repair', 'published', FALSE, 1, NOW()),
('Metal Roofing', 'metal-roofing', '/metal-roofing', 'published', FALSE, 1, NOW()),
('Shingle Roofing', 'shingle-roofing', '/shingle-roofing', 'published', FALSE, 1, NOW()),
('TPO Roofing', 'tpo-roofing', '/tpo-roofing', 'published', FALSE, 1, NOW()),
('Gallery', 'gallery', '/gallery', 'published', FALSE, 1, NOW()),
('Areas We Serve', 'areas-we-serve', '/areas-we-serve', 'published', FALSE, 1, NOW()),
('Financing', 'financing', '/financing', 'published', FALSE, 1, NOW());

-- Create Views for Easy Querying

-- Published Pages View
CREATE VIEW published_pages AS
SELECT
    p.*,
    pt.name as template_name,
    pc.content_data,
    pc.version as content_version,
    u1.name as created_by_name,
    u2.name as updated_by_name
FROM pages p
LEFT JOIN page_templates pt ON p.template_id = pt.id
LEFT JOIN page_content pc ON p.id = pc.page_id AND pc.is_current = TRUE
LEFT JOIN users u1 ON p.created_by = u1.id
LEFT JOIN users u2 ON p.updated_by = u2.id
WHERE p.status = 'published';

-- Navigation Menu View
CREATE VIEW navigation_menu AS
SELECT
    pn.*,
    p.title as page_title,
    p.path as page_path,
    p.status as page_status
FROM page_navigation pn
JOIN pages p ON pn.page_id = p.id
WHERE pn.is_visible = TRUE AND p.status = 'published'
ORDER BY pn.navigation_order;
