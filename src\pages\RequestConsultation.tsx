import { FC } from 'react';
import { useLocation } from 'react-router-dom';
import ConsultationForm from '@/components/forms/ConsultationForm';

const RequestConsultation: FC = () => {
  const location = useLocation();
  const { serviceType } = location.state || { serviceType: 'Roofing Service' };

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <h1 className="text-4xl font-bold text-center mb-2">Request a Free Consultation</h1>
        <p className="text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto">
          Fill out the form below and our team will get back to you within 24 hours to schedule your consultation.
        </p>
        <ConsultationForm serviceType={serviceType} />
      </div>
    </div>
  );
};

export default RequestConsultation;