import { config } from '@/config/environment';

export interface SocialPost {
  id: string;
  platform: 'linkedin' | 'facebook' | 'instagram' | 'twitter';
  content: string;
  imageUrl?: string;
  hashtags: string[];
  scheduledTime: string;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  createdAt: string;
  publishedAt?: string;
  engagement?: {
    likes: number;
    shares: number;
    comments: number;
  };
}

export interface ContentTemplate {
  id: string;
  type: 'tip' | 'article' | 'promotion' | 'emergency' | 'seasonal';
  platform: string;
  promptTemplate: string;
  hashtags: string[];
  targetAudience: string;
}

export interface SocialAccount {
  platform: string;
  accountId: string;
  accessToken: string;
  refreshToken?: string;
  isActive: boolean;
  lastSync: string;
}

export const CONTENT_TEMPLATES: ContentTemplate[] = [
  {
    id: 'linkedin-industry-trend',
    type: 'article',
    platform: 'linkedin',
    promptTemplate: 'Write a professional LinkedIn article about {topic} in the roofing industry. Include industry insights, best practices, and actionable advice for property owners. Keep it informative and authoritative.',
    hashtags: ['#RoofingIndustry', '#Construction', '#PropertyMaintenance', '#ProfessionalServices'],
    targetAudience: 'Property owners, facility managers, construction professionals'
  },
  {
    id: 'facebook-maintenance-tip',
    type: 'tip',
    platform: 'facebook',
    promptTemplate: 'Create a friendly Facebook post with a practical roofing maintenance tip about {topic}. Make it conversational and helpful for homeowners. Include a call-to-action.',
    hashtags: ['#RoofMaintenance', '#HomeOwnerTips', '#RoofCare', '#PreventiveMaintenance'],
    targetAudience: 'Homeowners, property owners'
  },
  {
    id: 'instagram-visual-content',
    type: 'promotion',
    platform: 'instagram',
    promptTemplate: 'Write an engaging Instagram caption for a roofing project photo showing {topic}. Include storytelling elements, project details, and relevant hashtags. Keep it visual and inspiring.',
    hashtags: ['#RoofingProject', '#QualityWork', '#ProfessionalRoofing', '#HomeImprovement', '#Construction'],
    targetAudience: 'Homeowners, design enthusiasts, local community'
  },
  {
    id: 'twitter-quick-tip',
    type: 'tip',
    platform: 'twitter',
    promptTemplate: 'Create a concise Twitter post with a quick roofing tip about {topic}. Keep it under 280 characters, actionable, and include relevant hashtags.',
    hashtags: ['#RoofingTip', '#HomeMaintenance', '#RoofCare'],
    targetAudience: 'General public, homeowners'
  },
  {
    id: 'emergency-alert',
    type: 'emergency',
    platform: 'all',
    promptTemplate: 'Create an urgent but professional social media post about {topic} emergency roofing services. Emphasize availability, quick response, and safety. Include contact information.',
    hashtags: ['#EmergencyRoofing', '#StormDamage', '#24HourService', '#RoofRepair'],
    targetAudience: 'Property owners in emergency situations'
  }
];

export const ROOFING_TOPICS = {
  maintenance: [
    'gutter cleaning and inspection',
    'seasonal roof preparation',
    'identifying early signs of damage',
    'proper attic ventilation',
    'ice dam prevention'
  ],
  materials: [
    'choosing the right roofing material',
    'metal vs shingle roofing comparison',
    'TPO membrane benefits',
    'sustainable roofing options',
    'energy-efficient roofing solutions'
  ],
  seasonal: [
    'winter roof protection strategies',
    'spring roof inspection checklist',
    'summer heat damage prevention',
    'fall preparation for winter weather',
    'storm damage assessment'
  ],
  business: [
    'roofing industry trends and innovations',
    'commercial roofing best practices',
    'roof warranty and insurance considerations',
    'choosing a reliable roofing contractor',
    'roofing project planning and budgeting'
  ]
};

class SocialMediaService {
  private posts: SocialPost[] = [];
  private templates: ContentTemplate[] = CONTENT_TEMPLATES;
  private accounts: SocialAccount[] = [];

  async generateContent(template: ContentTemplate, topic: string): Promise<string> {
    try {
      // In a real app, this would call OpenAI or another AI service
      const prompt = template.promptTemplate.replace('{topic}', topic);
      
      // Mock AI content generation
      const mockContent = await this.generateMockContent(template.platform, template.type, topic);
      
      return mockContent;
    } catch (error) {
      console.error('Error generating content:', error);
      throw new Error('Failed to generate content');
    }
  }

  private async generateMockContent(platform: string, type: string, topic: string): Promise<string> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const contentMap: Record<string, Record<string, string>> = {
      linkedin: {
        article: `🏠 The Future of ${topic} in Modern Construction

As roofing professionals, we're seeing significant changes in how property owners approach ${topic}. Here are three key trends every property manager should know:

1. Technology Integration: Smart monitoring systems are revolutionizing how we detect and prevent issues
2. Sustainable Solutions: Eco-friendly materials are becoming the standard, not the exception  
3. Preventive Maintenance: Proactive care saves 60% more than reactive repairs

At Roofers LLC, we're committed to staying ahead of these trends to serve our clients better. What roofing innovations are you most excited about?

#RoofingIndustry #Construction #PropertyMaintenance #Innovation`,

        tip: `💡 Professional Insight: ${topic}

Here's what 20+ years in the roofing industry has taught us about ${topic}. This simple approach can save property owners thousands in unexpected repairs.

Key takeaway: Prevention is always more cost-effective than emergency fixes.

What questions do you have about ${topic}? Drop them in the comments!

#RoofingTips #PropertyMaintenance #ProfessionalAdvice`
      },
      facebook: {
        tip: `🏡 Homeowner Tip Tuesday! 

Did you know that proper ${topic} can extend your roof's life by 5-10 years? Here's a simple tip from our team:

${this.getSpecificTip(topic)}

Remember, a little maintenance goes a long way! If you notice any issues, don't wait – early intervention saves money and prevents bigger problems.

Need a professional inspection? We're here to help! Call us at (305) 376-1808 or visit our website.

#RoofMaintenance #HomeOwnerTips #RoofCare #Miami`,

        promotion: `✨ Another successful ${topic} project completed! 

We love seeing the transformation when we help families protect their most important investment. This project showcased our commitment to quality workmanship and customer satisfaction.

🔧 Professional installation
⚡ Quick turnaround  
💯 100% satisfaction guarantee
🛡️ 5-year workmanship warranty

Ready to upgrade your roof? Contact us for a free estimate!

#RoofingProject #QualityWork #CustomerSatisfaction #Miami`
      },
      instagram: {
        promotion: `Before ➡️ After magic! ✨

This ${topic} transformation shows what's possible when you choose quality over quick fixes. Our team takes pride in every detail, from initial assessment to final cleanup.

Swipe to see the incredible difference! 👉

What you get with Roofers LLC:
🏠 Expert craftsmanship
⏰ On-time completion
🛡️ Comprehensive warranty
💬 Clear communication throughout

Ready for your transformation? DM us or call (305) 376-1808

#RoofingProject #Transformation #QualityWork #ProfessionalRoofing #Miami #HomeImprovement #Construction #RoofersLLC`,

        tip: `Quick Tip Tuesday! 💡

${this.getSpecificTip(topic)}

Save this post for later! 📌

Questions about ${topic}? Drop them below! 👇

#RoofingTip #HomeMaintenance #RoofCare #TipTuesday #Miami`
      },
      twitter: {
        tip: `🏠 Quick Tip: ${this.getSpecificTip(topic)}

Prevention > Repair every time! 

#RoofingTip #HomeMaintenance #RoofCare`,

        emergency: `🚨 STORM ALERT: ${topic} emergency services available 24/7

✅ Immediate response
✅ Insurance assistance  
✅ Temporary protection

Call now: (305) 376-1808

#EmergencyRoofing #StormDamage #24HourService #Miami`
      }
    };

    return contentMap[platform]?.[type] || `Professional content about ${topic} for ${platform}`;
  }

  private getSpecificTip(topic: string): string {
    const tips: Record<string, string> = {
      'gutter cleaning': 'Clean gutters twice yearly - spring and fall. Clogged gutters can cause water damage and ice dams.',
      'roof inspection': 'Walk around your home monthly and look up. Missing shingles, sagging areas, or debris are red flags.',
      'attic ventilation': 'Check your attic temperature. If it\'s much hotter than outside, you need better ventilation.',
      'ice dam prevention': 'Keep gutters clean and ensure proper attic insulation to prevent ice dams.',
      'storm preparation': 'Trim overhanging branches and secure loose items before storms hit.',
      'material selection': 'Consider your climate, budget, and home style when choosing roofing materials.',
      'energy efficiency': 'Light-colored roofing reflects heat and can reduce cooling costs by 10-15%.'
    };

    return tips[topic] || `Regular maintenance of ${topic} prevents costly repairs and extends roof life.`;
  }

  async schedulePost(post: Omit<SocialPost, 'id' | 'createdAt' | 'status'>): Promise<SocialPost> {
    const newPost: SocialPost = {
      ...post,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      status: 'scheduled'
    };

    this.posts.push(newPost);
    return newPost;
  }

  async getScheduledPosts(): Promise<SocialPost[]> {
    return this.posts.filter(post => post.status === 'scheduled');
  }

  async publishPost(postId: string): Promise<boolean> {
    const post = this.posts.find(p => p.id === postId);
    if (!post) return false;

    try {
      // In a real app, this would call the social media platform APIs
      post.status = 'published';
      post.publishedAt = new Date().toISOString();
      return true;
    } catch (error) {
      post.status = 'failed';
      return false;
    }
  }

  getContentTemplates(): ContentTemplate[] {
    return this.templates;
  }

  getRoofingTopics() {
    return ROOFING_TOPICS;
  }
}

export const socialMediaService = new SocialMediaService();
