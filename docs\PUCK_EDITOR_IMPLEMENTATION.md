# Puck Editor Implementation Guide

## Overview

This document analyzes the successful Puck editor implementation in public pages and provides instructions for replicating this pattern in the admin routes to resolve rendering issues.

## Public Pages Puck Editor Analysis

### Successful Implementation Pattern

The public pages (like `PuckHome.tsx`, `About.tsx`, etc.) implement the Puck editor with a simple, effective pattern:

```tsx
if (isEditing) {
  return (
    <div style={{ 
      position: 'fixed',
      inset: 0,
      zIndex: 50,
      height: '100vh',
      width: '100vw'
    }}>
      <Puck
        config={config}
        data={data}
        onPublish={async (newData) => {
          setData(newData as PuckData);
          setIsEditing(false);
        }}
      />
    </div>
  );
}
```

### Key Success Factors

1. **Simple Container Structure**:
   - Fixed-position container covering the entire viewport
   - No parent constraints or complex CSS affecting the editor
   - Full height and width (`100vh` and `100vw`)

2. **Minimal Wrapping**:
   - Direct rendering of the `<Puck>` component
   - No nested containers or complex layout structures
   - No custom modifications to <PERSON><PERSON>'s internal behavior

3. **Clean Props**:
   - Only essential props: `config`, `data`, and `onPublish`
   - No custom header or additional UI elements
   - Simple state management

4. **No DOM Manipulation**:
   - Puck manages its own DOM structure without interference
   - No useEffect hooks modifying Puck's internal elements
   - No custom CSS targeting Puck's internal components

## Admin Implementation Issues

The current admin implementation in `ProtectedPuckEditor.tsx` has several issues:

1. **Complex Container Structure**:
   - Multiple nested containers with calculated heights
   - Parent constraints affecting the editor's layout
   - Custom CSS targeting Puck's internal elements

2. **Custom Sidebar Functionality**:
   - Custom sidebar toggle functionality interfering with Puck's built-in behavior
   - Complex CSS for controlling sidebar visibility and positioning
   - DOM manipulation via useEffect to modify Puck's internal structure

3. **Excessive Customization**:
   - Too many custom UI elements and controls
   - Complex state management for sidebar visibility
   - Overriding Puck's native behavior

## Implementation Instructions

To fix the admin implementation, follow these steps:

### 1. Replace the ProtectedPuckEditor Component

Replace the current `ProtectedPuckEditor.tsx` with this simplified version that directly replicates the public pages pattern:

```tsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Puck, type Data } from '@measured/puck';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { ArrowLeft, AlertTriangle } from 'lucide-react';
import { config } from '@/puck/schema';

interface ProtectedPuckEditorProps {
  pagePath: string;
  pageTitle: string;
  initialData?: Data;
}

export default function ProtectedPuckEditor({
  pagePath,
  pageTitle,
  initialData
}: ProtectedPuckEditorProps) {
  const { canEditPages } = useAuth();
  const navigate = useNavigate();
  const [data, setData] = useState<Data>(initialData || { content: [], root: {} });
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // Check permissions
  if (!canEditPages()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to edit pages. Please contact an administrator.
            </AlertDescription>
          </Alert>
          <Button 
            onClick={() => navigate('/admin')} 
            className="w-full mt-4"
            variant="outline"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </div>
      </div>
    );
  }

  const handleSave = async (newData: Data) => {
    setSaveStatus('saving');
    
    try {
      // Save to localStorage (or your actual save implementation)
      localStorage.setItem(`puck-data-${pagePath}`, JSON.stringify(newData));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setData(newData);
      setSaveStatus('saved');
      
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
      
      // Optional: Navigate back to admin dashboard after save
      // navigate('/admin');
    } catch (error) {
      console.error('Save error:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  // Add a small floating status indicator for save status
  const renderSaveStatus = () => {
    if (saveStatus === 'idle') return null;
    
    const statusClasses = {
      saving: 'bg-blue-500',
      saved: 'bg-green-500',
      error: 'bg-red-500'
    };
    
    const statusText = {
      saving: 'Saving...',
      saved: 'Saved!',
      error: 'Error saving'
    };
    
    return (
      <div className={`fixed top-4 right-4 z-[100] px-4 py-2 rounded-md text-white ${statusClasses[saveStatus]}`}>
        {statusText[saveStatus]}
      </div>
    );
  };

  // Add a back button that floats above the editor
  const renderBackButton = () => {
    return (
      <Button
        onClick={() => navigate('/admin')}
        className="fixed top-4 left-4 z-[100]"
        variant="secondary"
        size="sm"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Admin
      </Button>
    );
  };

  // Directly replicate the public pages pattern
  return (
    <div style={{ 
      position: 'fixed',
      inset: 0,
      zIndex: 50,
      height: '100vh',
      width: '100vw'
    }}>
      {renderBackButton()}
      {renderSaveStatus()}
      <Puck
        config={config}
        data={data}
        onPublish={handleSave}
        headerTitle={pageTitle}
        headerPath={pagePath}
      />
    </div>
  );
}
```

### 2. Key Implementation Details

1. **Direct Replication of Public Pages Pattern**:
   - Use the exact same container structure with `position: fixed` and `inset: 0`
   - Set explicit `height: 100vh` and `width: 100vw` to ensure full viewport coverage
   - Minimal wrapping around the Puck component

2. **Simplified Component Structure**:
   - Remove all custom sidebar toggle functionality
   - Remove all custom CSS for sidebar behavior
   - Let Puck handle its own layout and sidebar

3. **Maintain Admin-Specific Functionality**:
   - Add a floating back button to return to the admin dashboard
   - Add a floating status indicator for save operations
   - Preserve permission checking

4. **Clean Save Handler**:
   - Simplify the save handler to match the pattern in public pages
   - Add status indicators for better UX

### 3. Remove Custom CSS

Remove any custom CSS that targets Puck's internal elements, especially:
- CSS that modifies the sidebar behavior
- CSS that changes the layout of Puck's components
- Any DOM manipulation via useEffect that modifies Puck's structure

### 4. Testing

After implementation, test the following:
1. Editor loads correctly in admin routes
2. Sidebar opens and closes using Puck's built-in controls
3. Components can be added and edited
4. Changes can be saved
5. Navigation back to admin dashboard works

## Why This Approach Works

This approach works because:

1. **Simplicity**: Minimal wrapping and no custom modifications to Puck's internal behavior
2. **Full Viewport**: The editor gets the entire viewport to work with
3. **Clean Container**: No parent constraints or complex CSS affecting the editor
4. **No DOM Manipulation**: Puck manages its own DOM structure without interference
5. **Direct Rendering**: Clean props and minimal wrapper elements

By directly replicating the successful pattern from public pages, we resolve the rendering issues in the admin routes while maintaining the necessary admin-specific functionality.

## Additional Notes

- The Puck editor requires full viewport space to function correctly
- Avoid custom CSS that targets Puck's internal elements
- Let Puck manage its own layout and sidebar behavior
- Use floating UI elements instead of trying to integrate with Puck's header
- Keep the container structure as simple as possible