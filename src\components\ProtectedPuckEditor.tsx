import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Puck, type Data } from '@measured/puck';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { ArrowLeft, Save, Eye, AlertTriangle } from 'lucide-react';
import { config } from '@/puck/schema';

interface ProtectedPuckEditorProps {
  pagePath: string;
  pageTitle: string;
  initialData?: Data;
}

export default function ProtectedPuckEditor({ 
  pagePath, 
  pageTitle, 
  initialData 
}: ProtectedPuckEditorProps) {
  const { canEditPages } = useAuth();
  const navigate = useNavigate();
  const [data, setData] = useState<Data>(initialData || { content: [], root: {} });
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // Check permissions
  if (!canEditPages()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to edit pages. Please contact an administrator.
            </AlertDescription>
          </Alert>
          <Button 
            onClick={() => navigate('/admin')} 
            className="w-full mt-4"
            variant="outline"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </div>
      </div>
    );
  }

  const handleSave = async (newData: Data) => {
    setIsSaving(true);
    setSaveStatus('saving');
    
    try {
      // In a real implementation, this would save to a backend API
      // For now, we'll save to localStorage as a demo
      localStorage.setItem(`puck-data-${pagePath}`, JSON.stringify(newData));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setData(newData);
      setSaveStatus('saved');
      
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Save error:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePreview = () => {
    // Open the actual page in a new tab
    window.open(pagePath, '_blank');
  };

  const getSaveStatusMessage = () => {
    switch (saveStatus) {
      case 'saving':
        return 'Saving changes...';
      case 'saved':
        return 'Changes saved successfully!';
      case 'error':
        return 'Error saving changes. Please try again.';
      default:
        return '';
    }
  };

  const getSaveStatusColor = () => {
    switch (saveStatus) {
      case 'saving':
        return 'text-blue-600';
      case 'saved':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Editor Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/admin')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Admin</span>
            </Button>
            
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Editing: {pageTitle}
              </h1>
              <p className="text-sm text-gray-500">{pagePath}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Save Status */}
            {saveStatus !== 'idle' && (
              <div className={`flex items-center space-x-2 ${getSaveStatusColor()}`}>
                {saveStatus === 'saving' && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                )}
                {saveStatus === 'saved' && <Save className="h-4 w-4" />}
                {saveStatus === 'error' && <AlertTriangle className="h-4 w-4" />}
                <span className="text-sm font-medium">
                  {getSaveStatusMessage()}
                </span>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              className="flex items-center space-x-2"
            >
              <Eye className="h-4 w-4" />
              <span>Preview</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Puck Editor */}
      <div className="h-[calc(100vh-73px)]">
        <Puck
          config={config}
          data={data}
          onPublish={handleSave}
          headerTitle={pageTitle}
          headerPath={pagePath}
        />
      </div>
    </div>
  );
}

// Higher-order component to wrap pages with protected Puck editing
interface WithProtectedPuckProps {
  pagePath: string;
  pageTitle: string;
  children: React.ReactNode;
}

export function WithProtectedPuck({ 
  pagePath, 
  pageTitle, 
  children 
}: WithProtectedPuckProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { canEditPages } = useAuth();

  // Check if we're in edit mode (could be from URL params or state)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editMode = urlParams.get('edit') === 'true';
    setIsEditing(editMode && canEditPages());
  }, [canEditPages]);

  if (isEditing) {
    // Load saved data for this page
    const savedData = localStorage.getItem(`puck-data-${pagePath}`);
    const initialData = savedData ? JSON.parse(savedData) : undefined;

    return (
      <ProtectedPuckEditor
        pagePath={pagePath}
        pageTitle={pageTitle}
        initialData={initialData}
      />
    );
  }

  return <>{children}</>;
}
