import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Puck, type Data, type Config } from '@measured/puck';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { ArrowLeft, AlertTriangle } from 'lucide-react';
import { config } from '@/puck/schema';

/**
 * Get the initial data for a page by dynamically importing defaultData from page components
 */
async function getPageDataAsync(pagePath: string): Promise<Data> {
  // First, try to load saved data from localStorage
  const savedData = localStorage.getItem(`puck-data-${pagePath}`);
  if (savedData) {
    try {
      return JSON.parse(savedData);
    } catch (error) {
      console.warn(`Failed to parse saved data for ${pagePath}:`, error);
    }
  }

  // Try to get actual page data from page components using dynamic imports
  try {
    switch (pagePath) {
      case '/about': {
        const { aboutDefaultData } = await import('../pages/About');
        return aboutDefaultData;
      }
      case '/residential-roofing': {
        const { residentialDefaultData } = await import('../pages/ResidentialRoofing');
        return residentialDefaultData;
      }
      case '/commercial-roofing': {
        const { commercialDefaultData } = await import('../pages/CommercialRoofing');
        return commercialDefaultData;
      }
    }
  } catch (error) {
    console.warn(`Failed to load page data for ${pagePath}:`, error);
  }

  // Fallback data for pages that don't have rich default data yet
  const fallbackData: Record<string, Data> = {
    '/': {
      root: { title: 'Home Page' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Transform Your Roof, Protect Your Home',
            subtitle: "Florida's most trusted roofing experts",
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/contact': {
      root: { title: 'Contact Us' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Contact Roofers LLC',
            subtitle: 'Get your free roofing estimate today',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    }
  };

  // Fall back to fallback data
  const fallback = fallbackData[pagePath];
  if (fallback) {
    return fallback;
  }

  // Ultimate fallback for unknown pages
  return {
    root: { title: 'Page' },
    content: []
  };
}

/**
 * Synchronous version for initial state - uses fallback data only
 */
function getPageData(pagePath: string): Data {
  // First, try to load saved data from localStorage
  const savedData = localStorage.getItem(`puck-data-${pagePath}`);
  if (savedData) {
    try {
      return JSON.parse(savedData);
    } catch (error) {
      console.warn(`Failed to parse saved data for ${pagePath}:`, error);
    }
  }

  // Fallback data for pages that don't have rich default data yet
  const fallbackData: Record<string, Data> = {
    '/': {
      root: { title: 'Home Page' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Transform Your Roof, Protect Your Home',
            subtitle: "Florida's most trusted roofing experts",
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/contact': {
      root: { title: 'Contact Us' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Contact Roofers LLC',
            subtitle: 'Get your free roofing estimate today',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/about': {
      root: { title: 'About Us' },
      content: [
        {
          type: 'AboutHero',
          props: {
            title: 'About Roofers LLC',
            subtitle: 'Your Trusted Roofing Partner in Florida',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/residential-roofing': {
      root: { title: 'Residential Roofing' },
      content: [
        {
          type: 'ResidentialHero',
          props: {
            title: 'Residential Roofing Services by Roofers LLC',
            subtitle: 'Protect your home with quality roofing solutions',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/commercial-roofing': {
      root: { title: 'Commercial Roofing' },
      content: [
        {
          type: 'CommercialHero',
          props: {
            title: 'Commercial Roofing Solutions by Roofers LLC',
            subtitle: 'Professional roofing services for businesses',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/emergency-roofing': {
      root: { title: '24/7 Emergency Roof Repair' },
      content: [
        {
          type: 'EmergencyHero',
          props: {
            title: '24/7 Emergency Roof Repair',
            subtitle: 'Fast response when you need it most',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/roof-repair': {
      root: { title: 'Roof Repair Services' },
      content: [
        {
          type: 'RepairHero',
          props: {
            title: 'Expert Roof Repair Services by Roofers LLC',
            subtitle: 'Fast, reliable roof repair solutions for all roofing types',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/metal-roofing': {
      root: { title: 'Metal Roofing Services' },
      content: [
        {
          type: 'MetalHero',
          props: {
            title: 'Metal Roofing Solutions by Roofers LLC',
            subtitle: 'Durable, energy-efficient metal roofing systems',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/shingle-roofing': {
      root: { title: 'Shingle Roofing Services' },
      content: [
        {
          type: 'ShingleHero',
          props: {
            title: 'Shingle Roofing Solutions by Roofers LLC',
            subtitle: 'Quality shingle roofing installation and replacement services',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/tpo-roofing': {
      root: { title: 'TPO Roofing Services' },
      content: [
        {
          type: 'TPOHero',
          props: {
            title: 'TPO Roofing Services',
            subtitle: 'Professional TPO roofing solutions in Florida',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/gallery': {
      root: { title: 'Project Gallery' },
      content: [
        {
          type: 'GallerySection',
          props: {
            title: 'Our Work',
            subtitle: 'See the quality of our roofing projects'
          }
        }
      ]
    },
    '/areas-we-serve': {
      root: { title: 'Areas We Serve' },
      content: [
        {
          type: 'AreasHero',
          props: {
            title: 'Areas We Serve',
            subtitle: 'Professional roofing services across Florida',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/financing': {
      root: { title: 'Financing Options' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Financing Options',
            subtitle: 'Make your roofing project affordable',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    },
    '/roofing-education': {
      root: { title: 'Roofing Education' },
      content: [
        {
          type: 'HeroSection',
          props: {
            title: 'Roofing Education Center',
            subtitle: 'Learn about roofing materials, maintenance, and best practices',
            backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
          }
        }
      ]
    }
  };

  // Fall back to fallback data
  const fallback = fallbackData[pagePath];
  if (fallback) {
    return fallback;
  }

  // Ultimate fallback for unknown pages
  return {
    root: { title: 'Page' },
    content: []
  };
}

/**
 * Get the appropriate Puck config for a page
 */
async function getPageConfig(pagePath: string) {
  // Import configs dynamically to avoid circular dependencies
  switch (pagePath) {
    case '/about':
      return import('../puck/schemas/about').then(m => m.aboutConfig);
    case '/':
      return import('../puck/schemas/home').then(m => m.homeConfig);
    case '/contact':
      return import('../puck/schemas/contact').then(m => m.contactConfig);
    case '/residential-roofing':
      return import('../puck/schemas/residential').then(m => m.residentialConfig);
    case '/commercial-roofing':
      return import('../puck/schemas/commercial').then(m => m.commercialConfig);
    default:
      // For unknown pages, use the general config
      return import('../puck/schema').then(m => m.config);
  }
}

interface ProtectedPuckEditorProps {
  pagePath: string;
  pageTitle: string;
  initialData?: Data;
}

export default function ProtectedPuckEditor({
  pagePath,
  pageTitle,
  initialData
}: ProtectedPuckEditorProps) {
  const { canEditPages } = useAuth();
  const navigate = useNavigate();
  const [data, setData] = useState<Data>(() => {
    // Load data with priority: initialData > localStorage > default data
    if (initialData) return initialData;
    return getPageData(pagePath);
  });
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [pageConfig, setPageConfig] = useState<Config>(config);

  // Load the appropriate config and rich data for the page
  useEffect(() => {
    // Load config
    getPageConfig(pagePath).then(loadedConfig => {
      setPageConfig(loadedConfig);
    }).catch(error => {
      console.warn(`Failed to load config for ${pagePath}, using default:`, error);
      setPageConfig(config);
    });

    // Load rich data asynchronously (only if not already loaded from localStorage or initialData)
    if (!initialData && !localStorage.getItem(`puck-data-${pagePath}`)) {
      getPageDataAsync(pagePath).then(richData => {
        setData(richData);
      }).catch(error => {
        console.warn(`Failed to load rich data for ${pagePath}:`, error);
      });
    }
  }, [pagePath, initialData]);

  // Check permissions
  if (!canEditPages()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to edit pages. Please contact an administrator.
            </AlertDescription>
          </Alert>
          <Button 
            onClick={() => navigate('/admin')} 
            className="w-full mt-4"
            variant="outline"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </div>
      </div>
    );
  }

  const handleSave = async (newData: Data) => {
    setSaveStatus('saving');

    try {
      // Save to localStorage (or your actual save implementation)
      localStorage.setItem(`puck-data-${pagePath}`, JSON.stringify(newData));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setData(newData);
      setSaveStatus('saved');

      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);

      // Optional: Navigate back to admin dashboard after save
      // navigate('/admin');
    } catch (error) {
      console.error('Save error:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  // Add a small floating status indicator for save status
  const renderSaveStatus = () => {
    if (saveStatus === 'idle') return null;

    const statusClasses = {
      saving: 'bg-blue-500',
      saved: 'bg-green-500',
      error: 'bg-red-500'
    };

    const statusText = {
      saving: 'Saving...',
      saved: 'Saved!',
      error: 'Error saving'
    };

    return (
      <div className={`fixed top-4 right-4 z-[100] px-4 py-2 rounded-md text-white ${statusClasses[saveStatus]}`}>
        {statusText[saveStatus]}
      </div>
    );
  };

  // Add a back button that floats above the editor
  const renderBackButton = () => {
    return (
      <Button
        onClick={() => navigate('/admin')}
        className="fixed top-4 left-4 z-[100]"
        variant="secondary"
        size="sm"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Admin
      </Button>
    );
  };

  // Directly replicate the public pages pattern
  return (
    <div style={{
      position: 'fixed',
      inset: 0,
      zIndex: 50,
      height: '100vh',
      width: '100vw'
    }}>
      {renderBackButton()}
      {renderSaveStatus()}
      <Puck
        config={pageConfig}
        data={data}
        onPublish={handleSave}
        headerTitle={pageTitle}
        headerPath={pagePath}
      />
    </div>
  );
}

// Higher-order component to wrap pages with protected Puck editing
interface WithProtectedPuckProps {
  pagePath: string;
  pageTitle: string;
  children: React.ReactNode;
}

export function WithProtectedPuck({ 
  pagePath, 
  pageTitle, 
  children 
}: WithProtectedPuckProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { canEditPages } = useAuth();

  // Check if we're in edit mode (could be from URL params or state)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editMode = urlParams.get('edit') === 'true';
    setIsEditing(editMode && canEditPages());
  }, [canEditPages]);

  if (isEditing) {
    // Load data using the utility function (handles localStorage + defaults)
    const initialData = getPageData(pagePath);

    return (
      <ProtectedPuckEditor
        pagePath={pagePath}
        pageTitle={pageTitle}
        initialData={initialData}
      />
    );
  }

  return <>{children}</>;
}

