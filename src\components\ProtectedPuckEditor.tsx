import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Puck, Data, Config } from '@measured/puck';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/hooks/useAuth';
import { ArrowLeft, AlertTriangle } from 'lucide-react';
import { config } from '@/puck/schema';

// Import rich data directly at module level
import { aboutDefaultData } from '../pages/About';
import { residentialDefaultData } from '../pages/ResidentialRoofing';
import { commercialDefaultData } from '../pages/CommercialRoofing';
import { homeDefaultData } from '../pages/Home';
import { contactDefaultData } from '../pages/Contact';
import { emergencyDefaultData } from '../pages/EmergencyRoofing';
import { galleryDefaultData } from '../pages/Gallery';

// Get rich data from page components - synchronous version for initial state
function getRichPageDataSync(pagePath: string): Data | null {
  try {
    switch (pagePath) {
      case '/': {
        console.log('Loaded rich Home data synchronously:', homeDefaultData);
        return homeDefaultData;
      }
      case '/about': {
        console.log('Loaded rich About data synchronously:', aboutDefaultData);
        return aboutDefaultData;
      }
      case '/contact': {
        console.log('Loaded rich Contact data synchronously:', contactDefaultData);
        return contactDefaultData;
      }
      case '/emergency-roofing': {
        console.log('Loaded rich Emergency data synchronously:', emergencyDefaultData);
        return emergencyDefaultData;
      }
      case '/gallery': {
        console.log('Loaded rich Gallery data synchronously:', galleryDefaultData);
        return galleryDefaultData;
      }
      case '/residential-roofing': {
        console.log('Loaded rich Residential data synchronously:', residentialDefaultData);
        return residentialDefaultData;
      }
      case '/commercial-roofing': {
        console.log('Loaded rich Commercial data synchronously:', commercialDefaultData);
        return commercialDefaultData;
      }
    }
  } catch (error) {
    console.warn(`Failed to load rich page data synchronously for ${pagePath}:`, error);
  }
  return null;
}

// Get rich data from page components - async version for updates
async function getRichPageData(pagePath: string): Promise<Data | null> {
  try {
    switch (pagePath) {
      case '/': {
        const { homeDefaultData } = await import('../pages/Home');
        console.log('Loaded rich Home data:', homeDefaultData);
        return homeDefaultData;
      }
      case '/about': {
        const { aboutDefaultData } = await import('../pages/About');
        console.log('Loaded rich About data:', aboutDefaultData);
        return aboutDefaultData;
      }
      case '/contact': {
        const { contactDefaultData } = await import('../pages/Contact');
        console.log('Loaded rich Contact data:', contactDefaultData);
        return contactDefaultData;
      }
      case '/emergency-roofing': {
        const { emergencyDefaultData } = await import('../pages/EmergencyRoofing');
        console.log('Loaded rich Emergency data:', emergencyDefaultData);
        return emergencyDefaultData;
      }
      case '/gallery': {
        const { galleryDefaultData } = await import('../pages/Gallery');
        console.log('Loaded rich Gallery data:', galleryDefaultData);
        return galleryDefaultData;
      }
      case '/residential-roofing': {
        const { residentialDefaultData } = await import('../pages/ResidentialRoofing');
        console.log('Loaded rich Residential data:', residentialDefaultData);
        return residentialDefaultData;
      }
      case '/commercial-roofing': {
        const { commercialDefaultData } = await import('../pages/CommercialRoofing');
        console.log('Loaded rich Commercial data:', commercialDefaultData);
        return commercialDefaultData;
      }
    }
  } catch (error) {
    console.warn(`Failed to load rich page data for ${pagePath}:`, error);
  }
  return null;
}

// Get page config
async function getPageConfig(pagePath: string) {
  switch (pagePath) {
    case '/about':
      return import('../puck/schemas/about').then(m => m.aboutConfig);
    case '/':
      return import('../puck/schemas/home').then(m => m.homeConfig);
    case '/contact':
      return import('../puck/schemas/contact').then(m => m.contactConfig);
    case '/emergency-roofing':
      return import('../puck/schemas/emergency').then(m => m.emergencyConfig);
    case '/residential-roofing':
      return import('../puck/schemas/residential').then(m => m.residentialConfig);
    case '/commercial-roofing':
      return import('../puck/schemas/commercial').then(m => m.commercialConfig);
    default:
      return import('../puck/schema').then(m => m.config);
  }
}

interface ProtectedPuckEditorProps {
  pagePath: string;
  pageTitle: string;
  initialData?: Data;
}

export default function ProtectedPuckEditor({
  pagePath,
  pageTitle,
  initialData
}: ProtectedPuckEditorProps) {
  const { canEditPages } = useAuth();
  const navigate = useNavigate();

  // Simple fallback data
  const fallbackData: Data = {
    root: { title: pageTitle },
    content: []
  };

  // Try to get rich data synchronously first, then fall back
  const initialRichData = getRichPageDataSync(pagePath);
  const [data, setData] = useState<Data>(initialData || initialRichData || fallbackData);
  const [pageConfig, setPageConfig] = useState<Config>(config);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  // Load page config
  useEffect(() => {
    getPageConfig(pagePath).then(loadedConfig => {
      setPageConfig(loadedConfig);
    }).catch(error => {
      console.warn(`Failed to load config for ${pagePath}:`, error);
      setPageConfig(config);
    });
  }, [pagePath]);

  // Load rich data - ALWAYS try to load it
  useEffect(() => {
    getRichPageData(pagePath).then(richData => {
      if (richData) {
        console.log(`Setting rich data for ${pagePath}:`, richData);
        setData(richData);
      }
    }).catch(error => {
      console.warn(`Failed to load rich data for ${pagePath}:`, error);
    });
  }, [pagePath]);

  if (!canEditPages()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to edit pages. Please contact an administrator.
            </AlertDescription>
          </Alert>
          <Button
            onClick={() => navigate('/admin')}
            className="w-full mt-4"
            variant="outline"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Admin
          </Button>
        </div>
      </div>
    );
  }

  const handleSave = async (newData: Data) => {
    setSaveStatus('saving');
    try {
      localStorage.setItem(`puck-data-${pagePath}`, JSON.stringify(newData));
      await new Promise(resolve => setTimeout(resolve, 1000));
      setData(newData);
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('Save error:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const renderSaveStatus = () => {
    if (saveStatus === 'idle') return null;
    const statusClasses = {
      saving: 'bg-blue-500',
      saved: 'bg-green-500',
      error: 'bg-red-500'
    };
    const statusText = {
      saving: 'Saving...',
      saved: 'Saved!',
      error: 'Error saving'
    };
    return (
      <div className={`fixed top-4 right-4 z-[100] px-4 py-2 rounded-md text-white ${statusClasses[saveStatus]}`}>
        {statusText[saveStatus]}
      </div>
    );
  };

  const renderBackButton = () => {
    return (
      <Button
        onClick={() => navigate('/admin')}
        className="fixed top-4 left-4 z-[100]"
        variant="secondary"
        size="sm"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Admin
      </Button>
    );
  };

  return (
    <div style={{
      position: 'fixed',
      inset: 0,
      zIndex: 50,
      height: '100vh',
      width: '100vw'
    }}>
      {renderBackButton()}
      {renderSaveStatus()}
      <Puck
        config={pageConfig}
        data={data}
        onPublish={handleSave}
        headerTitle={pageTitle}
        headerPath={pagePath}
      />
    </div>
  );
}