/**
 * Environment configuration module
 * Centralizes all environment-specific configuration and provides type safety
 */

export interface EnvironmentConfig {
  appUrl: string;
  apiBaseUrl: string;
  authCallbackUrl: string;
  nodeEnv: 'development' | 'production' | 'test';
  isProduction: boolean;
  isDevelopment: boolean;
}

// Define environment variables to check
const required = [
  'APP_URL',
  'API_BASE_URL',
  'AUTH_CALLBACK_URL',
] as const;

// Helper function to access environment variables
const getEnv = (key: string): string => {
  // First try with import.meta.env (for Vite)
  if (typeof import.meta.env !== 'undefined' && import.meta.env[key]) {
    return import.meta.env[key];
  }
  
  // Then try with process.env (for Node.js)
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key];
  }
  
  // Finally, check if the VITE_ prefixed version exists (for backward compatibility)
  const viteKey = `VITE_${key}`;
  if (typeof import.meta.env !== 'undefined' && import.meta.env[viteKey]) {
    return import.meta.env[viteKey];
  }
  
  return '';
};

// Validate presence of required environment variables
required.forEach((key) => {
  if (!getEnv(key)) {
    throw new Error(
      `Missing required environment variable: ${key}\n` +
      'Please check your .env file and make sure all required variables are defined.'
    );
  }
});

// Environment configuration object
export const config: EnvironmentConfig = {
  appUrl: getEnv('APP_URL'),
  apiBaseUrl: getEnv('API_BASE_URL'),
  authCallbackUrl: getEnv('AUTH_CALLBACK_URL'),
  nodeEnv: (getEnv('NODE_ENV') || import.meta.env.MODE) as 'development' | 'production' | 'test',
  isProduction: getEnv('NODE_ENV') === 'production' || import.meta.env.PROD,
  isDevelopment: getEnv('NODE_ENV') === 'development' || import.meta.env.DEV,
};

// Validate configuration values
const validateConfig = (cfg: EnvironmentConfig) => {
  // Validate URLs
  const urlFields = ['appUrl', 'apiBaseUrl', 'authCallbackUrl'] as const;
  urlFields.forEach(field => {
    try {
      new URL(cfg[field]);
    } catch (e) {
      throw new Error(`Invalid URL for ${field}: ${cfg[field]}`);
    }
  });

  // Validate environment
  if (!['development', 'production', 'test'].includes(cfg.nodeEnv)) {
    throw new Error(`Invalid NODE_ENV: ${cfg.nodeEnv}`);
  }
};

validateConfig(config);

export default config;