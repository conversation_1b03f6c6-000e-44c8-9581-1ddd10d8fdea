import { FC, useState, FormEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { sendEmail } from '../../lib/api';

const emergencyTypes = [
  'Leaking Roof',
  'Storm Damage',
  'Fallen Tree',
  'Wind Damage',
  'Hail Damage',
  'Other Emergency'
];

const EmergencyRepairForm: FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate available time slots (8 AM to 8 PM)
  const timeSlots = Array.from({ length: 13 }, (_, i) => {
    const hour = i + 8;
    return `${hour % 12 || 12}:00 ${hour < 12 ? 'AM' : 'PM'}`;
  });

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    const formData = new FormData(e.currentTarget);
    const formFields = Object.fromEntries(formData.entries());

    try {
      // Format the email content
      const emailContent = {
        to: '<EMAIL>',
        subject: `URGENT: Emergency Repair Request - ${formFields.emergencyType}`,
        message: `
          Emergency repair request details:
          
          Emergency Type: ${formFields.emergencyType}
          Scheduled Date: ${formFields.date}
          Scheduled Time: ${formFields.time}
          Name: ${formFields.name}
          Phone: ${formFields.phone}
          Email: ${formFields.email}
          Address: ${formFields.address}
          Emergency Details: ${formFields.details || 'No additional details provided'}

          URGENT: Please contact customer immediately to confirm appointment.
        `
      };

      // Send the email using the temporary API service
      await sendEmail(emailContent);

      // Navigate to success page with form data
      navigate('/emergency-success', {
        state: {
          name: formFields.name,
          date: formFields.date,
          time: formFields.time,
          type: formFields.emergencyType,
          email: formFields.email
        }
      });
    } catch (err) {
      setError('Failed to submit form. Please call us directly at (************* for immediate assistance.');
      setIsSubmitting(false);
    }
  };

  // Get today's date in YYYY-MM-DD format for min date
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="max-w-2xl mx-auto p-6">
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 border border-blue-200 text-blue-800 rounded-lg p-4 mb-6">
          Note: This form is in test mode. No actual emails will be sent during development.
          <br />
          Form submissions will still navigate to the success page for testing purposes.
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6">
          <p>{error}</p>
          <div className="mt-2">
            <a
              href="tel:+13053761808"
              className="text-red-600 font-semibold hover:text-red-700"
            >
              Call (************* Now
            </a>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <label htmlFor="name" className="block font-medium text-gray-700">
            Full Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="John Doe"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="phone" className="block font-medium text-gray-700">
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="(*************"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="block font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="address" className="block font-medium text-gray-700">
            Property Address
          </label>
          <input
            type="text"
            id="address"
            name="address"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="123 Main St, City, FL"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="emergencyType" className="block font-medium text-gray-700">
            Emergency Type
          </label>
          <select
            id="emergencyType"
            name="emergencyType"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Select Emergency Type</option>
            {emergencyTypes.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label htmlFor="date" className="block font-medium text-gray-700">
            Preferred Date
          </label>
          <input
            type="date"
            id="date"
            name="date"
            required
            min={today}
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="time" className="block font-medium text-gray-700">
            Preferred Time
          </label>
          <select
            id="time"
            name="time"
            required
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Select Preferred Time</option>
            {timeSlots.map(time => (
              <option key={time} value={time}>
                {time}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label htmlFor="details" className="block font-medium text-gray-700">
            Emergency Details
          </label>
          <textarea
            id="details"
            name="details"
            rows={4}
            className="w-full px-4 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Please describe the emergency situation..."
          />
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full py-3 px-6 text-white bg-primary rounded-md hover:bg-primary-dark transition-colors
            ${isSubmitting ? 'opacity-75 cursor-not-allowed' : ''}`}
        >
          {isSubmitting ? 'Scheduling...' : 'Schedule Emergency Repair'}
        </button>
      </form>
    </div>
  );
};

export default EmergencyRepairForm;