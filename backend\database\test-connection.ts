import mysql from 'mysql2/promise'
import * as dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import { spawnSync } from 'child_process'

// Load environment variables
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const envPath = join(__dirname, '../../.env')

console.log('Loading environment from:', envPath)
dotenv.config({ path: envPath })

interface MySQLError extends Error {
  code?: string
  errno?: number
  sqlState?: string
  sqlMessage?: string
}

function keepAlive(): NodeJS.Timeout {
  return setInterval(() => {
    process.stdout.write('.')
  }, 1000)
}

async function testConnection() {
  const timer = keepAlive()
  console.log('\nStarting database connection test...')
  
  // Log all relevant environment variables
  const dbConfig = {
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USER,
    password: process.env.DATABASE_PASSWORD ? '[HIDDEN]' : 'none',
    database: process.env.DATABASE_NAME,
    port: process.env.DATABASE_PORT,
  }
  
  console.log('\nDatabase configuration:', dbConfig)

  let connection
  try {
    console.log('\nAttempting to connect to MySQL server...')
    
    // Try to create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
      connectTimeout: 10000, // 10 seconds
    })

    console.log('Successfully connected to MySQL server')

    // Test database creation
    console.log(`\nAttempting to create database '${process.env.DATABASE_NAME}'...`)
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DATABASE_NAME}`)
    console.log(`Database '${process.env.DATABASE_NAME}' created or already exists`)

    // Switch to the database
    console.log(`\nAttempting to switch to database '${process.env.DATABASE_NAME}'...`)
    await connection.query(`USE ${process.env.DATABASE_NAME}`)
    console.log(`Successfully switched to database '${process.env.DATABASE_NAME}'`)

    // Test creating a table
    console.log('\nAttempting to create test table...')
    await connection.query(`
      CREATE TABLE IF NOT EXISTS connection_test (
        id INT PRIMARY KEY AUTO_INCREMENT,
        test_column VARCHAR(255)
      )
    `)
    console.log('Successfully created test table')

    // Test inserting data
    console.log('\nAttempting to insert test data...')
    await connection.query(
      'INSERT INTO connection_test (test_column) VALUES (?)',
      ['Test successful at ' + new Date().toISOString()]
    )
    console.log('Successfully inserted test data')

    // Test reading data
    console.log('\nAttempting to read test data...')
    const [rows] = await connection.query('SELECT * FROM connection_test')
    console.log('Successfully read test data:', rows)

    // Clean up
    console.log('\nCleaning up test data...')
    await connection.query('DROP TABLE connection_test')
    console.log('Successfully cleaned up test table')

    return true

  } catch (error) {
    console.error('\n❌ Connection test failed!')
    if (error instanceof Error) {
      console.error('Error type:', error.constructor.name)
      console.error('Error message:', error.message)
      console.error('Stack trace:', error.stack)
      
      const mysqlError = error as MySQLError
      if (mysqlError.code) {
        console.error('Error code:', mysqlError.code)
        
        // Common MySQL error codes
        const errorGuide: Record<string, string> = {
          'ER_ACCESS_DENIED_ERROR': 'Invalid username or password',
          'ECONNREFUSED': 'MySQL server is not running or not accessible',
          'ER_BAD_DB_ERROR': 'Database does not exist',
          'ER_HOST_NOT_PRIVILEGED': 'Host is not allowed to connect',
          'PROTOCOL_CONNECTION_LOST': 'Database connection was closed'
        }
        
        if (mysqlError.code in errorGuide) {
          console.error('\nPossible solution:', errorGuide[mysqlError.code])
        }
      }
    } else {
      console.error('Unknown error type:', error)
    }
    return false
  } finally {
    if (connection) {
      console.log('\nClosing connection...')
      try {
        await connection.end()
        console.log('Connection closed')
      } catch (err) {
        console.error('Error while closing connection:', err)
      }
    }
    clearInterval(timer)
  }
}

// Run test if executed directly
if (import.meta.url.endsWith(process.argv[1])) {
  console.log('\n=== MySQL Connection Test ===\n')
  console.log('Node version:', process.version)
  console.log('Current directory:', process.cwd())

  // Use sync execution to prevent premature termination
  const child = spawnSync(process.argv[0], [process.argv[1]], {
    stdio: 'inherit',
    shell: true
  })

  process.exit(child.status ?? 1)
} else {
  testConnection()
    .then(success => {
      if (success) {
        console.log('\n✅ All connection tests passed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('\n❌ Connection test failed:', error)
      process.exit(1)
    })
}