{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "ts-node-esm scripts/start-dev.ts", "start": "vite", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "build:prod": "tsc && vite build --mode production", "lint": "eslint .", "preview": "vite preview", "typecheck": "tsc --noEmit", "validate": "npm run typecheck && npm run lint", "db:migrate": "cross-env NODE_OPTIONS=--loader=ts-node/esm ts-node-esm backend/database/migrate.ts", "db:migrate:rollback": "cross-env NODE_OPTIONS=--loader=ts-node/esm ts-node-esm backend/database/migrate.ts --rollback", "db:check": "cross-env NODE_OPTIONS=--loader=ts-node/esm ts-node-esm backend/database/check-tables.ts", "db:test": "cross-env NODE_OPTIONS=--loader=ts-node/esm ts-node-esm backend/database/test-connection.ts"}, "dependencies": {"@auth/core": "^0.18.1", "@hookform/resolvers": "^5.0.1", "@measured/puck": "^0.18.3", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@swc/core": "^1.10.14", "@types/mysql": "^2.15.25", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "dotenv": "^16.4.1", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "mysql2": "^3.9.1", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.26.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.8", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}