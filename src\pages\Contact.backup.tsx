import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { contactConfig } from '../puck/schemas/contact';
import { contactInfo } from '@/lib/image-paths';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface ContactData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: ContactData = {
  root: {
    title: 'Contact Us',
    description: 'Get in touch with Roofers LLC'
  },
  content: [
    {
      type: 'RequestForm',
      props: {
        title: 'Request Your Free Quote',
        nameLabel: 'Name',
        emailLabel: 'Email',
        phoneLabel: 'Phone',
        messageLabel: 'Additional Details',
        serviceLabel: 'Service Needed',
        submitButtonText: 'Give me a quote',
        servicePlaceholder: 'Select a service',
        serviceOptions: [
          { value: 'residential', label: 'Residential Roofing' },
          { value: 'commercial', label: 'Commercial Roofing' },
          { value: 'repair', label: 'Roof Repair' },
          { value: 'inspection', label: 'Roof Inspection' }
        ]
      }
    },
    {
      type: 'ContactInfo',
      props: {
        title: 'Contact Information',
        address: contactInfo.address,
        phone: contactInfo.phone,
        email: contactInfo.email,
        businessHoursWeekday: contactInfo.businessHours.monday,
        businessHoursSaturday: contactInfo.businessHours.saturday,
        businessHoursSunday: contactInfo.businessHours.sunday
      }
    },
    {
      type: 'EmergencyService',
      props: {
        title: 'Emergency Service',
        description: '24/7 emergency roofing services available for urgent situations. Call our emergency hotline:',
        phone: contactInfo.phone
      }
    }
  ]
};

const Contact: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<ContactData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={contactConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as ContactData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  // Split the components into left and right columns
  const formComponent = data.content.find(component => component.type === 'RequestForm');
  const rightColumnComponents = data.content.filter(component => component.type !== 'RequestForm');

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-4xl font-bold mb-8 text-center">Get Your Free Quote</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Left Column - Form */}
          <div>
            {formComponent && (
              <Render config={contactConfig} data={{ content: [formComponent] }} />
            )}
          </div>

          {/* Right Column - Contact Info and Emergency Service */}
          <div className="space-y-8">
            {rightColumnComponents.map((component, index) => (
              <Render 
                key={index}
                config={contactConfig}
                data={{ content: [component] }}
              />
            ))}
          </div>
        </div>
      </div>
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default Contact;