# Environment Configuration Plan

## Overview

This document outlines the plan to make our application environment-agnostic by removing hardcoded localhost references and implementing proper environment-based configuration.

```mermaid
graph TD
    A[Environment Configuration] --> B[Frontend Changes]
    A --> C[Backend Changes]
    
    B --> B1[Create Environment Variables]
    B --> B2[API Client Setup]
    B --> B3[Development Server Config]
    
    C --> C1[Update CORS Config]
    C --> C2[Database Config]
    C --> C3[API Endpoints]
    
    B1 --> D[Update Documentation]
    C1 --> D
```

## 1. Environment Configuration Setup

### Frontend (.env)
```plaintext
# Development
VITE_APP_URL=http://localhost:5173
VITE_API_BASE_URL=http://localhost:3001
VITE_AUTH_CALLBACK_URL=http://localhost:5173/auth/callback

# Production
VITE_APP_URL=https://roofers.llc
VITE_API_BASE_URL=https://api.roofers.llc
VITE_AUTH_CALLBACK_URL=https://roofers.llc/auth/callback
```

### Backend (.env)
```plaintext
# Existing
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# New/Updated
APP_URL=http://localhost:3001
FRONTEND_URL=http://localhost:5173
AUTH_CALLBACK_URL=${FRONTEND_URL}/auth/callback

# Production Values
# APP_URL=https://api.roofers.llc
# FRONTEND_URL=https://roofers.llc
# AUTH_CALLBACK_URL=https://roofers.llc/auth/callback
```

## 2. Implementation Plan

### Phase 1: Frontend Configuration

#### 1. Create Configuration Module
File: `src/config/environment.ts`
```typescript
interface EnvironmentConfig {
  appUrl: string;
  apiBaseUrl: string;
  authCallbackUrl: string;
}

const validateConfig = (config: EnvironmentConfig) => {
  const required = ['appUrl', 'apiBaseUrl', 'authCallbackUrl'];
  required.forEach(key => {
    if (!config[key]) {
      throw new Error(`Missing required environment variable: ${key}`);
    }
  });
};

export const config: EnvironmentConfig = {
  appUrl: import.meta.env.VITE_APP_URL,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  authCallbackUrl: import.meta.env.VITE_AUTH_CALLBACK_URL,
};

validateConfig(config);
```

#### 2. Create API Client
File: `src/lib/api-client.ts`
```typescript
import { config } from '../config/environment';

const createApiClient = () => {
  const baseURL = config.apiBaseUrl;
  
  return {
    get: async (path: string) => {
      const response = await fetch(`${baseURL}${path}`);
      return response.json();
    },
    post: async (path: string, data: any) => {
      const response = await fetch(`${baseURL}${path}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return response.json();
    },
    // Add other methods as needed
  };
};

export const apiClient = createApiClient();
```

#### 3. Update Vite Configuration
File: `vite.config.ts`
```typescript
import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  envDir: '.',
});
```

### Phase 2: Backend Configuration

#### 1. Update CORS Configuration
File: `backend/app/Config/Cors.php`
```php
public $allowedOrigins = [];

public function __construct()
{
    $origins = explode(',', $_ENV['CORS_ORIGIN'] ?? '');
    $this->allowedOrigins = array_map('trim', $origins);
}
```

#### 2. Update Database Configuration
- Move all database connection strings to environment variables
- Support multiple environments through configuration

### Phase 3: Form Handling & Authentication

1. Update form components to use relative paths
2. Configure authentication flows with environment URLs
3. Implement proper URL generation for callbacks

### Phase 4: Testing & Documentation

1. Add environment variable validation
2. Update deployment documentation
3. Add environment setup guides

## File Changes Required

### New Files
- `src/config/environment.ts`
- `src/lib/api-client.ts`
- `.env.example` (update)
- `.env.production` (create)

### Modified Files
- `.env`
- `vite.config.ts`
- `src/lib/api.ts`
- `backend/app/Config/Cors.php`
- Form components using API endpoints

## Implementation Steps

1. Create environment configuration files
2. Implement API client with environment-based URLs
3. Update backend configuration
4. Test in multiple environments
5. Update documentation

## Success Criteria

1. No hardcoded URLs in codebase
2. Environment variables for all configuration
3. Successful testing in:
   - Development (localhost)
   - Production (roofers.llc)
4. Documentation updated for deployment

## Security Considerations

1. Environment variable validation
2. Secure handling of sensitive data
3. CORS configuration validation
4. Production security headers