import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import { db } from '../database/connection';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

// File upload configuration
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error as Error, '');
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// File filter for security
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = [
    // Images
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Documents
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv',
    // Videos
    'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} not allowed`));
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 10 // Max 10 files per upload
  }
});

// Helper function to determine file type
const getFileType = (mimetype: string): 'image' | 'video' | 'document' | 'other' => {
  if (mimetype.startsWith('image/')) return 'image';
  if (mimetype.startsWith('video/')) return 'video';
  if (mimetype.includes('pdf') || mimetype.includes('document') || mimetype.includes('text')) return 'document';
  return 'other';
};

// Helper function to get image dimensions
const getImageDimensions = async (filePath: string): Promise<{ width: number; height: number } | null> => {
  try {
    const metadata = await sharp(filePath).metadata();
    return {
      width: metadata.width || 0,
      height: metadata.height || 0
    };
  } catch {
    return null;
  }
};

// Get all media files
export const getMediaFiles = async (req: Request, res: Response) => {
  try {
    const { type, category, tag, search, page = 1, limit = 20 } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    let query = `
      SELECT 
        mf.*,
        u.name as uploaded_by_name,
        GROUP_CONCAT(DISTINCT mc.name) as categories,
        GROUP_CONCAT(DISTINCT mt.name) as tags
      FROM media_files mf
      LEFT JOIN users u ON mf.uploaded_by = u.id
      LEFT JOIN media_file_categories mfc ON mf.id = mfc.media_file_id
      LEFT JOIN media_categories mc ON mfc.category_id = mc.id
      LEFT JOIN media_file_tags mft ON mf.id = mft.media_file_id
      LEFT JOIN media_tags mt ON mft.tag_id = mt.id
      WHERE mf.status = 'active'
    `;

    const params: any[] = [];

    if (type) {
      query += ' AND mf.file_type = ?';
      params.push(type);
    }

    if (search) {
      query += ' AND (mf.original_filename LIKE ? OR mf.alt_text LIKE ? OR mf.description LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' GROUP BY mf.id ORDER BY mf.upload_date DESC LIMIT ? OFFSET ?';
    params.push(Number(limit), offset);

    const [rows] = await db.execute<RowDataPacket[]>(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM media_files WHERE status = "active"';
    const countParams: any[] = [];

    if (type) {
      countQuery += ' AND file_type = ?';
      countParams.push(type);
    }

    const [countRows] = await db.execute<RowDataPacket[]>(countQuery, countParams);
    const total = countRows[0].total;

    res.json({
      success: true,
      data: {
        files: rows,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Error fetching media files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch media files'
    });
  }
};

// Upload media files
export const uploadMediaFiles = async (req: Request, res: Response) => {
  try {
    const files = req.files as Express.Multer.File[];
    const userId = req.user?.id || 1; // Default to admin user
    const uploadedFiles = [];

    for (const file of files) {
      const fileType = getFileType(file.mimetype);
      const fileUrl = `/uploads/${file.filename}`;
      
      // Get image dimensions if it's an image
      let dimensions = null;
      if (fileType === 'image') {
        dimensions = await getImageDimensions(file.path);
      }

      // Insert into database
      const [result] = await db.execute<ResultSetHeader>(
        `INSERT INTO media_files (
          filename, original_filename, file_path, file_url, file_type, mime_type,
          file_size, width, height, uploaded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          file.filename,
          file.originalname,
          file.path,
          fileUrl,
          fileType,
          file.mimetype,
          file.size,
          dimensions?.width || null,
          dimensions?.height || null,
          userId
        ]
      );

      uploadedFiles.push({
        id: result.insertId,
        filename: file.filename,
        original_filename: file.originalname,
        file_url: fileUrl,
        file_type: fileType,
        file_size: file.size,
        width: dimensions?.width || null,
        height: dimensions?.height || null
      });
    }

    res.json({
      success: true,
      data: {
        files: uploadedFiles,
        count: uploadedFiles.length
      }
    });
  } catch (error) {
    console.error('Error uploading files:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload files'
    });
  }
};

// Delete media file
export const deleteMediaFile = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // Get file info first
    const [rows] = await db.execute<RowDataPacket[]>(
      'SELECT file_path FROM media_files WHERE id = ? AND status = "active"',
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }

    const filePath = rows[0].file_path;

    // Mark as deleted in database
    await db.execute(
      'UPDATE media_files SET status = "deleted" WHERE id = ?',
      [id]
    );

    // Delete physical file
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.warn('Could not delete physical file:', filePath);
    }

    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete file'
    });
  }
};

// Update media file metadata
export const updateMediaFile = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { alt_text, caption, description, categories, tags } = req.body;

    // Update basic metadata
    await db.execute(
      `UPDATE media_files 
       SET alt_text = ?, caption = ?, description = ?, last_modified = CURRENT_TIMESTAMP
       WHERE id = ? AND status = "active"`,
      [alt_text, caption, description, id]
    );

    // Update categories if provided
    if (categories && Array.isArray(categories)) {
      // Remove existing categories
      await db.execute('DELETE FROM media_file_categories WHERE media_file_id = ?', [id]);
      
      // Add new categories
      for (const categoryId of categories) {
        await db.execute(
          'INSERT INTO media_file_categories (media_file_id, category_id) VALUES (?, ?)',
          [id, categoryId]
        );
      }
    }

    // Update tags if provided
    if (tags && Array.isArray(tags)) {
      // Remove existing tags
      await db.execute('DELETE FROM media_file_tags WHERE media_file_id = ?', [id]);
      
      // Add new tags
      for (const tagName of tags) {
        // Get or create tag
        let [tagRows] = await db.execute<RowDataPacket[]>(
          'SELECT id FROM media_tags WHERE name = ?',
          [tagName]
        );

        let tagId;
        if (tagRows.length === 0) {
          const [result] = await db.execute<ResultSetHeader>(
            'INSERT INTO media_tags (name, slug) VALUES (?, ?)',
            [tagName, tagName.toLowerCase().replace(/\s+/g, '-')]
          );
          tagId = result.insertId;
        } else {
          tagId = tagRows[0].id;
        }

        await db.execute(
          'INSERT INTO media_file_tags (media_file_id, tag_id) VALUES (?, ?)',
          [id, tagId]
        );
      }
    }

    res.json({
      success: true,
      message: 'File updated successfully'
    });
  } catch (error) {
    console.error('Error updating file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update file'
    });
  }
};

// Get media statistics
export const getMediaStatistics = async (req: Request, res: Response) => {
  try {
    const [stats] = await db.execute<RowDataPacket[]>(
      'SELECT * FROM media_statistics'
    );

    res.json({
      success: true,
      data: stats[0] || {
        total_files: 0,
        total_size: 0,
        image_count: 0,
        video_count: 0,
        document_count: 0,
        other_count: 0,
        average_file_size: 0,
        last_upload_date: null
      }
    });
  } catch (error) {
    console.error('Error fetching media statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics'
    });
  }
};

// Get media categories
export const getMediaCategories = async (req: Request, res: Response) => {
  try {
    const [rows] = await db.execute<RowDataPacket[]>(
      'SELECT * FROM media_categories WHERE is_active = TRUE ORDER BY sort_order, name'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch categories'
    });
  }
};

// Get media tags
export const getMediaTags = async (req: Request, res: Response) => {
  try {
    const [rows] = await db.execute<RowDataPacket[]>(
      'SELECT * FROM media_tags ORDER BY usage_count DESC, name'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('Error fetching tags:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tags'
    });
  }
};
