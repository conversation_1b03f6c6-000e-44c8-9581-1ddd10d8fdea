import { useLocation, Link } from 'react-router-dom';
import { CheckCircle } from 'lucide-react';
import type { FC } from 'react';

const FinancingSuccess: FC = () => {
  const location = useLocation();
  const { name = 'there', email = '', projectType = 'roofing project' } = location.state || {};

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
            <h1 className="text-3xl font-bold mb-2">Financing Application Received!</h1>
            <p className="text-xl text-gray-600">
              Thank you {name} for applying for financing for your {projectType.toLowerCase()}.
            </p>
          </div>

          <div className="space-y-6 text-gray-600">
            <div className="border-t border-b border-gray-100 py-4">
              <h2 className="text-xl font-semibold mb-4">What happens next?</h2>
              <ol className="list-decimal list-inside space-y-2">
                <li>Our financing team will review your application</li>
                <li>We'll contact you within 24-48 hours at {email}</li>
                <li>We'll discuss available financing options</li>
                <li>Upon approval, we'll schedule your project</li>
              </ol>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Important Information</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Review of applications typically takes 1-2 business days</li>
                <li>Additional documentation may be required</li>
                <li>Multiple financing options and terms available</li>
                <li>No obligation to proceed until terms are agreed upon</li>
              </ul>
            </div>

            <p>
              Have questions? Call us at{' '}
              <a 
                href="tel:+***********"
                className="text-primary hover:text-primary-dark font-semibold"
              >
                (*************
              </a>
            </p>

            <div className="text-center pt-6">
              <Link
                to="/"
                className="inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
              >
                Return to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancingSuccess;