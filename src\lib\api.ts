import { config } from '@/config/environment';

// Legacy email interface (preserved for compatibility)
export interface EmailPayload {
  to: string;
  subject: string;
  message: string;
}

export const sendEmail = async (data: EmailPayload): Promise<void> => {
  // For development, log the email data and simulate a successful API call
  if (process.env.NODE_ENV === 'development') {
    console.log('Email data:', data);
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Promise.resolve();
  }

  // In production, this would be replaced with actual API call to PHP backend
  return Promise.resolve();
};

// API Base Configuration
const API_BASE_URL = config.apiBaseUrl;

// Generic API Response Type
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Generic API Client
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'An error occurred',
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    });
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Analytics API
export const analyticsApi = {
  getOverview: () => apiClient.get('/api/analytics/overview'),
  getTopPages: () => apiClient.get('/api/analytics/pages'),
  getTrafficSources: () => apiClient.get('/api/analytics/traffic'),
  getDeviceBreakdown: () => apiClient.get('/api/analytics/devices'),
  getStatistics: () => apiClient.get('/api/analytics/statistics'),
  trackPageView: (data: any) => apiClient.post('/api/analytics/pageview', data),
  trackEvent: (data: any) => apiClient.post('/api/analytics/event', data),
};

// Media API
export const mediaApi = {
  getFiles: (params?: any) => apiClient.get('/api/media' + (params ? `?${new URLSearchParams(params)}` : '')),
  uploadFile: (formData: FormData) => apiClient.upload('/api/media/upload', formData),
  deleteFile: (fileId: string) => apiClient.delete(`/api/media/${fileId}`),
  updateFile: (fileId: string, data: any) => apiClient.put(`/api/media/${fileId}`, data),
  getStatistics: () => apiClient.get('/api/media/statistics'),
  getCategories: () => apiClient.get('/api/media/categories'),
  getTags: () => apiClient.get('/api/media/tags'),
};

// Users API
export const usersApi = {
  getUsers: () => apiClient.get('/api/users'),
  createUser: (userData: any) => apiClient.post('/api/users', userData),
  updateUser: (userId: string, userData: any) => apiClient.put(`/api/users/${userId}`, userData),
  deleteUser: (userId: string) => apiClient.delete(`/api/users/${userId}`),
  updateProfile: (profileData: any) => apiClient.put('/api/users/profile', profileData),
  changePassword: (passwordData: any) => apiClient.post('/api/users/change-password', passwordData),
};

// Settings API
export const settingsApi = {
  getSettings: () => apiClient.get('/api/settings'),
  updateSettings: (settings: any) => apiClient.put('/api/settings', settings),
  resetSettings: () => apiClient.post('/api/settings/reset'),
};

// Pages API
export const pagesApi = {
  getPages: () => apiClient.get('/api/pages'),
  getPage: (id: string) => apiClient.get(`/api/pages/${id}`),
  createPage: (pageData: any) => apiClient.post('/api/pages', pageData),
  updatePage: (id: string, pageData: any) => apiClient.put(`/api/pages/${id}`, pageData),
  deletePage: (id: string) => apiClient.delete(`/api/pages/${id}`),
  getTemplates: () => apiClient.get('/api/pages/templates'),
  createTemplate: (templateData: any) => apiClient.post('/api/pages/templates', templateData),
};

// Forms API
export const formsApi = {
  submitContact: (formData: any) => apiClient.post('/api/forms/contact', formData),
  submitConsultation: (formData: any) => apiClient.post('/api/forms/consultation', formData),
  submitEstimate: (formData: any) => apiClient.post('/api/forms/estimate', formData),
  submitEmergency: (formData: any) => apiClient.post('/api/forms/emergency', formData),
  submitFinancing: (formData: any) => apiClient.post('/api/forms/financing', formData),
};

// Mock API Functions (for development)
export const mockApi = {
  analytics: {
    overview: () => Promise.resolve({
      success: true,
      data: {
        totalVisitors: 1250,
        pageViews: 3420,
        avgSessionDuration: '3m 5s',
        bounceRate: '42.5%',
        trends: {
          visitors: '+15.2%',
          pageViews: '+12.8%',
          sessionDuration: '+8.1%',
          bounceRate: '-2.3%'
        }
      }
    }),
    topPages: () => Promise.resolve({
      success: true,
      data: [
        { path: '/', views: 1250, title: 'Home Page' },
        { path: '/residential-roofing', views: 890, title: 'Residential Roofing' },
        { path: '/commercial-roofing', views: 675, title: 'Commercial Roofing' },
        { path: '/contact', views: 445, title: 'Contact' },
        { path: '/about', views: 320, title: 'About Us' },
        { path: '/gallery', views: 285, title: 'Gallery' },
        { path: '/emergency-roofing', views: 210, title: 'Emergency Roofing' }
      ]
    }),
    traffic: () => Promise.resolve({
      success: true,
      data: [
        { source: 'Organic Search', visitors: 525, percentage: 42 },
        { source: 'Direct', visitors: 350, percentage: 28 },
        { source: 'Social Media', visitors: 213, percentage: 17 },
        { source: 'Referral', visitors: 125, percentage: 10 },
        { source: 'Email', visitors: 37, percentage: 3 }
      ]
    }),
    devices: () => Promise.resolve({
      success: true,
      data: {
        mobile: 65,
        desktop: 28,
        tablet: 7
      }
    }),
    getStatistics: () => Promise.resolve({
      success: true,
      data: {
        totalSessions: 1250,
        totalPageViews: 3420,
        avgSessionDuration: 185.3,
        bounceRate: 42.5,
        conversionRate: 3.8,
        topReferrers: [
          { name: 'Google', sessions: 525 },
          { name: 'Direct', sessions: 350 },
          { name: 'Facebook', sessions: 150 },
          { name: 'Local Directory', sessions: 125 }
        ]
      }
    })
  },

  media: {
    getFiles: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          filename: 'hero-roofing-1.jpg',
          original_filename: 'hero-roofing-1.jpg',
          file_url: '/images/hero-roofing-1.jpg',
          file_type: 'image',
          file_size: 2516582, // 2.4 MB in bytes
          width: 1920,
          height: 1080,
          alt_text: 'Professional roofing installation',
          upload_date: '2024-12-01T10:30:00Z',
          uploaded_by_name: 'John Smith'
        },
        {
          id: '2',
          filename: 'commercial-project-1.jpg',
          original_filename: 'commercial-project-before-after.jpg',
          file_url: '/images/commercial-project-1.jpg',
          file_type: 'image',
          file_size: 3251200, // 3.1 MB in bytes
          width: 2048,
          height: 1536,
          alt_text: 'Commercial roofing project before and after',
          upload_date: '2024-11-28T14:20:00Z',
          uploaded_by_name: 'Sarah Johnson'
        },
        {
          id: '3',
          filename: 'roofing-brochure.pdf',
          original_filename: 'Roofers LLC Services Brochure.pdf',
          file_url: '/documents/roofing-brochure.pdf',
          file_type: 'document',
          file_size: 1887436, // 1.8 MB in bytes
          upload_date: '2024-11-25T09:15:00Z',
          uploaded_by_name: 'Mike Rodriguez'
        }
      ]
    }),
    uploadFile: (formData: FormData) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            data: {
              files: [{
                id: Date.now().toString(),
                filename: 'uploaded-file.jpg',
                original_filename: 'uploaded-file.jpg',
                file_url: '/uploads/uploaded-file.jpg',
                file_type: 'image',
                file_size: 1258291,
                upload_date: new Date().toISOString()
              }],
              count: 1
            }
          });
        }, 1000);
      });
    },
    getStatistics: () => Promise.resolve({
      success: true,
      data: {
        total_files: 15,
        total_size: 45678901,
        image_count: 12,
        video_count: 1,
        document_count: 2,
        other_count: 0
      }
    }),
    deleteFile: (fileId: string) => Promise.resolve({
      success: true,
      message: 'File deleted successfully'
    }),
    updateFile: (fileId: string, data: any) => Promise.resolve({
      success: true,
      data: { id: fileId, ...data }
    })
  },

  users: {
    getUsers: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          roles: 'Administrator',
          role_slugs: 'admin',
          status: 'active',
          phone: '(*************',
          address: 'Miami, Florida',
          email_verified: true,
          last_login_at: '2024-12-01T10:30:00Z',
          created_at: '2024-01-15T09:00:00Z'
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          roles: 'Administrator',
          role_slugs: 'admin',
          status: 'active',
          phone: '(*************',
          address: 'Miami, Florida',
          email_verified: true,
          last_login_at: '2024-12-01T08:15:00Z',
          created_at: '2024-02-01T10:30:00Z'
        },
        {
          id: '3',
          name: 'Mike Rodriguez',
          email: '<EMAIL>',
          roles: 'Editor',
          role_slugs: 'editor',
          status: 'active',
          phone: '(*************',
          address: 'Miami, Florida',
          email_verified: true,
          last_login_at: '2024-11-30T16:45:00Z',
          created_at: '2024-03-15T14:20:00Z'
        },
        {
          id: '4',
          name: 'Lisa Chen',
          email: '<EMAIL>',
          roles: 'Viewer',
          role_slugs: 'viewer',
          status: 'active',
          phone: '(*************',
          address: 'Miami, Florida',
          email_verified: true,
          last_login_at: '2024-11-29T14:20:00Z',
          created_at: '2024-04-10T11:45:00Z'
        },
        {
          id: '5',
          name: 'David Wilson',
          email: '<EMAIL>',
          roles: 'Viewer',
          role_slugs: 'viewer',
          status: 'active',
          phone: '(*************',
          address: 'Miami, Florida',
          email_verified: true,
          last_login_at: '2024-11-28T09:30:00Z',
          created_at: '2024-05-20T16:15:00Z'
        }
      ]
    }),
    createUser: (userData: any) => Promise.resolve({
      success: true,
      data: { id: Date.now().toString(), ...userData, created_at: new Date().toISOString() }
    }),
    updateUser: (userId: string, userData: any) => Promise.resolve({
      success: true,
      data: { id: userId, ...userData, updated_at: new Date().toISOString() }
    }),
    deleteUser: (userId: string) => Promise.resolve({
      success: true,
      message: 'User deleted successfully'
    }),
    getUserActivity: (userId: string) => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          action_type: 'login',
          action_description: 'User logged in successfully',
          ip_address: '*************',
          created_at: new Date(Date.now() - 3600000).toISOString()
        },
        {
          id: '2',
          action_type: 'page_edit',
          action_description: 'Edited Home page content',
          ip_address: '*************',
          created_at: new Date(Date.now() - 2700000).toISOString()
        }
      ]
    })
  },

  settings: {
    getSettings: () => Promise.resolve({
      success: true,
      data: {
        general: {
          siteName: 'Roofers LLC',
          siteDescription: 'Professional roofing services in Florida',
          contactEmail: '<EMAIL>',
          contactPhone: '(*************',
          address: 'Miami, Florida',
          timezone: 'America/New_York'
        },
        cms: {
          autoSave: true,
          enableVersioning: true,
          maxRevisions: 10,
          enableComments: false,
          defaultEditor: 'puck'
        }
      }
    }),
    updateSettings: (settings: any) => Promise.resolve({
      success: true,
      data: settings
    })
  },

  pages: {
    getPages: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          title: 'Home Page',
          slug: 'home',
          path: '/',
          status: 'published',
          is_homepage: true,
          created_by_name: 'Admin User',
          created_at: '2024-01-15T09:00:00Z',
          updated_at: '2024-12-01T10:30:00Z',
          published_at: '2024-01-15T09:00:00Z'
        },
        {
          id: '2',
          title: 'About Us',
          slug: 'about',
          path: '/about',
          status: 'published',
          is_homepage: false,
          created_by_name: 'Admin User',
          created_at: '2024-01-15T09:15:00Z',
          updated_at: '2024-11-28T14:20:00Z',
          published_at: '2024-01-15T09:15:00Z'
        },
        {
          id: '3',
          title: 'Contact',
          slug: 'contact',
          path: '/contact',
          status: 'published',
          is_homepage: false,
          created_by_name: 'Admin User',
          created_at: '2024-01-15T09:30:00Z',
          updated_at: '2024-11-25T16:45:00Z',
          published_at: '2024-01-15T09:30:00Z'
        },
        {
          id: '4',
          title: 'New Service Page',
          slug: 'new-service',
          path: '/new-service',
          status: 'draft',
          is_homepage: false,
          template_name: 'Service Page',
          created_by_name: 'Editor User',
          created_at: '2024-12-01T08:00:00Z',
          updated_at: '2024-12-01T08:00:00Z'
        }
      ]
    }),
    getTemplates: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          name: 'Service Page',
          slug: 'service-page',
          description: 'Template for service-related pages',
          is_active: true
        },
        {
          id: '2',
          name: 'Landing Page',
          slug: 'landing-page',
          description: 'Template for marketing landing pages',
          is_active: true
        },
        {
          id: '3',
          name: 'Content Page',
          slug: 'content-page',
          description: 'Template for general content pages',
          is_active: true
        }
      ]
    }),
    createPage: (pageData: any) => Promise.resolve({
      success: true,
      data: { id: Date.now().toString(), ...pageData }
    }),
    updatePage: (id: string, pageData: any) => Promise.resolve({
      success: true,
      data: { id, ...pageData }
    }),
    deletePage: (id: string) => Promise.resolve({
      success: true,
      message: 'Page deleted successfully'
    })
  },

  forms: {
    submitContact: (formData: any) => Promise.resolve({
      success: true,
      data: {
        id: Date.now().toString(),
        message: 'Contact form submitted successfully',
        submittedAt: new Date().toISOString(),
        formData
      }
    }),
    submitConsultation: (formData: any) => Promise.resolve({
      success: true,
      data: {
        id: Date.now().toString(),
        message: 'Consultation request submitted successfully',
        submittedAt: new Date().toISOString(),
        formData
      }
    }),
    submitEstimate: (formData: any) => Promise.resolve({
      success: true,
      data: {
        id: Date.now().toString(),
        message: 'Estimate request submitted successfully',
        submittedAt: new Date().toISOString(),
        formData
      }
    }),
    submitEmergency: (formData: any) => Promise.resolve({
      success: true,
      data: {
        id: Date.now().toString(),
        message: 'Emergency request submitted successfully',
        submittedAt: new Date().toISOString(),
        formData
      }
    }),
    submitFinancing: (formData: any) => Promise.resolve({
      success: true,
      data: {
        id: Date.now().toString(),
        message: 'Financing application submitted successfully',
        submittedAt: new Date().toISOString(),
        formData
      }
    })
  }
};

// Development mode flag
const isDevelopment = process.env.NODE_ENV === 'development';

// Export the appropriate API based on environment
export const api = isDevelopment ? mockApi : {
  analytics: analyticsApi,
  media: mediaApi,
  users: usersApi,
  settings: settingsApi,
  pages: pagesApi,
  forms: formsApi
};