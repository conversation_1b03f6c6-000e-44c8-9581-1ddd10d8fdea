// Temporary API stub for form handling
// This will be replaced with PHP backend integration later

export interface EmailPayload {
  to: string;
  subject: string;
  message: string;
}

export const sendEmail = async (data: EmailPayload): Promise<void> => {
  // For development, log the email data and simulate a successful API call
  if (process.env.NODE_ENV === 'development') {
    console.log('Email data:', data);
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Promise.resolve();
  }
  
  // In production, this would be replaced with actual API call to PHP backend
  return Promise.resolve();
};