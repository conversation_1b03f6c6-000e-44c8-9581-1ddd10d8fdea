// import { config } from '@/config/environment'; // Temporarily commented out for debugging

// Legacy email interface (preserved for compatibility)
export interface EmailPayload {
  to: string;
  subject: string;
  message: string;
}

export const sendEmail = async (data: EmailPayload): Promise<void> => {
  // For development, log the email data and simulate a successful API call
  if (process.env.NODE_ENV === 'development') {
    console.log('Email data:', data);
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Promise.resolve();
  }

  // In production, this would be replaced with actual API call to PHP backend
  return Promise.resolve();
};

// API Base Configuration
const API_BASE_URL = 'http://localhost:3001'; // Hardcoded for debugging

// Generic API Response Type
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Generic API Client
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'An error occurred',
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    });
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Analytics API
export const analyticsApi = {
  getOverview: () => apiClient.get('/api/analytics/overview'),
  getTopPages: () => apiClient.get('/api/analytics/pages'),
  getTrafficSources: () => apiClient.get('/api/analytics/traffic'),
  getDeviceBreakdown: () => apiClient.get('/api/analytics/devices'),
};

// Media API
export const mediaApi = {
  getFiles: () => apiClient.get('/api/media'),
  uploadFile: (formData: FormData) => apiClient.upload('/api/media/upload', formData),
  deleteFile: (fileId: string) => apiClient.delete(`/api/media/${fileId}`),
  updateFile: (fileId: string, data: any) => apiClient.put(`/api/media/${fileId}`, data),
};

// Users API
export const usersApi = {
  getUsers: () => apiClient.get('/api/users'),
  createUser: (userData: any) => apiClient.post('/api/users', userData),
  updateUser: (userId: string, userData: any) => apiClient.put(`/api/users/${userId}`, userData),
  deleteUser: (userId: string) => apiClient.delete(`/api/users/${userId}`),
  updateProfile: (profileData: any) => apiClient.put('/api/users/profile', profileData),
  changePassword: (passwordData: any) => apiClient.post('/api/users/change-password', passwordData),
};

// Settings API
export const settingsApi = {
  getSettings: () => apiClient.get('/api/settings'),
  updateSettings: (settings: any) => apiClient.put('/api/settings', settings),
  resetSettings: () => apiClient.post('/api/settings/reset'),
};

// Mock API Functions (for development)
export const mockApi = {
  analytics: {
    overview: () => Promise.resolve({
      success: true,
      data: {
        totalVisitors: 12543,
        pageViews: 45678,
        avgSessionDuration: '3m 24s',
        bounceRate: '42%',
        trends: {
          visitors: '+12.5%',
          pageViews: '+8.3%',
          sessionDuration: '+5.2%',
          bounceRate: '-3.1%'
        }
      }
    }),
    topPages: () => Promise.resolve({
      success: true,
      data: [
        { path: '/', views: 8234, title: 'Home Page' },
        { path: '/residential-roofing', views: 3456, title: 'Residential Roofing' },
        { path: '/commercial-roofing', views: 2987, title: 'Commercial Roofing' },
        { path: '/emergency-roofing', views: 2134, title: 'Emergency Roofing' },
        { path: '/contact', views: 1876, title: 'Contact' }
      ]
    }),
    traffic: () => Promise.resolve({
      success: true,
      data: [
        { source: 'Organic Search', visitors: 5234, percentage: 42 },
        { source: 'Direct', visitors: 3456, percentage: 28 },
        { source: 'Social Media', visitors: 2134, percentage: 17 },
        { source: 'Referral', visitors: 1234, percentage: 10 },
        { source: 'Email', visitors: 485, percentage: 3 }
      ]
    }),
    devices: () => Promise.resolve({
      success: true,
      data: {
        mobile: 65,
        desktop: 28,
        tablet: 7
      }
    })
  },

  media: {
    getFiles: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          name: 'hero-roofing-1.jpg',
          type: 'image',
          size: '2.4 MB',
          dimensions: '1920x1080',
          uploadDate: '2024-12-01',
          url: '/images/hero-roofing-1.jpg',
          thumbnail: '/images/thumbnails/hero-roofing-1.jpg'
        }
      ]
    }),
    uploadFile: (formData: FormData) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            data: {
              id: Date.now().toString(),
              name: 'uploaded-file.jpg',
              type: 'image',
              size: '1.2 MB',
              uploadDate: new Date().toISOString().split('T')[0],
              url: '/images/uploaded-file.jpg'
            }
          });
        }, 1000);
      });
    }
  },

  users: {
    getUsers: () => Promise.resolve({
      success: true,
      data: [
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          lastLogin: '2024-12-01T10:30:00Z',
          createdAt: '2024-01-15T09:00:00Z'
        }
      ]
    }),
    createUser: (userData: any) => Promise.resolve({
      success: true,
      data: { id: Date.now().toString(), ...userData }
    }),
    updateUser: (userId: string, userData: any) => Promise.resolve({
      success: true,
      data: { id: userId, ...userData }
    })
  },

  settings: {
    getSettings: () => Promise.resolve({
      success: true,
      data: {
        general: {
          siteName: 'Roofers LLC',
          siteDescription: 'Professional roofing services in Florida',
          contactEmail: '<EMAIL>',
          contactPhone: '(*************',
          address: 'Miami, Florida',
          timezone: 'America/New_York'
        },
        cms: {
          autoSave: true,
          enableVersioning: true,
          maxRevisions: 10,
          enableComments: false,
          defaultEditor: 'puck'
        }
      }
    }),
    updateSettings: (settings: any) => Promise.resolve({
      success: true,
      data: settings
    })
  }
};

// Development mode flag
const isDevelopment = process.env.NODE_ENV === 'development';

// Export the appropriate API based on environment
export const api = isDevelopment ? mockApi : {
  analytics: analyticsApi,
  media: mediaApi,
  users: usersApi,
  settings: settingsApi
};