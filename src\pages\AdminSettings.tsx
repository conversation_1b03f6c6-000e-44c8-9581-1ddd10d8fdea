import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import AdminPageHeader from '@/components/AdminPageHeader';
import { useTheme, ThemeToggle } from '@/contexts/ThemeContext';
import {
  Settings,
  Globe,
  Database,
  Shield,
  Palette,
  Share2,
  Sparkles,
  Moon,
  Sun
} from 'lucide-react';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('theme');
  const { theme, setTheme } = useTheme();

  const tabs = [
    { id: 'general', name: 'General', icon: Globe },
    { id: 'theme', name: 'Theme & UI', icon: Moon },
    { id: 'social', name: 'Social Media', icon: Share2 },
    { id: 'ai', name: 'AI & Automation', icon: Sparkles },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'api', name: 'API', icon: Database }
  ];

  const renderThemeSettings = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <Moon className="h-5 w-5 text-blue-600" />
          <h3 className="font-medium text-blue-900">Theme & User Interface</h3>
        </div>
        <p className="text-sm text-blue-700">Configure the visual appearance and user experience settings.</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Current Theme</label>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <input
                type="radio"
                id="theme-light"
                name="theme"
                value="light"
                checked={theme === 'light'}
                onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label htmlFor="theme-light" className="flex items-center space-x-2 cursor-pointer">
                <Sun className="h-4 w-4 text-yellow-500" />
                <span>Light Mode</span>
              </label>
            </div>
            <div className="flex items-center space-x-3">
              <input
                type="radio"
                id="theme-dark"
                name="theme"
                value="dark"
                checked={theme === 'dark'}
                onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label htmlFor="theme-dark" className="flex items-center space-x-2 cursor-pointer">
                <Moon className="h-4 w-4 text-blue-500" />
                <span>Dark Mode</span>
              </label>
            </div>
            <div className="flex items-center space-x-3">
              <input
                type="radio"
                id="theme-auto"
                name="theme"
                value="auto"
                checked={theme === 'auto'}
                onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'auto')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label htmlFor="theme-auto" className="flex items-center space-x-2 cursor-pointer">
                <span className="text-lg">🔄</span>
                <span>Auto (System)</span>
              </label>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Theme Preview</label>
          <div className="border rounded-lg p-4 bg-white dark:bg-gray-800">
            <ThemeToggle />
            <div className="mt-3 space-y-2">
              <div className="h-2 bg-blue-200 dark:bg-blue-700 rounded"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-medium text-yellow-900 mb-2">Theme Information</h3>
        <div className="text-sm text-yellow-700 space-y-1">
          <p>• Light Mode: Clean, professional appearance suitable for daytime use</p>
          <p>• Dark Mode: Reduced eye strain for low-light environments</p>
          <p>• Auto Mode: Automatically switches based on system settings</p>
          <p>• Theme preferences are saved per user and persist across sessions</p>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'theme': 
        return renderThemeSettings();
      default: 
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Settings panel for {activeTab} coming soon...</p>
            <p className="text-sm text-gray-400 mt-2">This feature will be available in a future update.</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-8">
      <AdminPageHeader
        title="Settings"
        description="Configure system settings and preferences"
      />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700'
                      }`}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {React.createElement(tabs.find(t => t.id === activeTab)?.icon || Settings, { className: "h-5 w-5" })}
                <span>{tabs.find(t => t.id === activeTab)?.name} Settings</span>
              </CardTitle>
              <CardDescription>
                Configure {tabs.find(t => t.id === activeTab)?.name.toLowerCase()} settings for your application
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
