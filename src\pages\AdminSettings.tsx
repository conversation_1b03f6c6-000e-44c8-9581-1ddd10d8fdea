import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { 
  Settings, 
  Globe, 
  Database, 
  Shield, 
  Mail, 
  Palette,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Bell,
  Lock,
  Eye,
  EyeOff,
  Upload,
  Image,
  Share2
} from 'lucide-react';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('general');
  const [showApiKey, setShowApiKey] = useState(false);
  const [settings, setSettings] = useState({
    general: {
      siteName: 'Roofers LLC',
      siteDescription: 'Professional roofing services in Florida',
      contactEmail: '<EMAIL>',
      contactPhone: '(*************',
      address: 'Miami, Florida',
      timezone: 'America/New_York'
    },
    cms: {
      autoSave: true,
      enableVersioning: true,
      maxRevisions: 10,
      enableComments: false,
      defaultEditor: 'puck'
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginAttempts: 5
    },
    notifications: {
      emailNotifications: true,
      systemAlerts: true,
      userRegistrations: true,
      contentChanges: false
    },
    api: {
      apiKey: 'sk_live_1234567890abcdef',
      webhookUrl: 'https://roofers.llc/api/webhook',
      rateLimitEnabled: true,
      rateLimitRequests: 1000
    },
    branding: {
      logoUrl: '',
      logoUrlDark: '',
      heroBackgroundUrl: '',
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af'
    },
    social: {
      facebookUrl: 'https://facebook.com',
      twitterUrl: 'https://twitter.com',
      instagramUrl: 'https://instagram.com',
      linkedinUrl: '',
      youtubeUrl: ''
    }
  });

  const tabs = [
    { id: 'general', name: 'General', icon: Globe },
    { id: 'cms', name: 'CMS', icon: Settings },
    { id: 'branding', name: 'Branding', icon: Palette },
    { id: 'social', name: 'Social Media', icon: Share2 },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'api', name: 'API', icon: Database }
  ];

  const handleSave = () => {
    // In a real app, this would save to the database
    console.log('Saving settings:', settings);
    // Show success message
  };

  const handleReset = () => {
    // In a real app, this would reset to default values
    console.log('Resetting settings to defaults');
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
          <input
            type="text"
            value={settings.general.siteName}
            onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
          <input
            type="email"
            value={settings.general.contactEmail}
            onChange={(e) => updateSetting('general', 'contactEmail', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
          <input
            type="tel"
            value={settings.general.contactPhone}
            onChange={(e) => updateSetting('general', 'contactPhone', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
          <select
            value={settings.general.timezone}
            onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Chicago">Central Time</option>
            <option value="America/Denver">Mountain Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
        <textarea
          value={settings.general.siteDescription}
          onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
          rows={3}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
        <input
          type="text"
          value={settings.general.address}
          onChange={(e) => updateSetting('general', 'address', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  );

  const renderCMSSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Auto Save</h3>
          <p className="text-sm text-gray-600">Automatically save content changes</p>
        </div>
        <input
          type="checkbox"
          checked={settings.cms.autoSave}
          onChange={(e) => updateSetting('cms', 'autoSave', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Version Control</h3>
          <p className="text-sm text-gray-600">Keep track of content revisions</p>
        </div>
        <input
          type="checkbox"
          checked={settings.cms.enableVersioning}
          onChange={(e) => updateSetting('cms', 'enableVersioning', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Max Revisions</label>
          <input
            type="number"
            value={settings.cms.maxRevisions}
            onChange={(e) => updateSetting('cms', 'maxRevisions', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Default Editor</label>
          <select
            value={settings.cms.defaultEditor}
            onChange={(e) => updateSetting('cms', 'defaultEditor', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="puck">Puck CMS</option>
            <option value="markdown">Markdown</option>
            <option value="rich-text">Rich Text</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Two-Factor Authentication</h3>
          <p className="text-sm text-gray-600">Add an extra layer of security</p>
        </div>
        <input
          type="checkbox"
          checked={settings.security.twoFactorAuth}
          onChange={(e) => updateSetting('security', 'twoFactorAuth', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
          <input
            type="number"
            value={settings.security.sessionTimeout}
            onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days)</label>
          <input
            type="number"
            value={settings.security.passwordExpiry}
            onChange={(e) => updateSetting('security', 'passwordExpiry', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
          <input
            type="number"
            value={settings.security.loginAttempts}
            onChange={(e) => updateSetting('security', 'loginAttempts', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h3 className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h3>
            <p className="text-sm text-gray-600">
              {key === 'emailNotifications' && 'Receive notifications via email'}
              {key === 'systemAlerts' && 'Get alerts about system issues'}
              {key === 'userRegistrations' && 'Notify when new users register'}
              {key === 'contentChanges' && 'Alert when content is modified'}
            </p>
          </div>
          <input
            type="checkbox"
            checked={value as boolean}
            onChange={(e) => updateSetting('notifications', key, e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </div>
      ))}
    </div>
  );

  const renderSocialSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Facebook URL</label>
          <input
            type="url"
            value={settings.social.facebookUrl}
            onChange={(e) => updateSetting('social', 'facebookUrl', e.target.value)}
            placeholder="https://facebook.com/yourpage"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Twitter URL</label>
          <input
            type="url"
            value={settings.social.twitterUrl}
            onChange={(e) => updateSetting('social', 'twitterUrl', e.target.value)}
            placeholder="https://twitter.com/yourhandle"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Instagram URL</label>
          <input
            type="url"
            value={settings.social.instagramUrl}
            onChange={(e) => updateSetting('social', 'instagramUrl', e.target.value)}
            placeholder="https://instagram.com/yourhandle"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn URL</label>
          <input
            type="url"
            value={settings.social.linkedinUrl}
            onChange={(e) => updateSetting('social', 'linkedinUrl', e.target.value)}
            placeholder="https://linkedin.com/company/yourcompany"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">YouTube URL</label>
          <input
            type="url"
            value={settings.social.youtubeUrl}
            onChange={(e) => updateSetting('social', 'youtubeUrl', e.target.value)}
            placeholder="https://youtube.com/channel/yourchannel"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Social Media Preview</h3>
        <p className="text-sm text-blue-700 mb-3">These URLs will be used in your website footer and social media links.</p>
        <div className="space-y-2">
          {settings.social.facebookUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Facebook:</span>
              <a href={settings.social.facebookUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.facebookUrl}
              </a>
            </div>
          )}
          {settings.social.twitterUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Twitter:</span>
              <a href={settings.social.twitterUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.twitterUrl}
              </a>
            </div>
          )}
          {settings.social.instagramUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Instagram:</span>
              <a href={settings.social.instagramUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.instagramUrl}
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderBrandingSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Logo (Light Background)</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {settings.branding.logoUrl ? (
              <div className="space-y-2">
                <img src={settings.branding.logoUrl} alt="Logo" className="h-16 mx-auto" />
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Change Logo
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Image className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="text-sm text-gray-600">Upload logo for light backgrounds</p>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </Button>
              </div>
            )}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Logo (Dark Background)</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900">
            {settings.branding.logoUrlDark ? (
              <div className="space-y-2">
                <img src={settings.branding.logoUrlDark} alt="Logo Dark" className="h-16 mx-auto" />
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Change Logo
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Image className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="text-sm text-gray-400">Upload logo for dark backgrounds</p>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Hero Background Image</label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          {settings.branding.heroBackgroundUrl ? (
            <div className="space-y-2">
              <img src={settings.branding.heroBackgroundUrl} alt="Hero Background" className="h-32 w-full object-cover rounded mx-auto" />
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Change Background
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <Image className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-600">Upload hero background image</p>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Upload Background
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={settings.branding.primaryColor}
              onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={settings.branding.primaryColor}
              onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
              className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Secondary Color</label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={settings.branding.secondaryColor}
              onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={settings.branding.secondaryColor}
              onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
              className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderAPISettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">API Key</label>
        <div className="flex items-center space-x-2">
          <input
            type={showApiKey ? 'text' : 'password'}
            value={settings.api.apiKey}
            onChange={(e) => updateSetting('api', 'apiKey', e.target.value)}
            className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowApiKey(!showApiKey)}
          >
            {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
        <input
          type="url"
          value={settings.api.webhookUrl}
          onChange={(e) => updateSetting('api', 'webhookUrl', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Rate Limiting</h3>
          <p className="text-sm text-gray-600">Limit API requests per hour</p>
        </div>
        <input
          type="checkbox"
          checked={settings.api.rateLimitEnabled}
          onChange={(e) => updateSetting('api', 'rateLimitEnabled', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      {settings.api.rateLimitEnabled && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Requests per Hour</label>
          <input
            type="number"
            value={settings.api.rateLimitRequests}
            onChange={(e) => updateSetting('api', 'rateLimitRequests', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general': return renderGeneralSettings();
      case 'cms': return renderCMSSettings();
      case 'branding': return renderBrandingSettings();
      case 'social': return renderSocialSettings();
      case 'security': return renderSecuritySettings();
      case 'notifications': return renderNotificationSettings();
      case 'api': return renderAPISettings();
      default: return renderGeneralSettings();
    }
  };

  return (
    <div className="space-y-8">
      <AdminPageHeader
        title="Settings"
        description="Configure system settings and preferences"
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </AdminPageHeader>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700'
                      }`}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {React.createElement(tabs.find(t => t.id === activeTab)?.icon || Settings, { className: "h-5 w-5" })}
                <span>{tabs.find(t => t.id === activeTab)?.name} Settings</span>
              </CardTitle>
              <CardDescription>
                Configure {tabs.find(t => t.id === activeTab)?.name.toLowerCase()} settings for your application
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
