import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { 
  Settings, 
  Globe, 
  Database, 
  Shield, 
  Mail, 
  Palette,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  Bell,
  Lock,
  Eye,
  EyeOff,
  Upload,
  Image,
  Share2,
  Sparkles,
  Moon,
  Sun
} from 'lucide-react';
import { useTheme, ThemeToggle } from '@/contexts/ThemeContext';

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState('general');
  const [showApiKey, setShowApiKey] = useState(false);
  const { theme, setTheme } = useTheme();
  const [settings, setSettings] = useState({
    general: {
      siteName: 'Roofers LLC',
      siteDescription: 'Professional roofing services in Florida',
      contactEmail: '<EMAIL>',
      contactPhone: '(*************',
      address: 'Miami, Florida',
      timezone: 'America/New_York'
    },
    cms: {
      autoSave: true,
      enableVersioning: true,
      maxRevisions: 10,
      enableComments: false,
      defaultEditor: 'puck'
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginAttempts: 5
    },
    notifications: {
      emailNotifications: true,
      systemAlerts: true,
      userRegistrations: true,
      contentChanges: false
    },
    api: {
      apiKey: 'sk_live_1234567890abcdef',
      webhookUrl: 'https://roofers.llc/api/webhook',
      rateLimitEnabled: true,
      rateLimitRequests: 1000
    },
    branding: {
      logoUrl: '',
      logoUrlDark: '',
      heroBackgroundUrl: '',
      primaryColor: '#2563eb',
      secondaryColor: '#1e40af'
    },
    social: {
      facebookUrl: 'https://facebook.com',
      twitterUrl: 'https://twitter.com',
      instagramUrl: 'https://instagram.com',
      linkedinUrl: '',
      youtubeUrl: ''
    },
    ai: {
      defaultProvider: 'openai-dalle3',
      openaiApiKey: '',
      openrouterApiKey: '',
      huggingfaceApiKey: '',
      replicateApiKey: '',
      monthlyBudget: 100,
      enableAutoGeneration: true,
      enableSEOOptimization: true
    },
    theme: {
      defaultTheme: 'auto',
      allowUserThemeSelection: true,
      enableDarkMode: true,
      enableAutoMode: true
    }
  });

  const tabs = [
    { id: 'general', name: 'General', icon: Globe },
    { id: 'cms', name: 'CMS', icon: Settings },
    { id: 'branding', name: 'Branding', icon: Palette },
    { id: 'social', name: 'Social Media', icon: Share2 },
    { id: 'ai', name: 'AI & Automation', icon: Sparkles },
    { id: 'theme', name: 'Theme & UI', icon: Moon },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'api', name: 'API', icon: Database }
  ];

  const handleSave = () => {
    // In a real app, this would save to the database
    console.log('Saving settings:', settings);
    // Show success message
  };

  const handleReset = () => {
    // In a real app, this would reset to default values
    console.log('Resetting settings to defaults');
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
          <input
            type="text"
            value={settings.general.siteName}
            onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
          <input
            type="email"
            value={settings.general.contactEmail}
            onChange={(e) => updateSetting('general', 'contactEmail', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
          <input
            type="tel"
            value={settings.general.contactPhone}
            onChange={(e) => updateSetting('general', 'contactPhone', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
          <select
            value={settings.general.timezone}
            onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Chicago">Central Time</option>
            <option value="America/Denver">Mountain Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
        <textarea
          value={settings.general.siteDescription}
          onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
          rows={3}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
        <input
          type="text"
          value={settings.general.address}
          onChange={(e) => updateSetting('general', 'address', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  );

  const renderCMSSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Auto Save</h3>
          <p className="text-sm text-gray-600">Automatically save content changes</p>
        </div>
        <input
          type="checkbox"
          checked={settings.cms.autoSave}
          onChange={(e) => updateSetting('cms', 'autoSave', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Version Control</h3>
          <p className="text-sm text-gray-600">Keep track of content revisions</p>
        </div>
        <input
          type="checkbox"
          checked={settings.cms.enableVersioning}
          onChange={(e) => updateSetting('cms', 'enableVersioning', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Max Revisions</label>
          <input
            type="number"
            value={settings.cms.maxRevisions}
            onChange={(e) => updateSetting('cms', 'maxRevisions', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Default Editor</label>
          <select
            value={settings.cms.defaultEditor}
            onChange={(e) => updateSetting('cms', 'defaultEditor', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="puck">Puck CMS</option>
            <option value="markdown">Markdown</option>
            <option value="rich-text">Rich Text</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Two-Factor Authentication</h3>
          <p className="text-sm text-gray-600">Add an extra layer of security</p>
        </div>
        <input
          type="checkbox"
          checked={settings.security.twoFactorAuth}
          onChange={(e) => updateSetting('security', 'twoFactorAuth', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
          <input
            type="number"
            value={settings.security.sessionTimeout}
            onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days)</label>
          <input
            type="number"
            value={settings.security.passwordExpiry}
            onChange={(e) => updateSetting('security', 'passwordExpiry', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
          <input
            type="number"
            value={settings.security.loginAttempts}
            onChange={(e) => updateSetting('security', 'loginAttempts', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h3 className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h3>
            <p className="text-sm text-gray-600">
              {key === 'emailNotifications' && 'Receive notifications via email'}
              {key === 'systemAlerts' && 'Get alerts about system issues'}
              {key === 'userRegistrations' && 'Notify when new users register'}
              {key === 'contentChanges' && 'Alert when content is modified'}
            </p>
          </div>
          <input
            type="checkbox"
            checked={value as boolean}
            onChange={(e) => updateSetting('notifications', key, e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </div>
      ))}
    </div>
  );

  const renderSocialSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Facebook URL</label>
          <input
            type="url"
            value={settings.social.facebookUrl}
            onChange={(e) => updateSetting('social', 'facebookUrl', e.target.value)}
            placeholder="https://facebook.com/yourpage"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Twitter URL</label>
          <input
            type="url"
            value={settings.social.twitterUrl}
            onChange={(e) => updateSetting('social', 'twitterUrl', e.target.value)}
            placeholder="https://twitter.com/yourhandle"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Instagram URL</label>
          <input
            type="url"
            value={settings.social.instagramUrl}
            onChange={(e) => updateSetting('social', 'instagramUrl', e.target.value)}
            placeholder="https://instagram.com/yourhandle"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn URL</label>
          <input
            type="url"
            value={settings.social.linkedinUrl}
            onChange={(e) => updateSetting('social', 'linkedinUrl', e.target.value)}
            placeholder="https://linkedin.com/company/yourcompany"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">YouTube URL</label>
          <input
            type="url"
            value={settings.social.youtubeUrl}
            onChange={(e) => updateSetting('social', 'youtubeUrl', e.target.value)}
            placeholder="https://youtube.com/channel/yourchannel"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Social Media Preview</h3>
        <p className="text-sm text-blue-700 mb-3">These URLs will be used in your website footer and social media links.</p>
        <div className="space-y-2">
          {settings.social.facebookUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Facebook:</span>
              <a href={settings.social.facebookUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.facebookUrl}
              </a>
            </div>
          )}
          {settings.social.twitterUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Twitter:</span>
              <a href={settings.social.twitterUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.twitterUrl}
              </a>
            </div>
          )}
          {settings.social.instagramUrl && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600">Instagram:</span>
              <a href={settings.social.instagramUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-800 hover:underline">
                {settings.social.instagramUrl}
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderBrandingSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Logo (Light Background)</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            {settings.branding.logoUrl ? (
              <div className="space-y-2">
                <img src={settings.branding.logoUrl} alt="Logo" className="h-16 mx-auto" />
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Change Logo
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Image className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="text-sm text-gray-600">Upload logo for light backgrounds</p>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </Button>
              </div>
            )}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Logo (Dark Background)</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-900">
            {settings.branding.logoUrlDark ? (
              <div className="space-y-2">
                <img src={settings.branding.logoUrlDark} alt="Logo Dark" className="h-16 mx-auto" />
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Change Logo
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Image className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="text-sm text-gray-400">Upload logo for dark backgrounds</p>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Logo
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Hero Background Image</label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          {settings.branding.heroBackgroundUrl ? (
            <div className="space-y-2">
              <img src={settings.branding.heroBackgroundUrl} alt="Hero Background" className="h-32 w-full object-cover rounded mx-auto" />
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Change Background
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <Image className="h-12 w-12 text-gray-400 mx-auto" />
              <p className="text-sm text-gray-600">Upload hero background image</p>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Upload Background
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={settings.branding.primaryColor}
              onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={settings.branding.primaryColor}
              onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
              className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Secondary Color</label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={settings.branding.secondaryColor}
              onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
              className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={settings.branding.secondaryColor}
              onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
              className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderThemeSettings = () => (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Moon className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-blue-900">Theme & User Interface</h3>
          </div>
          <p className="text-sm text-blue-700">Configure the visual appearance and user experience settings.</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Current Theme</label>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="radio"
                  id="theme-light"
                  name="theme"
                  value="light"
                  checked={theme === 'light'}
                  onChange={(e) => setTheme(e.target.value as any)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="theme-light" className="flex items-center space-x-2 cursor-pointer">
                  <Sun className="h-4 w-4 text-yellow-500" />
                  <span>Light Mode</span>
                </label>
              </div>
              <div className="flex items-center space-x-3">
                <input
                  type="radio"
                  id="theme-dark"
                  name="theme"
                  value="dark"
                  checked={theme === 'dark'}
                  onChange={(e) => setTheme(e.target.value as any)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="theme-dark" className="flex items-center space-x-2 cursor-pointer">
                  <Moon className="h-4 w-4 text-blue-500" />
                  <span>Dark Mode</span>
                </label>
              </div>
              <div className="flex items-center space-x-3">
                <input
                  type="radio"
                  id="theme-auto"
                  name="theme"
                  value="auto"
                  checked={theme === 'auto'}
                  onChange={(e) => setTheme(e.target.value as any)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <label htmlFor="theme-auto" className="flex items-center space-x-2 cursor-pointer">
                  <span className="text-lg">🔄</span>
                  <span>Auto (System)</span>
                </label>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Theme Preview</label>
            <div className="border rounded-lg p-4 bg-white dark:bg-gray-800">
              <ThemeToggle />
              <div className="mt-3 space-y-2">
                <div className="h-2 bg-blue-200 dark:bg-blue-700 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Allow User Theme Selection</h3>
              <p className="text-sm text-gray-600">Let users choose their preferred theme</p>
            </div>
            <input
              type="checkbox"
              checked={settings.theme.allowUserThemeSelection}
              onChange={(e) => updateSetting('theme', 'allowUserThemeSelection', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Enable Dark Mode</h3>
              <p className="text-sm text-gray-600">Allow dark theme option</p>
            </div>
            <input
              type="checkbox"
              checked={settings.theme.enableDarkMode}
              onChange={(e) => updateSetting('theme', 'enableDarkMode', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-medium">Enable Auto Mode</h3>
              <p className="text-sm text-gray-600">Allow automatic theme based on system preference</p>
            </div>
            <input
              type="checkbox"
              checked={settings.theme.enableAutoMode}
              onChange={(e) => updateSetting('theme', 'enableAutoMode', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Default Theme for New Users</label>
          <select
            value={settings.theme.defaultTheme}
            onChange={(e) => updateSetting('theme', 'defaultTheme', e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="light">Light Mode</option>
            <option value="dark">Dark Mode</option>
            <option value="auto">Auto (System Preference)</option>
          </select>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-900 mb-2">Theme Information</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>• Light Mode: Clean, professional appearance suitable for daytime use</p>
            <p>• Dark Mode: Reduced eye strain for low-light environments</p>
            <p>• Auto Mode: Automatically switches based on system settings</p>
            <p>• Theme preferences are saved per user and persist across sessions</p>
          </div>
        </div>
      </div>
    );
  };

  const renderAISettings = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <Sparkles className="h-5 w-5 text-purple-600" />
          <h3 className="font-medium text-purple-900">AI & Automation Settings</h3>
        </div>
        <p className="text-sm text-purple-700">Configure AI providers and automation features for content generation.</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Default AI Provider</label>
        <select
          value={settings.ai.defaultProvider}
          onChange={(e) => updateSetting('ai', 'defaultProvider', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="openai-dalle3">OpenAI DALL-E 3 (Premium)</option>
          <option value="openrouter">OpenRouter (Multiple Models)</option>
          <option value="huggingface">Hugging Face (Free)</option>
          <option value="replicate">Replicate Stable Diffusion</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">OpenAI API Key</label>
          <input
            type="password"
            value={settings.ai.openaiApiKey}
            onChange={(e) => updateSetting('ai', 'openaiApiKey', e.target.value)}
            placeholder="sk-..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">OpenRouter API Key</label>
          <input
            type="password"
            value={settings.ai.openrouterApiKey}
            onChange={(e) => updateSetting('ai', 'openrouterApiKey', e.target.value)}
            placeholder="sk-or-..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Hugging Face API Key</label>
          <input
            type="password"
            value={settings.ai.huggingfaceApiKey}
            onChange={(e) => updateSetting('ai', 'huggingfaceApiKey', e.target.value)}
            placeholder="hf_..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Replicate API Key</label>
          <input
            type="password"
            value={settings.ai.replicateApiKey}
            onChange={(e) => updateSetting('ai', 'replicateApiKey', e.target.value)}
            placeholder="r8_..."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Monthly Budget ($)</label>
        <input
          type="number"
          value={settings.ai.monthlyBudget}
          onChange={(e) => updateSetting('ai', 'monthlyBudget', parseInt(e.target.value))}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
        <p className="text-xs text-gray-600 mt-1">Set a monthly spending limit for AI services</p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h3 className="font-medium">Auto Image Generation</h3>
            <p className="text-sm text-gray-600">Automatically generate images for new content</p>
          </div>
          <input
            type="checkbox"
            checked={settings.ai.enableAutoGeneration}
            onChange={(e) => updateSetting('ai', 'enableAutoGeneration', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
        </div>
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div>
            <h3 className="font-medium">SEO Optimization</h3>
            <p className="text-sm text-gray-600">Auto-generate SEO metadata for images</p>
          </div>
          <input
            type="checkbox"
            checked={settings.ai.enableSEOOptimization}
            onChange={(e) => updateSetting('ai', 'enableSEOOptimization', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Usage Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-blue-600">This Month</p>
            <p className="font-semibold text-blue-900">$12.50</p>
          </div>
          <div>
            <p className="text-blue-600">Images Generated</p>
            <p className="font-semibold text-blue-900">47</p>
          </div>
          <div>
            <p className="text-blue-600">Budget Remaining</p>
            <p className="font-semibold text-blue-900">$87.50</p>
          </div>
          <div>
            <p className="text-blue-600">Success Rate</p>
            <p className="font-semibold text-blue-900">98%</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAPISettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">API Key</label>
        <div className="flex items-center space-x-2">
          <input
            type={showApiKey ? 'text' : 'password'}
            value={settings.api.apiKey}
            onChange={(e) => updateSetting('api', 'apiKey', e.target.value)}
            className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowApiKey(!showApiKey)}
          >
            {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
        <input
          type="url"
          value={settings.api.webhookUrl}
          onChange={(e) => updateSetting('api', 'webhookUrl', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h3 className="font-medium">Rate Limiting</h3>
          <p className="text-sm text-gray-600">Limit API requests per hour</p>
        </div>
        <input
          type="checkbox"
          checked={settings.api.rateLimitEnabled}
          onChange={(e) => updateSetting('api', 'rateLimitEnabled', e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
      {settings.api.rateLimitEnabled && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Requests per Hour</label>
          <input
            type="number"
            value={settings.api.rateLimitRequests}
            onChange={(e) => updateSetting('api', 'rateLimitRequests', parseInt(e.target.value))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general': return renderGeneralSettings();
      case 'cms': return renderCMSSettings();
      case 'branding': return renderBrandingSettings();
      case 'social': return renderSocialSettings();
      case 'ai': return renderAISettings();
      case 'theme': return renderThemeSettings();
      case 'security': return renderSecuritySettings();
      case 'notifications': return renderNotificationSettings();
      case 'api': return renderAPISettings();
      default: return renderGeneralSettings();
    }
  };

  return (
    <div className="space-y-8">
      <AdminPageHeader
        title="Settings"
        description="Configure system settings and preferences"
      >
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </AdminPageHeader>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                        activeTab === tab.id ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700'
                      }`}
                    >
                      <IconComponent className="h-5 w-5" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {React.createElement(tabs.find(t => t.id === activeTab)?.icon || Settings, { className: "h-5 w-5" })}
                <span>{tabs.find(t => t.id === activeTab)?.name} Settings</span>
              </CardTitle>
              <CardDescription>
                Configure {tabs.find(t => t.id === activeTab)?.name.toLowerCase()} settings for your application
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderTabContent()}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
