# Technical Context: Roofers Website

## Technologies Used

### Core Technologies
- React 18.3.1
- TypeScript 5.5.3
- Vite 5.4.1 (Build Tool)
- Node.js
- CodeIgniter (Backend PHP Framework)

### UI Framework
- Tailwind CSS 3.4.11
- shadcn/ui (Component Library)
- Radix <PERSON> (Primitive Components)
- Lucide React (Icons)
- TailwindCSS Animate

### Form Handling
- React Hook Form 7.53.0
- Zod 3.23.8 (Validation)
- @hookform/resolvers 5.0.1

### Content Management
- @measured/puck 0.18.3
- JSON-based content storage

### Routing & Navigation
- React Router DOM 6.26.2

### UI Components & Features
- Radix UI Components
  - Alert Dialog
  - Aspect Ratio
  - Avatar
  - Checkbox
  - Dialog
  - Dropdown Menu
  - Hover Card
  - Navigation Menu
  - And many more...
- Embla Carousel
- React Day Picker
- Sonner (Toast Notifications)
- <PERSON><PERSON> (Drawers)

## Development Setup

### Build Configuration
```bash
# Development
npm run dev        # Start development server
npm run lint       # Run ESLint checks

# Production
npm run build      # Build for production
npm run preview    # Preview production build
```

### Development Tools
- ESLint 9.9.0
- TypeScript ESLint
- Vite Development Server
- SWC (Fast JavaScript/TypeScript compiler)
- Autoprefixer
- PostCSS

### Environment Requirements
- Node.js
- npm/yarn
- PHP (for backend)
- Web Server (e.g., Apache/Nginx)

## Technical Constraints

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- No IE11 support required
- Mobile browser optimization

### Performance Requirements
- Fast initial load time
- Optimized bundle size
- Efficient image loading
- Responsive design across devices

### Security Requirements
- Form data validation
- CSRF protection
- Secure API endpoints
- Data sanitization
- Proper error handling

## Dependencies

### Production Dependencies
```json
{
  "@hookform/resolvers": "^5.0.1",
  "@measured/puck": "^0.18.3",
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "react-hook-form": "^7.53.0",
  "react-router-dom": "^6.26.2",
  "tailwindcss": "^3.4.11",
  "zod": "^3.23.8"
  // ... (additional dependencies listed in package.json)
}
```

### Development Dependencies
```json
{
  "@types/react": "^18.3.23",
  "@vitejs/plugin-react": "^4.3.4",
  "typescript": "^5.5.3",
  "vite": "^5.4.1"
  // ... (additional dev dependencies listed in package.json)
}
```

## Tool Usage Patterns

### Code Organization
- Components in feature-based directories
- Shared UI components in ui/ directory
- Page components in pages/ directory
- Business logic in lib/ directory
- Hooks in hooks/ directory

### Styling Patterns
- Tailwind CSS utility classes
- CSS modules when needed
- Component-specific styles
- Global styles in index.css

### Form Patterns
- React Hook Form for form state
- Zod schemas for validation
- Custom form components
- Error handling patterns

### Content Management
- JSON content files
- Puck CMS integration
- Dynamic content loading
- Content schemas

### Testing
- Component testing (to be implemented)
- Form validation testing
- API integration testing
- E2E testing (to be implemented)

## Development Workflow

### 1. Local Development
```bash
# Start development server
npm run dev

# Access development site
http://localhost:5173
```

### 2. Code Quality
```bash
# Run linting
npm run lint

# Type checking
tsc --noEmit
```

### 3. Building
```bash
# Production build
npm run build

# Preview build
npm run preview
```

### 4. Deployment
- Build static assets
- Deploy to web server
- Configure backend API
- Set up environment variables

## Configuration Files
- tsconfig.app.json (TypeScript)
- tailwind.config.ts (Tailwind CSS)
- postcss.config.js (PostCSS)
- eslint.config.js (ESLint)
- components.json (shadcn/ui)