import { mysqlTable, varchar, text, timestamp, int, boolean, json, primaryKey, index } from 'drizzle-orm/mysql-core';
import type { AdapterAccount } from '@auth/core/adapters';

// Auth.js required tables
export const users = mysqlTable('users', {
  id: varchar('id', { length: 255 }).notNull().primaryKey(),
  name: varchar('name', { length: 255 }),
  email: varchar('email', { length: 255 }).unique(),
  emailVerified: timestamp('email_verified', { mode: 'date', fsp: 6 }),
  image: varchar('image', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  emailIdx: index('user_email_index').on(table.email),
}));

export const accounts = mysqlTable('accounts', {
  id: varchar('id', { length: 255 }).notNull().primaryKey(),
  userId: varchar('user_id', { length: 255 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: varchar('type', { length: 255 }).$type<AdapterAccount['type']>().notNull(),
  provider: varchar('provider', { length: 255 }).notNull(),
  providerAccountId: varchar('provider_account_id', { length: 255 }).notNull(),
  refreshToken: text('refresh_token'),
  accessToken: text('access_token'),
  expiresAt: int('expires_at'),
  tokenType: varchar('token_type', { length: 255 }),
  scope: varchar('scope', { length: 255 }),
  idToken: text('id_token'),
  sessionState: varchar('session_state', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  userIdIdx: index('account_user_id_index').on(table.userId),
  providerAccountIdIdx: index('account_provider_id_index').on(table.providerAccountId),
}));

export const sessions = mysqlTable('sessions', {
  id: varchar('id', { length: 255 }).notNull().primaryKey(),
  sessionToken: varchar('session_token', { length: 255 }).notNull().unique(),
  userId: varchar('user_id', { length: 255 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
  expires: timestamp('expires', { mode: 'date', fsp: 6 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  userIdIdx: index('session_user_id_index').on(table.userId),
}));

export const verificationTokens = mysqlTable('verification_tokens', {
  identifier: varchar('identifier', { length: 255 }).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  expires: timestamp('expires', { mode: 'date', fsp: 6 }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.identifier, table.token] }),
}));

// RBAC tables
export const roles = mysqlTable('roles', {
  id: varchar('id', { length: 36 }).notNull().primaryKey(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  nameIdx: index('role_name_index').on(table.name),
}));

export const permissions = mysqlTable('permissions', {
  id: varchar('id', { length: 36 }).notNull().primaryKey(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  nameIdx: index('permission_name_index').on(table.name),
}));

export const rolePermissions = mysqlTable('role_permissions', {
  roleId: varchar('role_id', { length: 36 }).notNull().references(() => roles.id, { onDelete: 'cascade' }),
  permissionId: varchar('permission_id', { length: 36 }).notNull().references(() => permissions.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.roleId, table.permissionId] }),
}));

export const userRoles = mysqlTable('user_roles', {
  userId: varchar('user_id', { length: 255 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
  roleId: varchar('role_id', { length: 36 }).notNull().references(() => roles.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.roleId] }),
  userIdIdx: index('user_roles_user_id_index').on(table.userId),
  roleIdIdx: index('user_roles_role_id_index').on(table.roleId),
}));

export const authAuditLogs = mysqlTable('auth_audit_logs', {
  id: varchar('id', { length: 36 }).notNull().primaryKey(),
  userId: varchar('user_id', { length: 255 }).references(() => users.id, { onDelete: 'set null' }),
  eventType: varchar('event_type', { length: 255 }).notNull(),
  eventData: json('event_data'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  userIdIdx: index('audit_user_id_index').on(table.userId),
  eventTypeIdx: index('audit_event_type_index').on(table.eventType),
  createdAtIdx: index('audit_created_at_index').on(table.createdAt),
}));

// Business tables (existing)
export const customers = mysqlTable('customers', {
  id: int('id').autoincrement().primaryKey(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).unique(),
  phone: varchar('phone', { length: 20 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zipCode: varchar('zip_code', { length: 10 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
});

export const services = mysqlTable('services', {
  id: int('id').autoincrement().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  basePrice: varchar('base_price', { length: 10 }), // Using varchar for DECIMAL compatibility
  unit: varchar('unit', { length: 50 }),
  active: boolean('active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
});

export const estimates = mysqlTable('estimates', {
  id: int('id').autoincrement().primaryKey(),
  customerId: int('customer_id').notNull().references(() => customers.id, { onDelete: 'cascade' }),
  serviceId: int('service_id').notNull().references(() => services.id, { onDelete: 'restrict' }),
  status: varchar('status', { length: 20 }).default('pending'), // enum: pending, approved, rejected, completed
  estimatedCost: varchar('estimated_cost', { length: 10 }), // Using varchar for DECIMAL compatibility
  actualCost: varchar('actual_cost', { length: 10 }), // Using varchar for DECIMAL compatibility
  notes: text('notes'),
  scheduledDate: timestamp('scheduled_date', { mode: 'date' }),
  completedDate: timestamp('completed_date', { mode: 'date' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
});

export const contactForms = mysqlTable('contact_forms', {
  id: int('id').autoincrement().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  serviceType: varchar('service_type', { length: 100 }),
  message: text('message'),
  status: varchar('status', { length: 20 }).default('new'), // enum: new, contacted, converted, closed
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
});

// Type exports for TypeScript
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type Session = typeof sessions.$inferSelect;
export type Role = typeof roles.$inferSelect;
export type Permission = typeof permissions.$inferSelect;
export type Customer = typeof customers.$inferSelect;
export type Service = typeof services.$inferSelect;
export type Estimate = typeof estimates.$inferSelect;
export type ContactForm = typeof contactForms.$inferSelect;
