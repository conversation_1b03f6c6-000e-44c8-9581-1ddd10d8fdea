import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, Smartphone, Key, CheckCircle, AlertCircle, Copy, RefreshCw } from 'lucide-react';

interface TwoFactorAuthProps {
  userId: string;
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
}

interface QRCodeData {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export default function TwoFactorAuth({ userId, isEnabled, onToggle }: TwoFactorAuthProps) {
  const [showSetup, setShowSetup] = useState(false);
  const [qrData, setQrData] = useState<QRCodeData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [step, setStep] = useState<'setup' | 'verify' | 'complete'>('setup');
  const [error, setError] = useState('');

  // Generate 2FA setup data
  const generateSetupData = async () => {
    try {
      // In a real app, this would call the backend API
      const mockSecret = 'JBSWY3DPEHPK3PXP'; // Base32 encoded secret
      const appName = 'Roofers';
      const userEmail = '<EMAIL>'; // Would come from user data
      
      const qrCodeUrl = `otpauth://totp/${encodeURIComponent(appName)}:${encodeURIComponent(userEmail)}?secret=${mockSecret}&issuer=${encodeURIComponent(appName)}`;
      
      const backupCodes = [
        'A1B2C3D4',
        'E5F6G7H8',
        'I9J0K1L2',
        'M3N4O5P6',
        'Q7R8S9T0',
        'U1V2W3X4',
        'Y5Z6A7B8',
        'C9D0E1F2'
      ];

      setQrData({
        secret: mockSecret,
        qrCodeUrl,
        backupCodes
      });
    } catch (error) {
      setError('Failed to generate setup data');
    }
  };

  const handleSetupStart = async () => {
    setShowSetup(true);
    setStep('setup');
    setError('');
    await generateSetupData();
  };

  const handleVerifyCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      // In a real app, this would verify the code with the backend
      // For demo purposes, accept any 6-digit code
      if (/^\d{6}$/.test(verificationCode)) {
        setStep('complete');
        setTimeout(() => {
          onToggle(true);
          setShowSetup(false);
          setStep('setup');
          setVerificationCode('');
        }, 2000);
      } else {
        setError('Invalid verification code');
      }
    } catch (error) {
      setError('Verification failed. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleDisable2FA = async () => {
    if (window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
      try {
        // In a real app, this would call the backend API
        onToggle(false);
      } catch (error) {
        setError('Failed to disable 2FA');
      }
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  if (showSetup) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
          <div className="flex items-center space-x-2 mb-4">
            <Shield className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold">Setup Two-Factor Authentication</h3>
          </div>

          {step === 'setup' && qrData && (
            <div className="space-y-4">
              <div className="text-center">
                <div className="bg-gray-100 p-4 rounded-lg mb-4">
                  <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">QR Code would appear here</p>
                      <p className="text-xs text-gray-500 mt-1">Use your authenticator app to scan</p>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                </p>
              </div>

              <div className="border rounded-lg p-3">
                <p className="text-xs text-gray-600 mb-1">Manual entry key:</p>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 text-sm bg-gray-100 p-2 rounded font-mono">
                    {qrData.secret}
                  </code>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(qrData.secret)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Enter verification code from your app:
                </label>
                <input
                  type="text"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="000000"
                  className="w-full text-center text-lg font-mono border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={6}
                />
                {error && (
                  <p className="text-sm text-red-600 flex items-center space-x-1">
                    <AlertCircle className="h-4 w-4" />
                    <span>{error}</span>
                  </p>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSetup(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleVerifyCode}
                  disabled={isVerifying || verificationCode.length !== 6}
                >
                  {isVerifying ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Verify & Enable'
                  )}
                </Button>
              </div>
            </div>
          )}

          {step === 'complete' && qrData && (
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <h4 className="text-lg font-semibold text-green-800">2FA Enabled Successfully!</h4>
              <p className="text-sm text-gray-600">
                Your account is now protected with two-factor authentication.
              </p>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h5 className="font-medium text-yellow-800 mb-2">Backup Codes</h5>
                <p className="text-sm text-yellow-700 mb-3">
                  Save these backup codes in a safe place. You can use them to access your account if you lose your phone.
                </p>
                <div className="grid grid-cols-2 gap-2">
                  {qrData.backupCodes.map((code, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <code className="flex-1 text-sm bg-white p-2 rounded font-mono border">
                        {code}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(code)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="h-5 w-5" />
          <span>Two-Factor Authentication</span>
          {isEnabled && (
            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              Enabled
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account with two-factor authentication.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isEnabled ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800">2FA is active</p>
                <p className="text-xs text-green-600">Your account is protected with two-factor authentication</p>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">Authenticator App</p>
                <p className="text-xs text-gray-600">Connected and active</p>
              </div>
              <Button variant="outline" size="sm" onClick={handleDisable2FA}>
                Disable 2FA
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-yellow-800">2FA is not enabled</p>
                <p className="text-xs text-yellow-600">Your account could be more secure</p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Key className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Protect your account with an authenticator app</span>
              </div>
              <div className="flex items-center space-x-3">
                <Smartphone className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Works with Google Authenticator, Authy, and more</span>
              </div>
            </div>
            <Button onClick={handleSetupStart} className="w-full">
              <Shield className="h-4 w-4 mr-2" />
              Enable Two-Factor Authentication
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
