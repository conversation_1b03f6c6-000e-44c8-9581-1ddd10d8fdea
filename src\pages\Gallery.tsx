import React, { useState } from 'react';
import { P<PERSON>, Render, type Data } from '@measured/puck';
import { galleryConfig } from '../puck/schemas/gallery';
import { galleryImages } from '@/lib/image-paths';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface GalleryData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const galleryDefaultData: GalleryData = {
  root: {
    title: 'Project Gallery',
    description: 'Our portfolio and customer reviews'
  },
  content: [
    {
      type: 'GalleryHero',
      props: {
        title: 'Our Project Gallery',
        subtitle: 'Browse through our portfolio of successful roofing projects across Florida',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'ProjectsGrid',
      props: {
        title: 'Featured Projects',
        subtitle: 'Browse through our portfolio of successful roofing projects',
        projects: [
          {
            image: galleryImages[0]?.url || "/placeholder.svg",
            title: "Modern Shingle Roof Installation",
            description: "Complete roof replacement with architectural shingles in Miami by Roofers LLC",
            category: "Residential"
          },
          {
            image: galleryImages[1]?.url || "/placeholder.svg",
            title: "Commercial Flat Roof Project",
            description: "TPO roofing system installation for retail complex by Roofers LLC",
            category: "Commercial"
          },
          {
            image: galleryImages[2]?.url || "/placeholder.svg",
            title: "Emergency Storm Damage Repair",
            description: "24-hour response to hurricane damage in Fort Lauderdale by Roofers LLC",
            category: "Emergency"
          },
          {
            image: galleryImages[3]?.url || "/placeholder.svg",
            title: "Tile Roof Installation",
            description: "Spanish tile roof installation for luxury home by Roofers LLC",
            category: "Residential"
          },
          {
            image: galleryImages[4]?.url || "/placeholder.svg",
            title: "Industrial Roof Renovation",
            description: "Large-scale industrial roofing project by Roofers LLC",
            category: "Commercial"
          },
          {
            image: galleryImages[5]?.url || "/placeholder.svg",
            title: "Leak Detection & Repair",
            description: "Precision leak detection and repair services by Roofers LLC",
            category: "Repair"
          },
          {
            image: galleryImages[6]?.url || "/placeholder.svg",
            title: "Metal Roof Installation",
            description: "Standing seam metal roof for modern residence by Roofers LLC",
            category: "Residential"
          },
          {
            image: galleryImages[7]?.url || "/placeholder.svg",
            title: "Shopping Center Roof Replacement",
            description: "Complete roof system upgrade for retail center by Roofers LLC",
            category: "Commercial"
          },
          {
            image: "/placeholder.svg",
            title: "Solar Roof Integration",
            description: "Solar panel integration with roof modification by Roofers LLC",
            category: "Specialty"
          }
        ]
      }
    },
    {
      type: 'ReviewsGrid',
      props: {
        title: 'Customer Reviews',
        subtitle: 'What our satisfied customers say about Roofers LLC services',
        reviews: [
          {
            name: "John Smith",
            rating: 5,
            text: "Outstanding service from Roofers LLC! The team was professional, clean, and completed our new roof installation ahead of schedule. Highly recommend for any roofing needs.",
            image: "/placeholder.svg",
            project: "Residential Roofing"
          },
          {
            name: "Sarah Johnson",
            rating: 5,
            text: "As a business owner, I appreciate Roofers LLC's attention to detail and minimal disruption to our operations during the commercial roof replacement. Excellent work!",
            image: "/placeholder.svg",
            project: "Commercial Roofing"
          },
          {
            name: "Mike Wilson",
            rating: 5,
            text: "Responsive and reliable! Roofers LLC came out immediately after storm damage and had our roof repaired the same day. Great emergency service.",
            image: "/placeholder.svg",
            project: "Emergency Repair"
          },
          {
            name: "Lisa Rodriguez",
            rating: 5,
            text: "The quality of workmanship by Roofers LLC on our Spanish tile roof installation is outstanding. Their attention to detail and craftsmanship is evident in every aspect.",
            image: "/placeholder.svg",
            project: "Tile Roofing"
          }
        ]
      }
    },
    {
      type: 'GalleryCTA',
      props: {
        title: 'Ready to Start Your Roofing Project?',
        description: 'Contact Roofers LLC today for a free consultation and estimate',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

const Gallery: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<GalleryData>(galleryDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={galleryConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as GalleryData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={galleryConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default Gallery;
