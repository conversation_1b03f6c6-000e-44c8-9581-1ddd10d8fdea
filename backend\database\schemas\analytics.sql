-- Analytics Database Schema
-- Roofers LLC - Website Analytics and Visitor Tracking

-- Page Views Table
CREATE TABLE IF NOT EXISTS page_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id INT NULL,
    page_path VARCHAR(500) NOT NULL,
    page_title VARCHAR(255) NULL,
    referrer VARCHAR(500) NULL,
    user_agent TEXT NULL,
    ip_address VARCHAR(45) NULL,
    country VARCHAR(100) NULL,
    region VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    device_type ENUM('desktop', 'mobile', 'tablet') NULL,
    browser VARCHAR(100) NULL,
    operating_system VARCHAR(100) NULL,
    screen_resolution VARCHAR(20) NULL,
    view_duration INT NULL, -- Duration in seconds
    is_bounce BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_page_path (page_path),
    INDEX idx_created_at (created_at),
    INDEX idx_device_type (device_type),
    INDEX idx_is_bounce (is_bounce),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- User Sessions Table
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id INT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    country VARCHAR(100) NULL,
    region VARCHAR(100) NULL,
    city VARCHAR(100) NULL,
    device_type ENUM('desktop', 'mobile', 'tablet') NULL,
    browser VARCHAR(100) NULL,
    operating_system VARCHAR(100) NULL,
    entry_page VARCHAR(500) NULL,
    exit_page VARCHAR(500) NULL,
    total_page_views INT DEFAULT 0,
    session_duration INT NULL, -- Duration in seconds
    is_bounce BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_started_at (started_at),
    INDEX idx_device_type (device_type),
    INDEX idx_is_bounce (is_bounce),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Traffic Sources Table
CREATE TABLE IF NOT EXISTS traffic_sources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    source_type ENUM('organic', 'direct', 'social', 'referral', 'email', 'paid', 'other') NOT NULL,
    source_name VARCHAR(255) NULL, -- Google, Facebook, etc.
    medium VARCHAR(100) NULL, -- search, social, email, etc.
    campaign VARCHAR(255) NULL,
    keyword VARCHAR(255) NULL,
    referrer_url VARCHAR(500) NULL,
    utm_source VARCHAR(255) NULL,
    utm_medium VARCHAR(255) NULL,
    utm_campaign VARCHAR(255) NULL,
    utm_term VARCHAR(255) NULL,
    utm_content VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_source_type (source_type),
    INDEX idx_source_name (source_name),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (session_id) REFERENCES user_sessions(session_id) ON DELETE CASCADE
);

-- Events Table (for tracking specific actions)
CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id INT NULL,
    event_type ENUM('click', 'form_submit', 'download', 'video_play', 'scroll', 'custom') NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    event_category VARCHAR(100) NULL,
    event_label VARCHAR(255) NULL,
    event_value DECIMAL(10,2) NULL,
    page_path VARCHAR(500) NULL,
    element_id VARCHAR(255) NULL,
    element_class VARCHAR(255) NULL,
    element_text TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_event_name (event_name),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Goals and Conversions Table
CREATE TABLE IF NOT EXISTS conversions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id INT NULL,
    goal_type ENUM('contact_form', 'phone_call', 'estimate_request', 'newsletter_signup', 'custom') NOT NULL,
    goal_name VARCHAR(255) NOT NULL,
    goal_value DECIMAL(10,2) NULL,
    page_path VARCHAR(500) NULL,
    conversion_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_goal_type (goal_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Real-time Analytics Table
CREATE TABLE IF NOT EXISTS realtime_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2) NOT NULL,
    metric_date DATE NOT NULL,
    metric_hour TINYINT NULL, -- 0-23 for hourly data
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_metric (metric_name, metric_date, metric_hour),
    INDEX idx_metric_name (metric_name),
    INDEX idx_metric_date (metric_date),
    INDEX idx_created_at (created_at)
);

-- Create Views for Analytics Reporting

-- Daily Analytics Summary
CREATE VIEW daily_analytics AS
SELECT 
    DATE(created_at) as date,
    COUNT(DISTINCT session_id) as unique_visitors,
    COUNT(*) as page_views,
    AVG(view_duration) as avg_session_duration,
    SUM(CASE WHEN is_bounce = TRUE THEN 1 ELSE 0 END) / COUNT(DISTINCT session_id) * 100 as bounce_rate,
    COUNT(DISTINCT CASE WHEN device_type = 'mobile' THEN session_id END) as mobile_visitors,
    COUNT(DISTINCT CASE WHEN device_type = 'desktop' THEN session_id END) as desktop_visitors,
    COUNT(DISTINCT CASE WHEN device_type = 'tablet' THEN session_id END) as tablet_visitors
FROM page_views 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Top Pages View
CREATE VIEW top_pages AS
SELECT 
    page_path,
    page_title,
    COUNT(*) as views,
    COUNT(DISTINCT session_id) as unique_visitors,
    AVG(view_duration) as avg_time_on_page,
    SUM(CASE WHEN is_bounce = TRUE THEN 1 ELSE 0 END) / COUNT(*) * 100 as bounce_rate
FROM page_views 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY page_path, page_title
ORDER BY views DESC
LIMIT 20;

-- Traffic Sources Summary
CREATE VIEW traffic_sources_summary AS
SELECT 
    ts.source_type,
    ts.source_name,
    COUNT(DISTINCT ts.session_id) as sessions,
    COUNT(pv.id) as page_views,
    AVG(us.session_duration) as avg_session_duration,
    SUM(CASE WHEN us.is_bounce = TRUE THEN 1 ELSE 0 END) / COUNT(DISTINCT ts.session_id) * 100 as bounce_rate
FROM traffic_sources ts
LEFT JOIN user_sessions us ON ts.session_id = us.session_id
LEFT JOIN page_views pv ON ts.session_id = pv.session_id
WHERE ts.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY ts.source_type, ts.source_name
ORDER BY sessions DESC;

-- Device Analytics View
CREATE VIEW device_analytics AS
SELECT 
    device_type,
    browser,
    operating_system,
    COUNT(DISTINCT session_id) as sessions,
    COUNT(*) as page_views,
    AVG(view_duration) as avg_session_duration
FROM page_views 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY device_type, browser, operating_system
ORDER BY sessions DESC;

-- Insert Sample Analytics Data
INSERT INTO user_sessions (session_id, ip_address, device_type, browser, operating_system, entry_page, total_page_views, session_duration, is_bounce, started_at) VALUES
('sess_001', '*************', 'desktop', 'Chrome', 'Windows', '/', 5, 180, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_002', '*************', 'mobile', 'Safari', 'iOS', '/residential-roofing', 3, 120, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_003', '*************', 'desktop', 'Firefox', 'macOS', '/', 1, 30, TRUE, DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_004', '*************', 'tablet', 'Chrome', 'Android', '/commercial-roofing', 4, 200, FALSE, DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_005', '*************', 'mobile', 'Chrome', 'Android', '/contact', 2, 90, FALSE, DATE_SUB(NOW(), INTERVAL 3 DAYS));

INSERT INTO page_views (session_id, page_path, page_title, device_type, browser, view_duration, is_bounce, created_at) VALUES
('sess_001', '/', 'Home Page', 'desktop', 'Chrome', 45, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_001', '/residential-roofing', 'Residential Roofing', 'desktop', 'Chrome', 60, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_001', '/contact', 'Contact', 'desktop', 'Chrome', 75, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_002', '/residential-roofing', 'Residential Roofing', 'mobile', 'Safari', 40, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_002', '/gallery', 'Gallery', 'mobile', 'Safari', 50, FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_003', '/', 'Home Page', 'desktop', 'Firefox', 30, TRUE, DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_004', '/commercial-roofing', 'Commercial Roofing', 'tablet', 'Chrome', 80, FALSE, DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_004', '/about', 'About Us', 'tablet', 'Chrome', 45, FALSE, DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_005', '/contact', 'Contact', 'mobile', 'Chrome', 60, FALSE, DATE_SUB(NOW(), INTERVAL 3 DAYS)),
('sess_005', '/', 'Home Page', 'mobile', 'Chrome', 30, FALSE, DATE_SUB(NOW(), INTERVAL 3 DAYS));

INSERT INTO traffic_sources (session_id, source_type, source_name, medium, referrer_url, created_at) VALUES
('sess_001', 'organic', 'Google', 'search', 'https://google.com/search?q=roofing+services', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_002', 'direct', NULL, 'direct', NULL, DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_003', 'social', 'Facebook', 'social', 'https://facebook.com', DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_004', 'referral', 'Local Directory', 'referral', 'https://localdirectory.com', DATE_SUB(NOW(), INTERVAL 2 DAYS)),
('sess_005', 'organic', 'Google', 'search', 'https://google.com/search?q=roof+repair', DATE_SUB(NOW(), INTERVAL 3 DAYS));

INSERT INTO conversions (session_id, goal_type, goal_name, goal_value, page_path, created_at) VALUES
('sess_001', 'contact_form', 'Contact Form Submission', 100.00, '/contact', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_002', 'estimate_request', 'Free Estimate Request', 150.00, '/residential-roofing', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('sess_004', 'phone_call', 'Phone Call Click', 75.00, '/commercial-roofing', DATE_SUB(NOW(), INTERVAL 2 DAYS));

-- Insert Real-time Metrics
INSERT INTO realtime_analytics (metric_name, metric_value, metric_date, metric_hour) VALUES
('total_visitors', 1250, CURDATE(), HOUR(NOW())),
('page_views', 3420, CURDATE(), HOUR(NOW())),
('bounce_rate', 42.5, CURDATE(), HOUR(NOW())),
('avg_session_duration', 185.3, CURDATE(), HOUR(NOW())),
('mobile_percentage', 65.2, CURDATE(), HOUR(NOW())),
('conversion_rate', 3.8, CURDATE(), HOUR(NOW()));
