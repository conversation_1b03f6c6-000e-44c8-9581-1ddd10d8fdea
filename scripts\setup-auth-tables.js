import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables
dotenv.config();

async function setupAuthTables() {
  let connection;
  
  try {
    console.log('🔐 Setting up authentication tables...');
    
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('✅ Connected to database');

    // Create Auth.js required tables
    console.log('📋 Creating Auth.js tables...');

    // Users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(255) NOT NULL PRIMARY KEY,
        name <PERSON><PERSON><PERSON><PERSON>(255),
        email VARCHAR(255) UNIQUE,
        email_verified TIMESTAMP(6) NULL,
        image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX user_email_index (email)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created users table');

    // Accounts table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS accounts (
        id VARCHAR(255) NOT NULL PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        type VARCHAR(255) NOT NULL,
        provider VARCHAR(255) NOT NULL,
        provider_account_id VARCHAR(255) NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        expires_at INT,
        token_type VARCHAR(255),
        scope VARCHAR(255),
        id_token TEXT,
        session_state VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX account_user_id_index (user_id),
        INDEX account_provider_id_index (provider_account_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created accounts table');

    // Sessions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sessions (
        id VARCHAR(255) NOT NULL PRIMARY KEY,
        session_token VARCHAR(255) NOT NULL UNIQUE,
        user_id VARCHAR(255) NOT NULL,
        expires TIMESTAMP(6) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX session_user_id_index (user_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created sessions table');

    // Verification tokens table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS verification_tokens (
        identifier VARCHAR(255) NOT NULL,
        token VARCHAR(255) NOT NULL UNIQUE,
        expires TIMESTAMP(6) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (identifier, token)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created verification_tokens table');

    // RBAC tables
    console.log('📋 Creating RBAC tables...');

    // Roles table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS roles (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX role_name_index (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created roles table');

    // Permissions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS permissions (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX permission_name_index (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created permissions table');

    // Role permissions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS role_permissions (
        role_id VARCHAR(36) NOT NULL,
        permission_id VARCHAR(36) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created role_permissions table');

    // User roles table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_roles (
        user_id VARCHAR(255) NOT NULL,
        role_id VARCHAR(36) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_id, role_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        INDEX user_roles_user_id_index (user_id),
        INDEX user_roles_role_id_index (role_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created user_roles table');

    // Insert default roles
    console.log('📝 Inserting default roles...');
    const adminRoleId = uuidv4();
    const editorRoleId = uuidv4();
    const viewerRoleId = uuidv4();

    await connection.execute(`
      INSERT IGNORE INTO roles (id, name, description) VALUES
      (?, 'admin', 'Full system access - can manage users and edit all pages'),
      (?, 'editor', 'Content management access - can edit pages but not manage users'),
      (?, 'viewer', 'Read-only access - can view admin dashboard but cannot edit')
    `, [adminRoleId, editorRoleId, viewerRoleId]);
    console.log('✅ Inserted default roles');

    // Insert default permissions
    console.log('📝 Inserting default permissions...');
    const permissions = [
      { id: uuidv4(), name: 'edit_pages', description: 'Can edit website pages using Puck CMS' },
      { id: uuidv4(), name: 'manage_users', description: 'Can manage user accounts and roles' },
      { id: uuidv4(), name: 'view_admin', description: 'Can access admin dashboard' },
      { id: uuidv4(), name: 'manage_content', description: 'Can manage website content' },
    ];

    for (const permission of permissions) {
      await connection.execute(`
        INSERT IGNORE INTO permissions (id, name, description) VALUES (?, ?, ?)
      `, [permission.id, permission.name, permission.description]);
    }
    console.log('✅ Inserted default permissions');

    // Create a test admin user
    console.log('👤 Creating test admin user...');
    const adminUserId = uuidv4();
    
    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, email_verified) VALUES 
      (?, 'Admin User', '<EMAIL>', NOW())
    `, [adminUserId]);

    // Assign admin role to test user
    await connection.execute(`
      INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (?, ?)
    `, [adminUserId, adminRoleId]);
    
    console.log('✅ Created test admin user (<EMAIL>)');

    // Check what was created
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('\n📊 Authentication setup complete!');
    console.log('✅ Auth.js tables created');
    console.log('✅ RBAC system configured');
    console.log('✅ Default roles and permissions inserted');
    console.log('✅ Test admin user created');
    
    console.log('\n🔑 Test Login Credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');

    console.log('\n🎉 Authentication system ready!');

  } catch (error) {
    console.error('\n❌ Authentication setup failed:');
    console.error('Error:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the setup
setupAuthTables()
  .then(() => {
    console.log('\n✨ Setup complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Setup failed:', error.message);
    process.exit(1);
  });

export { setupAuthTables };
