import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';

// Load environment variables
dotenv.config();

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🚀 Setting up database for Roofers LLC...');
    
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('✅ Connected to database');

    // Create basic tables for roofing business
    console.log('📋 Creating business tables...');

    // Customers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
        last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
        email VARCHAR(255) UNIQUE,
        phone VARCHAR(20),
        address TEXT,
        city VARCHAR(100),
        state VARCHAR(50),
        zip_code VARCHAR(10),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created customers table');

    // Services table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        base_price DECIMAL(10,2),
        unit VARCHAR(50),
        active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created services table');

    // Estimates table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS estimates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT NOT NULL,
        service_id INT NOT NULL,
        status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
        estimated_cost DECIMAL(10,2),
        actual_cost DECIMAL(10,2),
        notes TEXT,
        scheduled_date DATE,
        completed_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created estimates table');

    // Contact forms table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS contact_forms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        service_type VARCHAR(100),
        message TEXT,
        status ENUM('new', 'contacted', 'converted', 'closed') DEFAULT 'new',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ Created contact_forms table');

    // Insert sample services
    console.log('📝 Inserting sample services...');
    await connection.execute(`
      INSERT IGNORE INTO services (name, description, base_price, unit) VALUES
      ('Residential Roofing', 'Complete residential roof installation and repair', 5000.00, 'project'),
      ('Commercial Roofing', 'Commercial building roof installation and maintenance', 15000.00, 'project'),
      ('Emergency Repair', 'Emergency roof repair services', 500.00, 'hour'),
      ('Metal Roofing', 'Metal roof installation and repair', 8000.00, 'project'),
      ('Shingle Roofing', 'Asphalt shingle installation and replacement', 4000.00, 'project'),
      ('TPO Roofing', 'TPO membrane roofing for commercial buildings', 12000.00, 'project'),
      ('Roof Inspection', 'Comprehensive roof inspection and assessment', 200.00, 'inspection')
    `);
    console.log('✅ Inserted sample services');

    // Check what was created
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n📊 Database setup complete!');
    console.log(`Created ${tables.length} tables:`);
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${index + 1}. ${tableName}`);
    });

    // Check services count
    const [serviceCount] = await connection.execute('SELECT COUNT(*) as count FROM services');
    console.log(`\n📋 Inserted ${serviceCount[0].count} services`);

    console.log('\n🎉 Database setup completed successfully!');
    console.log('✅ Ready for roofing business operations');

  } catch (error) {
    console.error('\n❌ Database setup failed:');
    console.error('Error:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the setup
setupDatabase()
  .then(() => {
    console.log('\n✨ Setup complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Setup failed:', error.message);
    process.exit(1);
  });

export { setupDatabase };
