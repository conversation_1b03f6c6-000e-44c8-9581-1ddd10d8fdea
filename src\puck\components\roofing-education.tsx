import React from 'react';
import { ComponentConfig } from '@measured/puck';
import { Shield, Info, AlertTriangle, Wrench, SunMoon, Cloud, Wind, Zap } from 'lucide-react';

// Education Hero Component
export const EducationHero: ComponentConfig = {
  fields: {
    title: { type: 'text' },
    description: { type: 'textarea' }
  },
  defaultProps: {
    title: 'Understanding Your Roofing Project',
    description: 'Thank you for submitting your roofing request. While we prepare your quote, here\'s some valuable information about roofing projects.'
  },
  render: ({ title, description }) => (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold text-center mb-8">{title}</h1>
      <div className="max-w-3xl mx-auto mb-12 text-center">
        <p className="text-xl text-gray-600">{description}</p>
      </div>
    </div>
  )
};

// Education Sections Component
export const EducationSections: ComponentConfig = {
  fields: {
    sections: {
      type: 'array',
      arrayFields: {
        icon: { type: 'text' },
        title: { type: 'text' },
        items: {
          type: 'array',
          arrayFields: {
            text: { type: 'text' }
          }
        }
      }
    }
  },
  defaultProps: {
    sections: [
      {
        icon: 'AlertTriangle',
        title: 'Signs You Need a New Roof',
        items: [
          { text: 'Age of roof is over 20 years' },
          { text: 'Curling or missing shingles' },
          { text: 'Daylight showing through roof boards' },
          { text: 'Sagging roof deck' }
        ]
      },
      {
        icon: 'Wrench',
        title: 'Popular Roofing Materials',
        items: [
          { text: 'Asphalt Shingles: Most common, cost-effective, 20-30 year lifespan' },
          { text: 'Metal Roofing: Durable, energy-efficient, 50+ year lifespan' },
          { text: 'TPO: Popular for flat roofs, excellent UV resistance' }
        ]
      }
    ]
  },
  render: ({ sections }) => {
    const getIcon = (iconName: string) => {
      switch (iconName) {
        case 'AlertTriangle': return <AlertTriangle className="w-8 h-8 text-accent mr-4" />;
        case 'Wrench': return <Wrench className="w-8 h-8 text-accent mr-4" />;
        default: return <Info className="w-8 h-8 text-accent mr-4" />;
      }
    };

    return (
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {sections.map((section: any, index: number) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center mb-6">
                {getIcon(section.icon)}
                <h2 className="text-2xl font-semibold">{section.title}</h2>
              </div>
              <ul className="space-y-4 text-gray-600">
                {section.items.map((item: any, itemIndex: number) => (
                  <li key={itemIndex} className="flex items-start">
                    <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
                    <span>{item.text}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    );
  }
};

// Weather Considerations Component
export const WeatherConsiderations: ComponentConfig = {
  fields: {
    title: { type: 'text' },
    considerations: {
      type: 'array',
      arrayFields: {
        icon: { type: 'text' },
        title: { type: 'text' },
        description: { type: 'text' }
      }
    }
  },
  defaultProps: {
    title: 'Florida Weather Considerations',
    considerations: [
      {
        icon: 'SunMoon',
        title: 'UV Protection',
        description: 'Protection against intense Florida sun'
      },
      {
        icon: 'Cloud',
        title: 'Rain Resistance',
        description: 'Superior water-shedding capabilities'
      },
      {
        icon: 'Wind',
        title: 'Hurricane Ready',
        description: 'Built to withstand high winds'
      },
      {
        icon: 'Zap',
        title: 'Lightning Protection',
        description: 'Optional lightning protection systems'
      }
    ]
  },
  render: ({ title, considerations }) => {
    const getIcon = (iconName: string) => {
      switch (iconName) {
        case 'SunMoon': return <SunMoon className="w-12 h-12 text-accent mx-auto mb-4" />;
        case 'Cloud': return <Cloud className="w-12 h-12 text-accent mx-auto mb-4" />;
        case 'Wind': return <Wind className="w-12 h-12 text-accent mx-auto mb-4" />;
        case 'Zap': return <Zap className="w-12 h-12 text-accent mx-auto mb-4" />;
        default: return <Info className="w-12 h-12 text-accent mx-auto mb-4" />;
      }
    };

    return (
      <div className="container mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <h2 className="text-2xl font-semibold mb-8 text-center">{title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {considerations.map((item: any, index: number) => (
              <div key={index} className="text-center">
                {getIcon(item.icon)}
                <h3 className="font-semibold mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }
};

// Process Steps Component
export const ProcessSteps: ComponentConfig = {
  fields: {
    title: { type: 'text' },
    steps: {
      type: 'array',
      arrayFields: {
        title: { type: 'text' },
        description: { type: 'text' }
      }
    }
  },
  defaultProps: {
    title: 'What to Expect Next',
    steps: [
      {
        title: '1. Initial Contact',
        description: 'Our team will contact you within 24 hours to discuss your roofing needs.'
      },
      {
        title: '2. Professional Inspection',
        description: 'We\'ll schedule a thorough inspection of your roof to assess its condition.'
      },
      {
        title: '3. Detailed Quote',
        description: 'You\'ll receive a comprehensive quote outlining all costs and recommendations.'
      }
    ]
  },
  render: ({ title, steps }) => (
    <div className="container mx-auto px-4">
      <div className="bg-gradient-to-r from-primary to-primary-dark text-white rounded-xl shadow-lg p-8">
        <div className="flex items-center mb-6">
          <Info className="w-8 h-8 text-accent mr-4" />
          <h2 className="text-2xl font-semibold">{title}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step: any, index: number) => (
            <div key={index}>
              <h3 className="font-semibold mb-2">{step.title}</h3>
              <p className="text-white/90">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
};

// Contact Banner Component
export const ContactBanner: ComponentConfig = {
  fields: {
    message: { type: 'text' },
    buttonText: { type: 'text' },
    phoneNumber: { type: 'text' }
  },
  defaultProps: {
    message: 'Have questions while you wait? Our team is here to help!',
    buttonText: 'Call (*************',
    phoneNumber: '+13053761808'
  },
  render: ({ message, buttonText, phoneNumber }) => (
    <div className="container mx-auto px-4">
      <div className="mt-12 text-center">
        <p className="text-xl mb-4">{message}</p>
        <a 
          href={`tel:${phoneNumber}`}
          className="inline-flex items-center justify-center px-8 py-3 text-lg font-medium text-white bg-accent rounded-full hover:bg-accent-dark transition-colors duration-300"
        >
          {buttonText}
        </a>
      </div>
    </div>
  )
};
