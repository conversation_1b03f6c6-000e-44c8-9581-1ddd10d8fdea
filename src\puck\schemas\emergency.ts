import React from 'react';
import { type Config } from '@measured/puck';
import { Link } from 'react-router-dom';
import { sharedFields, defaultVerticalPadding } from './shared';

interface HeroProps {
  title?: string;
  subtitle?: string;
  emergencyButtonText?: string;
  emergencyButtonLink?: string;
  phoneNumber?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface EmergencyServicesProps {
  title?: string;
  services?: Array<{
    title: string;
    description: string;
    icon: string;
  }>;
  verticalPadding?: string;
}

interface WhyChooseItemProps {
  name: string;
  description: string;
  points: Array<{ text: string }>;
  image: string;
}

interface WhyChooseProps {
  title?: string;
  items?: Array<WhyChooseItemProps>;
  verticalPadding?: string;
}

interface CTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  verticalPadding?: string;
}

export const emergencyConfig: Config = {
  components: {
    EmergencyHero: {
      render: ({
        title = '24/7 Emergency Roof Repair',
        subtitle = 'Immediate response to protect your property from roof emergencies',
        emergencyButtonText = 'Request Emergency Service',
        emergencyButtonLink = '/request-emergency-repair',
        phoneNumber = '(*************',
        backgroundImage = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-red-600 text-white bg-cover bg-center ${verticalPadding}`,
          style: {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("${backgroundImage}")`
          }
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('div', {
              key: 'content',
              className: 'max-w-2xl'
            }, [
              React.createElement('h1', {
                key: 'title',
                className: 'text-4xl md:text-5xl font-bold mb-6'
              }, title),
              React.createElement('p', {
                key: 'subtitle',
                className: 'text-xl mb-8'
              }, subtitle),
              React.createElement('div', {
                key: 'buttons',
                className: 'space-x-4'
              }, [
                React.createElement(Link, {
                  key: 'emergency',
                  to: emergencyButtonLink,
                  className: 'bg-white text-red-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors'
                }, emergencyButtonText),
                React.createElement('a', {
                  key: 'phone',
                  href: `tel:${phoneNumber.replace(/\D/g, '')}`,
                  className: 'inline-block bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors'
                }, `Call Now: ${phoneNumber}`)
              ])
            ])
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'text' },
        emergencyButtonText: { type: 'text' },
        emergencyButtonLink: { type: 'text' },
        phoneNumber: { type: 'text' },
        backgroundImage: { type: 'text' }
      }
    },

    EmergencyServices: {
      render: ({
        title = 'Emergency Roofing Services',
        services = [],
        verticalPadding = defaultVerticalPadding
      }: EmergencyServicesProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold text-center mb-12'
            }, title),
            React.createElement('div', {
              key: 'grid',
              className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'
            }, services.map((service, index) => 
              React.createElement('div', {
                key: index,
                className: 'bg-white rounded-lg shadow-lg p-6 text-center'
              }, [
                React.createElement('div', {
                  key: 'icon',
                  className: 'text-4xl mb-4'
                }, service.icon),
                React.createElement('h3', {
                  key: 'title',
                  className: 'text-xl font-bold mb-2'
                }, service.title),
                React.createElement('p', {
                  key: 'description',
                  className: 'text-gray-600'
                }, service.description)
              ])
            ))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        services: {
          type: 'array',
          arrayFields: {
            title: { type: 'text' },
            description: { type: 'textarea' },
            icon: { type: 'text' }
          }
        }
      }
    },

    WhyChooseUs: {
      render: ({
        title = 'Why Choose Our Emergency Service',
        items = [],
        verticalPadding = defaultVerticalPadding
      }: WhyChooseProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold text-center mb-12'
            }, title),
            React.createElement('div', {
              key: 'grid',
              className: 'grid grid-cols-1 md:grid-cols-3 gap-8'
            }, items.map((item, index) => 
              React.createElement('div', {
                key: index,
                className: 'bg-white rounded-lg shadow-lg overflow-hidden'
              }, [
                React.createElement('img', {
                  key: 'image',
                  src: item.image,
                  alt: `${item.name} by Roofers LLC`,
                  className: 'w-full h-48 object-cover'
                }),
                React.createElement('div', {
                  key: 'content',
                  className: 'p-6'
                }, [
                  React.createElement('h3', {
                    key: 'name',
                    className: 'text-xl font-bold mb-2'
                  }, item.name),
                  React.createElement('p', {
                    key: 'description',
                    className: 'text-gray-600 mb-4'
                  }, item.description),
                  React.createElement('ul', {
                    key: 'points',
                    className: 'space-y-2'
                  }, item.points.map((point, i) => 
                    React.createElement('li', {
                      key: i,
                      className: 'flex items-center text-gray-600'
                    }, [
                      React.createElement('svg', {
                        key: 'icon',
                        className: 'w-4 h-4 mr-2 text-primary',
                        viewBox: '0 0 20 20',
                        fill: 'currentColor'
                      }, [
                        React.createElement('path', {
                          key: 'path',
                          fillRule: 'evenodd',
                          d: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z',
                          clipRule: 'evenodd'
                        })
                      ]),
                      point.text
                    ])
                  ))
                ])
              ])
            ))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        items: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            description: { type: 'textarea' },
            points: {
              type: 'array',
              arrayFields: {
                text: { type: 'text' }
              }
            },
            image: { type: 'text' }
          }
        }
      }
    },

    EmergencyCTA: {
      render: ({
        title = 'Need a Long-term Solution?',
        description = 'Get a comprehensive estimate for permanent roofing solutions.',
        buttonText = 'Get Free Estimate',
        buttonLink = '/request-estimate',
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `bg-primary ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4 text-center text-white'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-3xl font-bold mb-6'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-xl mb-8'
            }, description),
            React.createElement(Link, {
              key: 'button',
              to: buttonLink,
              className: 'bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-block'
            }, buttonText)
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' }
      }
    }
  }
};