# Roofers LLC Project - User Guidelines for Augment Chat

## Table of Contents

1. [Project Overview](#project-overview)
2. [Environment Configuration](#environment-configuration)
3. [Development Rules](#development-rules)
4. [Code Organization](#code-organization)
5. [Security Guidelines](#security-guidelines)
6. [Testing and Quality Assurance](#testing-and-quality-assurance)
7. [Deployment Guidelines](#deployment-guidelines)
8. [Troubleshooting](#troubleshooting)

## Project Overview

The Roofers LLC project is a comprehensive website for a professional roofing company built with React, TypeScript, and Vite. The application serves as both an informational platform and lead generation tool, designed for future integration with a PHP-based CRM system.

### Key Technologies
- **Frontend**: React 18.3.1, TypeScript 5.5.3, Vite 5.4.1
- **UI Framework**: Tailwind CSS, shadcn/ui, Radix UI
- **Form Handling**: React Hook Form, Zod validation
- **Routing**: React Router DOM
- **Build Tool**: Vite with SWC
- **Package Manager**: npm

### Project Structure
```
/
├── docs/                  # Documentation
├── memory-bank/           # Project context and planning
├── src/                   # Source code
│   ├── components/        # React components
│   ├── config/           # Environment configuration
│   ├── lib/              # Utilities and API clients
│   ├── pages/            # Page components
│   └── hooks/            # Custom React hooks
├── public/               # Static assets
├── backend/              # Future CRM integration
└── scripts/              # Development scripts
```

## Environment Configuration

### Core Principles
1. **Centralized Configuration**: All environment settings managed through `src/config/environment.ts`
2. **Type Safety**: Strongly typed configuration with runtime validation
3. **No Hardcoded URLs**: All URL references must use environment configuration
4. **Cross-Platform Support**: Works with both Vite and Node.js environments

### Required Environment Variables
```bash
# Server Configuration
PORT=8080                           # Development server port
NODE_ENV=development               # Environment type

# Application URLs
APP_URL=http://localhost:8080      # Main application URL
API_BASE_URL=http://localhost:3001 # API server URL
AUTH_CALLBACK_URL=http://localhost:8080/auth/callback

# Security
CORS_ORIGIN=http://localhost:8080  # Allowed CORS origins
API_KEY=your-api-key-here          # API key for protected endpoints
AUTH_SECRET=your-auth-secret-here  # Authentication secret
```

### Environment Setup
```bash
# 1. Copy environment template
cp .env.example .env

# 2. Edit environment variables
nano .env

# 3. Start development server
npm run dev
```

## Development Rules

### Mandatory Practices
1. **Use Environment Configuration**: Always import and use `config` from `@/config/environment`
2. **No Hardcoded URLs**: Never hardcode localhost, ports, or domain names
3. **Type Safety**: Use TypeScript strictly, no `any` types without justification
4. **Component Patterns**: Follow established component structure and naming
5. **API Client Usage**: Use the centralized API client for all HTTP requests

### Code Quality Standards
1. **ESLint Compliance**: Code must pass all ESLint checks
2. **TypeScript Validation**: No TypeScript errors allowed
3. **Responsive Design**: All components must work on mobile and desktop
4. **Accessibility**: Follow WCAG guidelines for accessibility
5. **Performance**: Optimize for fast loading and smooth interactions

### File Organization
- **Components**: Feature-based organization in `src/components/`
- **Pages**: Route components in `src/pages/`
- **Utilities**: Helper functions in `src/lib/`
- **Types**: TypeScript interfaces and types co-located with usage
- **Styles**: Tailwind classes preferred, CSS modules when necessary

## Code Organization

### Component Structure
```typescript
// Component file structure
import { useState, useEffect } from 'react';
import { config } from '@/config/environment';
import { Button } from '@/components/ui/button';

interface ComponentProps {
  // Props interface
}

export function Component({ prop }: ComponentProps) {
  // Component implementation
}
```

### API Integration Pattern
```typescript
import { apiClient } from '@/lib/api-client';

// Use the centralized API client
const data = await apiClient.get('/endpoint');
const result = await apiClient.post('/endpoint', payload);
```

### Environment Usage Pattern
```typescript
import { config } from '@/config/environment';

// Access environment configuration
const redirectUrl = `${config.appUrl}/dashboard`;
const apiEndpoint = `${config.apiBaseUrl}/api/data`;
```

## Security Guidelines

### Environment Security
1. **Never commit `.env` files** with sensitive data
2. **Use different secrets** for each environment
3. **Validate all environment variables** on startup
4. **Restrict CORS origins** to known domains
5. **Use HTTPS in production**

### Code Security
1. **Sanitize user inputs** before processing
2. **Validate form data** with Zod schemas
3. **Handle errors gracefully** without exposing sensitive information
4. **Use secure authentication patterns**
5. **Implement proper CSRF protection**

## Testing and Quality Assurance

### Pre-commit Checks
```bash
# Type checking
npm run typecheck

# Linting
npm run lint

# Build validation
npm run build
```

### Testing Strategy
1. **Component Testing**: Test individual components in isolation
2. **Integration Testing**: Test component interactions
3. **Form Validation**: Test all form scenarios
4. **Responsive Testing**: Test on multiple screen sizes
5. **Performance Testing**: Monitor bundle size and load times

### Quality Metrics
- TypeScript strict mode compliance
- ESLint rule adherence
- Accessibility score > 90
- Performance score > 90
- Mobile responsiveness

## Deployment Guidelines

### Build Process
```bash
# Development build
npm run build:dev

# Production build
npm run build:prod

# Preview build
npm run preview
```

### Environment Setup
1. **Development**: Use `.env` with localhost URLs
2. **Production**: Use `.env.production` with production URLs
3. **Staging**: Use environment-specific configuration

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Build process successful
- [ ] All tests passing
- [ ] Performance optimized
- [ ] Security headers configured
- [ ] HTTPS enabled (production)
- [ ] CORS properly configured

## Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check port usage
netstat -ano | findstr :8080  # Windows
lsof -i :8080                 # macOS/Linux

# Kill process
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # macOS/Linux
```

#### Environment Variable Issues
- Verify `.env` file exists and is properly formatted
- Check that all required variables are defined
- Ensure URLs include protocol (http:// or https://)
- Validate environment configuration on startup

#### Build Issues
- Clear node_modules and reinstall dependencies
- Check TypeScript errors with `npm run typecheck`
- Verify ESLint compliance with `npm run lint`
- Check for missing environment variables

### Getting Help
1. Check existing documentation in `docs/` and `memory-bank/`
2. Review error messages carefully
3. Validate environment configuration
4. Check console for detailed error information
5. Ensure all dependencies are properly installed

### Development Workflow
```bash
# Daily development workflow
git pull origin main
npm install                    # Update dependencies
cp .env.example .env          # If .env doesn't exist
npm run dev                   # Start development server
npm run typecheck             # Validate TypeScript
npm run lint                  # Check code quality
npm run build                 # Test build process
```

This guide ensures consistent, secure, and maintainable development practices for the Roofers LLC project.
