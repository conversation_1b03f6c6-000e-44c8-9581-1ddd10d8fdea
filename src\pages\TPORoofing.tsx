import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { tpoConfig } from '../puck/schemas/tpo';
import { useEditButton } from '@/hooks/useEditButton';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface TPOData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const tpoDefaultData: TPOData = {
  root: {
    title: 'TPO Roofing Services',
    description: 'Professional TPO roofing solutions in Florida'
  },
  content: [
    {
      type: 'TPOHero',
      props: {
        title: 'TPO Roofing Solutions by Roofers LLC',
        subtitle: 'Energy-efficient commercial TPO roofing systems for superior performance',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'TPOServices',
      props: {
        title: 'Our TPO Roofing Services',
        services: [
          {
            title: "TPO Installation",
            description: "Professional installation of energy-efficient TPO roofing systems",
            image: "/placeholder.svg"
          },
          {
            title: "TPO Replacement",
            description: "Complete TPO roof replacement for commercial buildings",
            image: "/placeholder.svg"
          },
          {
            title: "TPO Maintenance",
            description: "Regular maintenance and repair services for TPO roofing",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'TPOBenefits',
      props: {
        title: 'TPO Roofing Benefits',
        benefits: [
          {
            name: "Energy Efficiency",
            description: "Reduce energy costs with reflective TPO roofing",
            features: [
              { text: "Reflects UV rays" },
              { text: "Reduces cooling costs" },
              { text: "Energy Star certified" },
              { text: "Heat-resistant surface" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Durability",
            description: "Long-lasting performance in harsh conditions",
            features: [
              { text: "Weather resistant" },
              { text: "Chemical resistant" },
              { text: "Puncture resistant" },
              { text: "Low maintenance" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Cost-Effective",
            description: "Excellent value for commercial properties",
            features: [
              { text: "Competitive pricing" },
              { text: "Long lifespan" },
              { text: "Energy savings" },
              { text: "Minimal maintenance" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'TPOCTA',
      props: {
        title: 'Ready to Get Started?',
        description: 'Contact Roofers LLC today for a consultation about our premium TPO roofing solutions.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

const TPORoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<TPOData>(tpoDefaultData);

  const { showEditButton } = useEditButton();

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={tpoConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as TPOData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={tpoConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default TPORoofing;