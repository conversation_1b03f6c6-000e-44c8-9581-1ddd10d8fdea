import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Home } from 'lucide-react';

interface AdminPageHeaderProps {
  title: string;
  description?: string;
  showBackButton?: boolean;
  children?: React.ReactNode;
}

export default function AdminPageHeader({ 
  title, 
  description, 
  showBackButton = true, 
  children 
}: AdminPageHeaderProps) {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-between mb-8">
      <div className="flex items-center space-x-4">
        {showBackButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/admin')}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Dashboard</span>
          </Button>
        )}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          {description && (
            <p className="text-gray-600 mt-2">{description}</p>
          )}
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/admin')}
          className="flex items-center space-x-2"
        >
          <Home className="h-4 w-4" />
          <span>Dashboard</span>
        </Button>
        {children}
      </div>
    </div>
  );
}
