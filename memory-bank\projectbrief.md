# Project Brief: Roofers Website

## Project Overview
A comprehensive website for a professional roofing company built using React and TypeScript. The website serves as both an informational platform and a lead generation tool, allowing potential customers to learn about roofing services and request estimates, consultations, or emergency repairs.

## Core Requirements

### Functional Requirements
1. Service Information Pages
   - Residential Roofing
   - Commercial Roofing
   - Emergency Roofing
   - Metal Roofing
   - Shingle Roofing
   - TPO Roofing
   - Roof Repair

2. Lead Generation Forms
   - Estimate Request
   - Consultation Request
   - Emergency Repair Request
   - Financing Application

3. Content Pages
   - Home Page
   - About Us
   - Areas We Serve
   - Gallery
   - Roofing Education
   - Contact

### Technical Requirements
1. Frontend
   - React + TypeScript for modern, type-safe development
   - Tailwind CSS for responsive styling
   - Reusable UI components
   - Form handling and validation
   - Content management through JSON files
   - Responsive design for all devices

2. Backend
   - PHP/CodeIgniter backend system
   - Form submission handling
   - File upload capabilities
   - API endpoints for form processing

## Project Goals
1. Provide clear, accessible information about roofing services
2. Generate quality leads through multiple conversion paths
3. Establish trust through educational content and professional presentation
4. Ensure fast, responsive user experience
5. Maintain high code quality and maintainability

## Success Metrics
1. Form submission conversion rates
2. Page load performance
3. Mobile responsiveness scores
4. Accessibility compliance
5. SEO optimization