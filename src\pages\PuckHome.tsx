import React, { useState } from 'react';
import { type Data, Puck, Render } from '@measured/puck';
import '@measured/puck/dist/index.css';
import { defaultVerticalPadding } from '../puck/schemas/shared';

// Import config
import { config } from '../puck/schema';

// Define proper types for our data
interface ComponentData {
  type: string;
  props: {
    backgroundImage?: string;
    tagline?: string;
    title?: string;
    subtitle?: string;
    description?: string;
    primaryButtonText?: string;
    primaryButtonLink?: string;
    secondaryButtonText?: string;
    secondaryButtonLink?: string;
    overlayColor?: string;
    verticalPadding?: string;
    features?: Array<{
      icon: 'shield' | 'star' | 'award' | 'check';
      text: string;
    }>;
    services?: Array<{
      image: string;
      title: string;
      description: string;
      link: string;
    }>;
    images?: Array<{
      url: string;
      alt: string;
    }>;
    reviews?: Array<{
      name: string;
      rating: number;
      review: string;
      date: string;
      location: string;
    }>;
    imagesPerView?: number;
    [key: string]: unknown;
  };
}

interface PuckData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: PuckData = {
  root: {
    title: 'Home Page',
    description: 'Main landing page for Roofers LLC'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        verticalPadding: defaultVerticalPadding,
        backgroundImage: 'https://images.unsplash.com/photo-1632778144849-611a9353439c',
        tagline: '#1 Trusted Roofing Company in Florida',
        title: 'Transform Your Roof',
        subtitle: "Florida's most trusted roofing experts with over 30+ years of experience. Free estimates and financing available.",
        features: [
          { icon: 'shield', text: 'Licensed & Insured' },
          { icon: 'star', text: '5-Star Service' },
          { icon: 'award', text: 'BBB A+ Rated' },
          { icon: 'check', text: '100% Satisfaction' }
        ]
      }
    },
    {
      type: 'ServicesGrid',
      props: {
        verticalPadding: defaultVerticalPadding,
        title: 'Our Services',
        description: 'Expert roofing solutions tailored to your needs, delivered with unmatched quality and professionalism.',
        services: [
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Residential Roofing',
            description: 'Expert roofing solutions for your home, from repairs to complete replacements.',
            link: '/residential-roofing'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778634169-8955cc0ba799',
            title: 'Commercial Roofing',
            description: 'Comprehensive roofing services for businesses and commercial properties.',
            link: '/commercial-roofing'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778634451-1c4a347774ce',
            title: 'Emergency Services',
            description: '24/7 emergency roofing repairs when you need them most.',
            link: '/emergency-roofing'
          }
        ]
      }
    },
    {
      type: 'GallerySection',
      props: {
        verticalPadding: defaultVerticalPadding,
        title: 'Project Gallery',
        description: 'Some of our completed roofing projects across Florida.',
        imagesPerView: 4,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1632778144849-611a9353439c?q=80&w=1920',
            alt: 'Beautiful modern house with new roof installation'
          },
          {
            url: 'https://images.unsplash.com/photo-1620641622503-21c824c15063?q=80&w=1920',
            alt: 'Professional roofers working on a new roof'
          },
          {
            url: 'https://images.unsplash.com/photo-1632778634162-e67c259bad89?q=80&w=1920',
            alt: 'Metal roofing with perfect installation'
          },
          {
            url: 'https://images.unsplash.com/photo-1600585553490-76fb20a32601?q=80&w=1920',
            alt: 'Modern house exterior with premium roofing'
          },
          {
            url: 'https://images.unsplash.com/photo-1638184984605-af1f05249a56?q=80&w=1920',
            alt: 'Residential roofing project completion'
          },
          {
            url: 'https://images.unsplash.com/photo-1632778145681-61e902bb3ba3?q=80&w=1920',
            alt: 'Commercial roofing installation'
          },
          {
            url: 'https://images.unsplash.com/photo-1635424710928-c0201da357b9?q=80&w=1920',
            alt: 'Solar roof panel installation'
          },
          {
            url: 'https://images.unsplash.com/photo-1600047508390-d39f5e147925?q=80&w=1920',
            alt: 'Luxury home with modern roofing'
          }
        ]
      }
    },
    {
      type: 'ReviewsSection',
      props: {
        verticalPadding: defaultVerticalPadding,
        title: 'Customer Reviews',
        description: 'See what our satisfied customers have to say about their experience with us.',
        reviews: [
          {
            name: "Michael Rodriguez",
            rating: 5,
            review: "Outstanding service from start to finish! The team was professional, punctual, and did an amazing job with our new roof. They cleaned up thoroughly after the job and the results exceeded our expectations.",
            date: "January 15, 2024",
            location: "Miami, FL"
          },
          {
            name: "Sarah Thompson",
            rating: 5,
            review: "After Hurricane Ian, we needed emergency roof repairs. They responded quickly and fixed everything perfectly. Their team explained everything clearly and made sure we were comfortable with the process.",
            date: "February 1, 2024",
            location: "Fort Lauderdale, FL"
          },
          {
            name: "David Martinez",
            rating: 5,
            review: "The best roofing company in Florida! Their attention to detail and quality of work is unmatched. They installed our metal roof and it looks fantastic. Great value for the investment.",
            date: "December 28, 2023",
            location: "Hollywood, FL"
          },
          {
            name: "Jennifer Clark",
            rating: 5,
            review: "I can't recommend them enough! They were extremely professional and completed our roof replacement ahead of schedule. Their team was courteous and kept us informed throughout the process.",
            date: "March 5, 2024",
            location: "Boca Raton, FL"
          },
          {
            name: "Robert Wilson",
            rating: 5,
            review: "Excellent workmanship and customer service. They handled our insurance claim expertly and made the entire process stress-free. The new roof looks amazing!",
            date: "January 30, 2024",
            location: "Coral Gables, FL"
          },
          {
            name: "Emily Torres",
            rating: 5,
            review: "Very impressed with their work! They were able to repair our complex tile roof with precision. Their attention to detail and professionalism is outstanding.",
            date: "February 15, 2024",
            location: "Kendall, FL"
          },
          {
            name: "William Anderson",
            rating: 5,
            review: "They did an incredible job on our commercial building's roof. The project was completed on time and within budget. Their team's expertise is evident in the quality of work.",
            date: "March 1, 2024",
            location: "Doral, FL"
          },
          {
            name: "Maria Sanchez",
            rating: 5,
            review: "After getting multiple quotes, we chose them for their expertise and competitive pricing. They delivered exactly what they promised - a beautiful, high-quality roof installation.",
            date: "February 20, 2024",
            location: "Aventura, FL"
          },
          {
            name: "James Peterson",
            rating: 5,
            review: "Outstanding experience from start to finish. Their team was professional, efficient, and left our property spotless. The new roof has transformed the look of our home.",
            date: "March 10, 2024",
            location: "Pinecrest, FL"
          }
        ]
      }
    },
    {
      type: 'CTASection',
      props: {
        verticalPadding: defaultVerticalPadding,
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        title: "Ready to Transform Your Roof?",
        description: "Get a free estimate for your roofing project. Our experts are ready to help you protect your most valuable investment.",
        primaryButtonText: "Get Free Estimate",
        primaryButtonLink: "/contact",
        secondaryButtonText: "Call (*************",
        secondaryButtonLink: "tel:+13053761808",
        overlayColor: "rgba(0, 0, 0, 0.65)"
      }
    }
  ]
};

const PuckHome: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<PuckData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={config}
          data={data}
          onPublish={async (newData) => {
            setData(newData as PuckData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={config} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default PuckHome;