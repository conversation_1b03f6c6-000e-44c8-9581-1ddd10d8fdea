import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    plugins: [react()],
    
    server: {
      port: parseInt(env.PORT || '8080'),
      host: true, // Listen on all addresses
      
      proxy: {
        '/api': {
          target: env.API_BASE_URL || env.VITE_API_BASE_URL, // Support both new and old variable names
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('Sending Request:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response:', proxyRes.statusCode, req.url);
            });
          },
        },
      },
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },

    build: {
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: [
              'react',
              'react-dom',
              'react-router-dom',
              '@radix-ui/react-icons',
              '@radix-ui/react-alert-dialog',
              '@radix-ui/react-navigation-menu',
            ],
          },
        },
      },
    },

    // Define global constants
    define: {
      __DEV__: mode === 'development',
      __PROD__: mode === 'production',
    },
  };
});
