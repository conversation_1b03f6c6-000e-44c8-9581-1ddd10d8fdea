import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { financingConfig } from '../puck/schemas/financing';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface FinancingData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: FinancingData = {
  root: {
    title: 'Financing Options',
    description: 'Flexible payment solutions for your roofing needs'
  },
  content: [
    {
      type: 'FinancingHero',
      props: {
        title: 'Affordable Roofing Solutions',
        description: 'Get your new roof now with flexible financing options. We work with trusted lenders to help make your roofing project affordable.',
        buttonText: 'Apply Now',
        buttonLink: '/contact'
      }
    },
    {
      type: 'ApplicationProcess',
      props: {
        title: 'Easy 3-Step Process',
        description: 'Getting started with financing is simple',
        steps: [
          {
            title: 'Schedule a Consultation',
            description: 'Meet with our experts to assess your roofing needs and discuss financing options'
          },
          {
            title: 'Review Options',
            description: 'We\'ll help you explore financing solutions that fit your budget'
          },
          {
            title: 'Start Your Project',
            description: 'Once approved, we can begin work on your new roof right away'
          }
        ]
      }
    }
  ]
};

const Financing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<FinancingData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={financingConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as FinancingData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Render config={financingConfig} data={data} />
      
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default Financing;
