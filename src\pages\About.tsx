import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { aboutConfig } from '../puck/schemas/about';
import { useEditButton } from '@/hooks/useEditButton';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface AboutData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const aboutDefaultData: AboutData = {
  root: {
    title: 'About Roofers LLC',
    description: 'Your trusted roofing partner in Florida'
  },
  content: [
    {
      type: 'AboutHero',
      props: {
        title: 'About Roofers LLC',
        subtitle: 'Your Trusted Roofing Partner in Florida',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    },
    {
      type: 'OurStory',
      props: {
        title: 'Our Story',
        content: [
          'With years of experience in the roofing industry, we\'ve built our reputation on quality workmanship and exceptional customer service. Our team of skilled professionals is dedicated to providing the best roofing solutions for homeowners and businesses across Florida.',
          'We understand that your roof is one of the most important investments you\'ll make in your property. That\'s why we use only the highest quality materials and employ the most skilled craftsmen in the industry.'
        ],
        image: '/placeholder.svg'
      }
    },
    {
      type: 'CompanyValues',
      props: {
        title: 'Our Values',
        subtitle: 'What sets us apart from the competition',
        values: [
          {
            title: 'Quality Craftsmanship',
            description: 'We take pride in our work and ensure every project meets our high standards of excellence.'
          },
          {
            title: 'Customer Service',
            description: 'Our clients are our priority, and we strive to exceed their expectations in every interaction.'
          },
          {
            title: 'Professional Excellence',
            description: 'We maintain the highest standards of professionalism in everything we do.'
          },
          {
            title: 'Integrity',
            description: 'We operate with complete transparency and honesty in all our dealings.'
          },
          {
            title: 'Reliability',
            description: 'You can count on us to deliver on our promises and meet deadlines.'
          },
          {
            title: 'Safety First',
            description: 'We prioritize safety in every project to protect our team and customers.'
          }
        ]
      }
    },
    {
      type: 'TeamSection',
      props: {
        title: 'Our Team',
        subtitle: 'Meet the experts behind our success',
        members: [
          {
            name: 'Jorge A Gutierrez',
            role: 'Co-founder',
            bio: 'Jorge brings extensive roofing expertise and leadership to guide our company\'s vision and growth.',
            image: '/placeholder.svg'
          },
          {
            name: 'Yoan Valiente',
            role: 'Co-founder',
            bio: 'Yoan leads our strategic initiatives and ensures operational excellence across all projects.',
            image: '/placeholder.svg'
          },
          {
            name: 'Gabriel Torres',
            role: 'Director of Operations',
            bio: 'Gabriel ensures smooth operations and maintains our high standards of quality in every project.',
            image: '/placeholder.svg'
          }
        ]
      }
    },
    {
      type: 'StatsSection',
      props: {
        title: 'Our Achievements',
        subtitle: 'Numbers that speak for themselves',
        stats: [
          {
            value: '1000+',
            label: 'Projects Completed',
            description: 'Successfully delivered roofing solutions'
          },
          {
            value: '15+',
            label: 'Years Experience',
            description: 'Serving Florida communities'
          },
          {
            value: '100%',
            label: 'Satisfaction',
            description: 'Customer satisfaction guaranteed'
          },
          {
            value: '24/7',
            label: 'Support',
            description: 'Emergency services available'
          }
        ]
      }
    },
    {
      type: 'AboutCTA',
      props: {
        title: 'Ready to Work with Us?',
        description: 'Contact Roofers LLC today to discuss your roofing needs',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

const About: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<AboutData>(aboutDefaultData);

  const { showEditButton } = useEditButton();

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={aboutConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as AboutData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={aboutConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default About;