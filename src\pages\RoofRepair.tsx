import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { repairConfig } from '../puck/schemas/repair';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface RepairData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const repairDefaultData: RepairData = {
  root: {
    title: 'Roof Repair Services',
    description: 'Professional roof repair solutions in Florida'
  },
  content: [
    {
      type: 'RepairHero',
      props: {
        title: 'Expert Roof Repair Services by Roofers LLC',
        subtitle: 'Fast, reliable roof repair solutions for all roofing types',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'RepairServices',
      props: {
        title: 'Our Repair Services',
        services: [
          {
            title: "Emergency Repairs",
            description: "24/7 emergency repair services for urgent roofing issues",
            image: "/placeholder.svg"
          },
          {
            title: "Leak Repairs",
            description: "Expert leak detection and repair services",
            image: "/placeholder.svg"
          },
          {
            title: "Storm Damage Repair",
            description: "Comprehensive storm damage assessment and repairs",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'RepairTypes',
      props: {
        title: 'Common Repair Types',
        repairTypes: [
          {
            name: "Shingle Repairs",
            description: "Expert repairs for damaged or missing shingles",
            features: [
              { text: "Replace missing shingles" },
              { text: "Fix loose shingles" },
              { text: "Seal damaged areas" },
              { text: "Prevent future issues" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Flat Roof Repairs",
            description: "Professional repairs for commercial flat roofs",
            features: [
              { text: "Fix membrane damage" },
              { text: "Address ponding water" },
              { text: "Repair flashing" },
              { text: "Seal seams" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Metal Roof Repairs",
            description: "Specialized repairs for metal roofing systems",
            features: [
              { text: "Fix loose panels" },
              { text: "Seal leaks" },
              { text: "Replace fasteners" },
              { text: "Repair seams" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'RepairCTA',
      props: {
        title: 'Need Roof Repairs?',
        description: 'Contact Roofers LLC today for fast, professional roof repair services.',
        buttonText: 'Request Service',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

const RoofRepair: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<RepairData>(repairDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={repairConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as RepairData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={repairConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default RoofRepair;