import React from 'react';
import { type Config } from '@measured/puck';
import { Link } from 'react-router-dom';
import { Star, Users, Trophy, Phone } from 'lucide-react';
import { sharedFields, defaultVerticalPadding } from './shared';

interface HeroProps {
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface StoryProps {
  title?: string;
  content?: string[];
  image?: string;
  verticalPadding?: string;
}

interface ValueProps {
  title: string;
  description: string;
  icon?: string;
}

interface ValuesProps {
  title?: string;
  subtitle?: string;
  values?: ValueProps[];
  verticalPadding?: string;
}

interface TeamMemberProps {
  name: string;
  role: string;
  bio: string;
  image: string;
}

interface TeamProps {
  title?: string;
  subtitle?: string;
  members?: TeamMemberProps[];
  verticalPadding?: string;
}

interface StatProps {
  value: string;
  label: string;
  description: string;
}

interface StatsProps {
  title?: string;
  subtitle?: string;
  stats?: StatProps[];
  verticalPadding?: string;
}

interface CTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  verticalPadding?: string;
}

export const aboutConfig: Config = {
  components: {
    AboutHero: {
      render: ({
        title = 'About Roofers LLC',
        subtitle = 'Your Trusted Roofing Partner in Florida',
        backgroundImage = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-primary text-white ${verticalPadding}`,
          children: [
            React.createElement('div', {
              key: 'background',
              className: 'absolute inset-0 bg-cover bg-center opacity-20',
              style: { backgroundImage: `url("${backgroundImage}")` }
            }),
            React.createElement('div', {
              key: 'content',
              className: 'container mx-auto px-4 relative',
              children: React.createElement('div', {
                className: 'max-w-3xl mx-auto text-center',
                children: [
                  React.createElement('h1', {
                    key: 'title',
                    className: 'text-5xl font-bold mb-6'
                  }, title),
                  React.createElement('p', {
                    key: 'subtitle',
                    className: 'text-xl'
                  }, subtitle)
                ]
              })
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        backgroundImage: { type: 'text' }
      }
    },

    OurStory: {
      render: ({
        title = 'Our Story',
        content = [
          'With years of experience in the roofing industry, we\'ve built our reputation on quality workmanship and exceptional customer service.',
          'We understand that your roof is one of the most important investments you\'ll make in your property. That\'s why we use only the highest quality materials and employ the most skilled craftsmen in the industry.'
        ],
        image = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: StoryProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4',
            children: React.createElement('div', {
              className: 'grid grid-cols-1 lg:grid-cols-2 gap-12 items-center',
              children: [
                React.createElement('div', {
                  key: 'content',
                  children: [
                    React.createElement('h2', {
                      key: 'title',
                      className: 'text-3xl font-bold mb-6'
                    }, title),
                    ...content.map((paragraph, index) => 
                      React.createElement('p', {
                        key: index,
                        className: 'text-gray-600 mb-4'
                      }, paragraph)
                    )
                  ]
                }),
                React.createElement('div', {
                  key: 'image',
                  className: 'relative h-96 rounded-lg overflow-hidden shadow-xl',
                  children: React.createElement('img', {
                    src: image,
                    alt: 'Our Story',
                    className: 'w-full h-full object-cover'
                  })
                })
              ]
            })
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        content: {
          type: 'array',
          arrayFields: {
            text: { type: 'textarea' }
          }
        },
        image: { type: 'text' }
      }
    },

    CompanyValues: {
      render: ({
        title = 'Our Values',
        subtitle = 'What sets us apart from the competition',
        values = [],
        verticalPadding = defaultVerticalPadding
      }: ValuesProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4',
            children: [
              React.createElement('div', {
                key: 'header',
                className: 'text-center mb-12',
                children: [
                  React.createElement('h2', {
                    key: 'title',
                    className: 'text-3xl font-bold mb-4'
                  }, title),
                  React.createElement('p', {
                    key: 'subtitle',
                    className: 'text-xl text-gray-600'
                  }, subtitle)
                ]
              }),
              React.createElement('div', {
                key: 'grid',
                className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8',
                children: values.map((value, index) => 
                  React.createElement('div', {
                    key: index,
                    className: 'bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow',
                    children: [
                      React.createElement('h3', {
                        key: 'title',
                        className: 'text-xl font-bold mb-4'
                      }, value.title),
                      React.createElement('p', {
                        key: 'description',
                        className: 'text-gray-600'
                      }, value.description)
                    ]
                  })
                )
              })
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        values: {
          type: 'array',
          arrayFields: {
            title: { type: 'text' },
            description: { type: 'textarea' }
          }
        }
      }
    },

    TeamSection: {
      render: ({
        title = 'Our Team',
        subtitle = 'Meet the experts behind our success',
        members = [],
        verticalPadding = defaultVerticalPadding
      }: TeamProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4',
            children: [
              React.createElement('div', {
                key: 'header',
                className: 'text-center mb-12',
                children: [
                  React.createElement('h2', {
                    key: 'title',
                    className: 'text-3xl font-bold mb-4'
                  }, title),
                  React.createElement('p', {
                    key: 'subtitle',
                    className: 'text-xl text-gray-600'
                  }, subtitle)
                ]
              }),
              React.createElement('div', {
                key: 'grid',
                className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8',
                children: members.map((member, index) => 
                  React.createElement('div', {
                    key: index,
                    className: 'bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow',
                    children: [
                      React.createElement('div', {
                        key: 'image',
                        className: 'h-64 relative',
                        children: React.createElement('img', {
                          src: member.image,
                          alt: member.name,
                          className: 'w-full h-full object-cover'
                        })
                      }),
                      React.createElement('div', {
                        key: 'content',
                        className: 'p-6',
                        children: [
                          React.createElement('h3', {
                            key: 'name',
                            className: 'text-xl font-bold mb-2'
                          }, member.name),
                          React.createElement('p', {
                            key: 'role',
                            className: 'text-primary mb-4'
                          }, member.role),
                          React.createElement('p', {
                            key: 'bio',
                            className: 'text-gray-600'
                          }, member.bio)
                        ]
                      })
                    ]
                  })
                )
              })
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        members: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            role: { type: 'text' },
            bio: { type: 'textarea' },
            image: { type: 'text' }
          }
        }
      }
    },

    StatsSection: {
      render: ({
        title = 'Our Achievements',
        subtitle = 'Numbers that speak for themselves',
        stats = [],
        verticalPadding = defaultVerticalPadding
      }: StatsProps) => {
        return React.createElement('section', {
          className: `bg-primary text-white ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4',
            children: [
              React.createElement('div', {
                key: 'header',
                className: 'text-center mb-12',
                children: [
                  React.createElement('h2', {
                    key: 'title',
                    className: 'text-3xl font-bold mb-4'
                  }, title),
                  React.createElement('p', {
                    key: 'subtitle',
                    className: 'text-xl opacity-90'
                  }, subtitle)
                ]
              }),
              React.createElement('div', {
                key: 'grid',
                className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8',
                children: stats.map((stat, index) => 
                  React.createElement('div', {
                    key: index,
                    className: 'text-center p-6 rounded-lg bg-white/10 backdrop-blur',
                    children: [
                      React.createElement('div', {
                        key: 'value',
                        className: 'text-4xl font-bold mb-2'
                      }, stat.value),
                      React.createElement('div', {
                        key: 'label',
                        className: 'text-lg font-semibold mb-2'
                      }, stat.label),
                      React.createElement('p', {
                        key: 'description',
                        className: 'text-sm opacity-90'
                      }, stat.description)
                    ]
                  })
                )
              })
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        stats: {
          type: 'array',
          arrayFields: {
            value: { type: 'text' },
            label: { type: 'text' },
            description: { type: 'textarea' }
          }
        }
      }
    },

    AboutCTA: {
      render: ({
        title = 'Ready to Work with Us?',
        description = 'Contact Roofers LLC today to discuss your roofing needs',
        buttonText = 'Get Free Estimate',
        buttonLink = '/request-estimate',
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4 text-center',
            children: [
              React.createElement('h2', {
                key: 'title',
                className: 'text-3xl font-bold mb-4'
              }, title),
              React.createElement('p', {
                key: 'description',
                className: 'text-xl text-gray-600 mb-8 max-w-2xl mx-auto'
              }, description),
              React.createElement('div', {
                key: 'buttons',
                className: 'flex flex-col sm:flex-row gap-4 justify-center',
                children: [
                  React.createElement(Link, {
                    key: 'cta',
                    to: buttonLink,
                    className: 'bg-accent text-white px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-block'
                  }, buttonText),
                  React.createElement('a', {
                    key: 'phone',
                    href: 'tel:+13053761808',
                    className: 'bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors inline-flex items-center justify-center'
                  }, [
                    React.createElement(Phone, {
                      key: 'icon',
                      className: 'w-5 h-5 mr-2'
                    }),
                    '(*************'
                  ])
                ]
              })
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' }
      }
    }
  }
};