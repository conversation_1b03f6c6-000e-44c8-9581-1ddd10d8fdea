# Database Management

This directory contains the database migrations and management tools for the Roofers application.

## Structure

```
database/
├── migrations/        # Migration SQL files
│   ├── 01_auth_tables.sql       # Auth.js required tables
│   └── 02_rbac_tables.sql      # Role-based access control tables
├── migrate.ts        # Migration runner script
└── README.md        # This file
```

## Migration System

The migration system tracks executed migrations in a `migrations` table and runs new migrations in order. Each migration runs in a transaction to ensure data consistency.

### Commands

```bash
# Run pending migrations
npm run db:migrate

# Rollback migrations (not implemented yet)
npm run db:migrate:rollback
```

### Environment Variables

Required environment variables for database connection:

```bash
DATABASE_HOST=localhost
DATABASE_USER=your_user
DATABASE_PASSWORD=your_password
DATABASE_NAME=roofers
DATABASE_PORT=3306
```

## Database Schema

### Auth Tables
- `users` - User accounts and profile information
- `accounts` - OAuth provider accounts
- `sessions` - User sessions
- `verification_tokens` - Email verification tokens

### RBAC Tables
- `roles` - Available roles (admin, editor, viewer)
- `permissions` - Available permissions
- `role_permissions` - Role to permission mappings
- `user_roles` - User to role assignments

### Audit Tables
- `auth_audit_logs` - Authentication and authorization events

## Default Data

The migrations automatically create:

### Roles
- `admin` - Full system access
- `editor` - Content management access
- `viewer` - Read-only access

### Permissions
- `manage_users` - User management
- `manage_roles` - Role management
- `manage_content` - Content management
- `view_content` - Content viewing
- `view_analytics` - Analytics viewing

## Usage

1. Set up environment variables
2. Run migrations: `npm run db:migrate`
3. Check migration status in the `migrations` table

## Development

### Adding New Migrations

1. Create a new SQL file in `migrations/` with a numeric prefix
2. Add `UP` migration statements
3. Test the migration locally
4. Commit the migration file

### Best Practices

1. Make migrations incremental and atomic
2. Include transactions for data consistency
3. Add appropriate indexes for performance
4. Document schema changes in comments
5. Test migrations on a copy of production data