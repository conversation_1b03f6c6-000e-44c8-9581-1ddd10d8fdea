import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { commercialConfig } from '../puck/schemas/commercial';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface CommercialData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

const defaultData: CommercialData = {
  root: {
    title: 'Commercial Roofing Services',
    description: 'Expert commercial roofing services in Florida'
  },
  content: [
    {
      type: 'CommercialHero',
      props: {
        title: 'Commercial Roofing Solutions by Roofers LLC',
        subtitle: 'Professional roofing services for businesses of all sizes across Florida',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'CommercialServices',
      props: {
        title: 'Our Commercial Services',
        services: [
          {
            title: "Flat Roof Installation",
            description: "Expert installation of commercial flat roofing systems by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Replacement",
            description: "Complete commercial roof replacement services by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Preventive Maintenance",
            description: "Regular maintenance programs by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Emergency Repairs",
            description: "24/7 emergency repair services by Roofers LLC",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'CommercialSystems',
      props: {
        title: 'Commercial Roofing Systems',
        systems: [
          {
            name: "Built-Up Roofing (BUR)",
            description: "Traditional and reliable multi-layer system",
            features: [
              { text: "Multiple protective layers" },
              { text: "Excellent waterproofing" },
              { text: "Long-lasting durability" },
              { text: "Superior protection" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Modified Bitumen",
            description: "Durable and weather-resistant solution",
            features: [
              { text: "High tensile strength" },
              { text: "Weather resistance" },
              { text: "Easy maintenance" },
              { text: "Excellent durability" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "TPO/PVC Systems",
            description: "Energy-efficient single-ply membranes",
            features: [
              { text: "Energy efficient" },
              { text: "UV resistant" },
              { text: "Heat reflective" },
              { text: "Cost effective" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'CommercialCTA',
      props: {
        title: 'Ready to Discuss Your Project?',
        description: 'Contact Roofers LLC today for a comprehensive consultation for your commercial roofing needs.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

const CommercialRoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<CommercialData>(defaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={commercialConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as CommercialData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={commercialConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-primary text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default CommercialRoofing;