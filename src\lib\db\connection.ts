import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import { config } from '@/config/environment';
import * as schema from './schema';

// Create connection pool
const pool = mysql.createPool({
  host: process.env.DATABASE_HOST || config.isDevelopment ? 'localhost' : '',
  user: process.env.DATABASE_USER || 'roofers01',
  password: process.env.DATABASE_PASSWORD || '',
  database: process.env.DATABASE_NAME || 'roofers01',
  port: parseInt(process.env.DATABASE_PORT || '3306'),
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// Create Drizzle instance
export const db = drizzle(pool, { schema, mode: 'default' });

// Export pool for direct access if needed
export { pool };

// Test connection function
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connection successful');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Close connection pool
export async function closeConnection() {
  try {
    await pool.end();
    console.log('🔌 Database connection pool closed');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
}
