import { FC } from 'react';
import { Star } from 'lucide-react';

interface ReviewCardProps {
  name: string;
  rating: number;
  review: string;
  date: string;
  location: string;
}

const ReviewCard: FC<ReviewCardProps> = ({ name, rating, review, date, location }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-lg">{name}</h3>
        <span className="text-sm text-gray-500">{date}</span>
      </div>
      
      {/* Rating Stars */}
      <div className="flex gap-1 mb-3">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            className={`w-5 h-5 ${
              index < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
      
      {/* Review Text */}
      <p className="text-gray-600 mb-4">{review}</p>
      
      {/* Location */}
      <div className="text-sm text-gray-500">
        {location}
      </div>
    </div>
  );
};

export default ReviewCard;