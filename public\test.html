<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roofers LLC - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 Roofers LLC - Server Test</h1>
        <p>This is a static HTML test page to verify the server is working.</p>
        
        <h2>Server Status Check:</h2>
        <ul>
            <li class="status">✅ HTML serving: Working</li>
            <li class="status">✅ Static files: Working</li>
            <li class="status">✅ Port 8080: Accessible</li>
        </ul>
        
        <h2>Next Steps:</h2>
        <ol>
            <li>Verify React application is loading</li>
            <li>Check JavaScript console for errors</li>
            <li>Test main application routes</li>
        </ol>
        
        <p><strong>If you can see this page, the Vite development server is running correctly.</strong></p>
        
        <script>
            console.log('Static HTML test page loaded successfully');
            console.log('Server is responding on port 8080');
        </script>
    </div>
</body>
</html>
