import { type Data } from '@measured/puck';

// Fallback data for pages that don't have dedicated data files yet
const fallbackPageData: Record<string, Data> = {
  '/': {
    root: { title: 'Home Page' },
    content: [
      {
        type: 'HeroSection',
        props: {
          title: 'Transform Your Roof, Protect Your Home',
          subtitle: "Florida's most trusted roofing experts",
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/contact': {
    root: { title: 'Contact Us' },
    content: [
      {
        type: 'HeroSection',
        props: {
          title: 'Contact Roofers LLC',
          subtitle: 'Get your free roofing estimate today',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/emergency-roofing': {
    root: { title: '24/7 Emergency Roof Repair' },
    content: [
      {
        type: 'EmergencyHero',
        props: {
          title: '24/7 Emergency Roof Repair',
          subtitle: 'Fast response when you need it most',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/roof-repair': {
    root: { title: 'Roof Repair Services' },
    content: [
      {
        type: 'RepairHero',
        props: {
          title: 'Expert Roof Repair Services by Roofers LLC',
          subtitle: 'Fast, reliable roof repair solutions for all roofing types',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/metal-roofing': {
    root: { title: 'Metal Roofing Services' },
    content: [
      {
        type: 'MetalHero',
        props: {
          title: 'Metal Roofing Solutions by Roofers LLC',
          subtitle: 'Durable, energy-efficient metal roofing systems',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/shingle-roofing': {
    root: { title: 'Shingle Roofing Services' },
    content: [
      {
        type: 'ShingleHero',
        props: {
          title: 'Shingle Roofing Solutions by Roofers LLC',
          subtitle: 'Quality shingle roofing installation and replacement services',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/tpo-roofing': {
    root: { title: 'TPO Roofing Services' },
    content: [
      {
        type: 'TPOHero',
        props: {
          title: 'TPO Roofing Services',
          subtitle: 'Professional TPO roofing solutions in Florida',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/gallery': {
    root: { title: 'Project Gallery' },
    content: [
      {
        type: 'GallerySection',
        props: {
          title: 'Our Work',
          subtitle: 'See the quality of our roofing projects'
        }
      }
    ]
  },
  '/areas-we-serve': {
    root: { title: 'Areas We Serve' },
    content: [
      {
        type: 'AreasHero',
        props: {
          title: 'Areas We Serve',
          subtitle: 'Professional roofing services across Florida',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/financing': {
    root: { title: 'Financing Options' },
    content: [
      {
        type: 'HeroSection',
        props: {
          title: 'Financing Options',
          subtitle: 'Make your roofing project affordable',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  },
  '/roofing-education': {
    root: { title: 'Roofing Education' },
    content: [
      {
        type: 'HeroSection',
        props: {
          title: 'Roofing Education Center',
          subtitle: 'Learn about roofing materials, maintenance, and best practices',
          backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
        }
      }
    ]
  }
};

/**
 * Get the initial data for a page, checking localStorage first, then actual data, then fallback
 */
export function getPageData(pagePath: string): Data {
  // First, try to load saved data from localStorage
  const savedData = localStorage.getItem(`puck-data-${pagePath}`);
  if (savedData) {
    try {
      return JSON.parse(savedData);
    } catch (error) {
      console.warn(`Failed to parse saved data for ${pagePath}:`, error);
    }
  }

  // Try to get actual page data from dedicated data files
  switch (pagePath) {
    case '/about': {
      try {
        // Import synchronously for now - we'll improve this later
        const { aboutDefaultData } = require('../data/aboutData');
        return aboutDefaultData;
      } catch (error) {
        console.warn('Failed to load about data:', error);
      }
      break;
    }
    case '/residential-roofing': {
      try {
        const { residentialDefaultData } = require('../data/residentialData');
        return residentialDefaultData;
      } catch (error) {
        console.warn('Failed to load residential data:', error);
      }
      break;
    }
    case '/commercial-roofing': {
      try {
        const { commercialDefaultData } = require('../data/commercialData');
        return commercialDefaultData;
      } catch (error) {
        console.warn('Failed to load commercial data:', error);
      }
      break;
    }
  }

  // Fall back to fallback data
  const fallbackData = fallbackPageData[pagePath];
  if (fallbackData) {
    return fallbackData;
  }

  // Ultimate fallback for unknown pages
  return {
    root: { title: 'Page' },
    content: []
  };
}

/**
 * Get the appropriate Puck config for a page
 */
export async function getPageConfig(pagePath: string) {
  // Import configs dynamically to avoid circular dependencies
  switch (pagePath) {
    case '/about':
      return import('../puck/schemas/about').then(m => m.aboutConfig);
    case '/':
      return import('../puck/schemas/home').then(m => m.homeConfig);
    case '/contact':
      return import('../puck/schemas/contact').then(m => m.contactConfig);
    case '/residential-roofing':
      return import('../puck/schemas/residential').then(m => m.residentialConfig);
    case '/commercial-roofing':
      return import('../puck/schemas/commercial').then(m => m.commercialConfig);
    case '/emergency-roofing':
      return import('../puck/schemas/emergency').then(m => m.emergencyConfig);
    case '/roof-repair':
      return import('../puck/schemas/repair').then(m => m.repairConfig);
    case '/metal-roofing':
      return import('../puck/schemas/metal').then(m => m.metalConfig);
    case '/shingle-roofing':
      return import('../puck/schemas/shingle').then(m => m.shingleConfig);
    case '/tpo-roofing':
      return import('../puck/schemas/tpo').then(m => m.tpoConfig);
    case '/gallery':
      return import('../puck/schemas/gallery').then(m => m.galleryConfig);
    case '/areas-we-serve':
      return import('../puck/schemas/areas').then(m => m.areasConfig);
    case '/financing':
      return import('../puck/schemas/financing').then(m => m.financingConfig);
    default:
      // For unknown pages, use the general config
      return import('../puck/schema').then(m => m.config);
  }
}
    description: 'Your trusted roofing partner in Florida'
  },
  content: [
    {
      type: 'OurStory',
      props: {
        title: 'Our Story',
        content: [
          'With years of experience in the roofing industry, we\'ve built our reputation on quality workmanship and exceptional customer service. Our team of skilled professionals is dedicated to providing the best roofing solutions for homeowners and businesses across Florida.',
          'We understand that your roof is one of the most important investments you\'ll make in your property. That\'s why we use only the highest quality materials and employ the most skilled craftsmen in the industry.'
        ],
        image: '/placeholder.svg'
      }
    },
    {
      type: 'CompanyValues',
      props: {
        title: 'Our Values',
        subtitle: 'What sets us apart from the competition',
        values: [
          {
            title: 'Quality Craftsmanship',
            description: 'We take pride in our work and ensure every project meets our high standards of excellence.'
          },
          {
            title: 'Customer Service',
            description: 'Our clients are our priority, and we strive to exceed their expectations in every interaction.'
          },
          {
            title: 'Professional Excellence',
            description: 'We maintain the highest standards of professionalism in everything we do.'
          },
          {
            title: 'Integrity',
            description: 'We operate with complete transparency and honesty in all our dealings.'
          },
          {
            title: 'Reliability',
            description: 'You can count on us to deliver on our promises and meet deadlines.'
          },
          {
            title: 'Safety First',
            description: 'We prioritize safety in every project to protect our team and customers.'
          }
        ]
      }
    },
    {
      type: 'TeamSection',
      props: {
        title: 'Our Team',
        subtitle: 'Meet the experts behind our success',
        members: [
          {
            name: 'Jorge A Gutierrez',
            role: 'Co-founder',
            bio: 'Jorge brings extensive roofing expertise and leadership to guide our company\'s vision and growth.',
            image: '/placeholder.svg'
          },
          {
            name: 'Yoan Valiente',
            role: 'Co-founder',
            bio: 'Yoan leads our strategic initiatives and ensures operational excellence across all projects.',
            image: '/placeholder.svg'
          },
          {
            name: 'Gabriel Torres',
            role: 'Director of Operations',
            bio: 'Gabriel ensures smooth operations and maintains our high standards of quality in every project.',
            image: '/placeholder.svg'
          }
        ]
      }
    },
    {
      type: 'StatsSection',
      props: {
        title: 'Our Achievements',
        subtitle: 'Numbers that speak for themselves',
        stats: [
          {
            value: '1000+',
            label: 'Projects Completed',
            description: 'Successfully delivered roofing solutions'
          },
          {
            value: '15+',
            label: 'Years Experience',
            description: 'Serving Florida communities'
          },
          {
            value: '100%',
            label: 'Satisfaction',
            description: 'Customer satisfaction guaranteed'
          },
          {
            value: '24/7',
            label: 'Support',
            description: 'Emergency services available'
          }
        ]
      }
    },
    {
      type: 'AboutCTA',
      props: {
        title: 'Ready to Work with Us?',
        description: 'Contact Roofers LLC today to discuss your roofing needs',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

// Home page default data
const homeDefaultData: PageData = {
  root: {
    title: 'Home Page',
    description: 'Main landing page for Roofers LLC'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        tagline: '#1 Trusted Roofing Company in Florida',
        title: 'Transform Your Roof, Protect Your Home',
        subtitle: "Florida's most trusted roofing experts with over 30+ years of experience. Free estimates and financing available.",
        primaryButtonText: 'Get Free Estimate',
        primaryButtonLink: '/contact',
        secondaryButtonText: 'Call (*************',
        secondaryButtonLink: 'tel:+***********',
        features: [
          { icon: 'shield', text: 'Licensed & Insured' },
          { icon: 'star', text: '5-Star Reviews' },
          { icon: 'award', text: '30+ Years Experience' },
          { icon: 'check', text: 'Free Estimates' }
        ]
      }
    },
    {
      type: 'ServicesGrid',
      props: {
        title: 'Our Services',
        subtitle: 'Comprehensive roofing solutions for every need',
        services: [
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Residential Roofing',
            description: 'Complete roofing solutions for your home',
            link: '/services/residential'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Commercial Roofing',
            description: 'Professional roofing for businesses',
            link: '/services/commercial'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Emergency Repairs',
            description: '24/7 emergency roofing services',
            link: '/services/emergency'
          }
        ]
      }
    },
    {
      type: 'CTASection',
      props: {
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        title: 'Ready to Transform Your Roof?',
        description: 'Get a free estimate for your roofing project. Our experts are ready to help you protect your most valuable investment.',
        primaryButtonText: 'Get Free Estimate',
        primaryButtonLink: '/contact',
        secondaryButtonText: 'Call (*************',
        secondaryButtonLink: 'tel:+***********'
      }
    }
  ]
};

// Contact page default data
const contactDefaultData: PageData = {
  root: {
    title: 'Contact Us',
    description: 'Get in touch with Roofers LLC'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Contact Roofers LLC',
        subtitle: 'Get your free roofing estimate today',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Residential services default data (from actual ResidentialRoofing.tsx)
const residentialDefaultData: PageData = {
  root: {
    title: 'Residential Roofing Services',
    description: 'Expert residential roofing services in Florida'
  },
  content: [
    {
      type: 'ResidentialHero',
      props: {
        title: 'Residential Roofing Services by Roofers LLC',
        subtitle: 'Protect your home with quality roofing solutions from Florida\'s trusted experts',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'ResidentialServices',
      props: {
        title: 'Our Residential Services',
        services: [
          {
            title: "Roof Installation",
            description: "Complete installation of new roofing systems by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Replacement",
            description: "Full roof replacement with quality materials by Roofers LLC",
            image: "/placeholder.svg"
          },
          {
            title: "Roof Maintenance",
            description: "Regular maintenance to extend roof life by Roofers LLC",
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ResidentialMaterials',
      props: {
        title: 'Roofing Materials',
        materials: [
          {
            name: "Asphalt Shingles",
            description: "Durable and cost-effective roofing solution",
            features: [
              { text: "30-50 year lifespan" },
              { text: "Wide variety of colors" },
              { text: "Affordable option" },
              { text: "Easy maintenance" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Metal Roofing",
            description: "Long-lasting and energy-efficient option",
            features: [
              { text: "50+ year lifespan" },
              { text: "Energy efficient" },
              { text: "Fire resistant" },
              { text: "Environmentally friendly" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Tile Roofing",
            description: "Beautiful and durable ceramic or concrete tiles",
            features: [
              { text: "50+ year lifespan" },
              { text: "Lasting beauty" },
              { text: "Weather resistant" },
              { text: "Excellent insulation" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Flat Roofing",
            description: "Modern and versatile roofing solution for flat or low-slope roofs",
            features: [
              { text: "20-30 year lifespan" },
              { text: "Cost-effective" },
              { text: "Perfect for patios" },
              { text: "Energy efficient" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Wood Roofing",
            description: "Natural and aesthetically pleasing wood shakes and shingles",
            features: [
              { text: "20-25 year lifespan" },
              { text: "Unique natural look" },
              { text: "Excellent insulation" },
              { text: "Weather resistant" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'ResidentialCTA',
      props: {
        title: 'Ready to Get Started?',
        description: 'Contact Roofers LLC today for a comprehensive consultation for your residential roofing project.',
        buttonText: 'Request Consultation',
        buttonLink: '/request-consultation'
      }
    }
  ]
};

// Commercial services default data
const commercialDefaultData: PageData = {
  root: {
    title: 'Commercial Roofing Services',
    description: 'Professional roofing services for businesses'
  },
  content: [
    {
      type: 'CommercialHero',
      props: {
        title: 'Commercial Roofing Services',
        subtitle: 'Reliable roofing solutions for your business',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Emergency services default data
const emergencyDefaultData: PageData = {
  root: {
    title: 'Emergency Roofing Services',
    description: '24/7 emergency roofing repairs'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: '24/7 Emergency Roofing Services',
        subtitle: 'Fast response when you need it most',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Gallery default data
const galleryDefaultData: PageData = {
  root: {
    title: 'Project Gallery',
    description: 'View our completed roofing projects'
  },
  content: [
    {
      type: 'GallerySection',
      props: {
        title: 'Our Work',
        subtitle: 'See the quality of our roofing projects',
        images: []
      }
    }
  ]
};

// Areas we serve default data
const areasDefaultData: PageData = {
  root: {
    title: 'Areas We Serve',
    description: 'Roofing services across Florida'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Areas We Serve',
        subtitle: 'Professional roofing services across Florida',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Financing default data
const financingDefaultData: PageData = {
  root: {
    title: 'Financing Options',
    description: 'Flexible financing for your roofing project'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Financing Options',
        subtitle: 'Make your roofing project affordable',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Additional page data for all actual pages
const roofRepairDefaultData: PageData = {
  root: {
    title: 'Roof Repair Services',
    description: 'Professional roof repair solutions in Florida'
  },
  content: [
    {
      type: 'RepairHero',
      props: {
        title: 'Expert Roof Repair Services by Roofers LLC',
        subtitle: 'Fast, reliable roof repair solutions for all roofing types',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

const metalRoofingDefaultData: PageData = {
  root: {
    title: 'Metal Roofing Services',
    description: 'Premium metal roofing solutions in Florida'
  },
  content: [
    {
      type: 'MetalHero',
      props: {
        title: 'Metal Roofing Solutions by Roofers LLC',
        subtitle: 'Durable, energy-efficient metal roofing systems',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

const shingleRoofingDefaultData: PageData = {
  root: {
    title: 'Shingle Roofing Services',
    description: 'Quality shingle roofing installation and replacement'
  },
  content: [
    {
      type: 'ShingleHero',
      props: {
        title: 'Shingle Roofing Solutions by Roofers LLC',
        subtitle: 'Quality shingle roofing installation and replacement services',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

const tpoRoofingDefaultData: PageData = {
  root: {
    title: 'TPO Roofing Services',
    description: 'Professional TPO roofing solutions in Florida'
  },
  content: [
    {
      type: 'TPOHero',
      props: {
        title: 'TPO Roofing Services',
        subtitle: 'Professional TPO roofing solutions in Florida',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

const roofingEducationDefaultData: PageData = {
  root: {
    title: 'Roofing Education',
    description: 'Learn about roofing materials, maintenance, and best practices'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Roofing Education Center',
        subtitle: 'Learn about roofing materials, maintenance, and best practices',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Page data mapping with correct paths
const pageDataMap: Record<string, PageData> = {
  '/': homeDefaultData,
  '/about': aboutDefaultData,
  '/contact': contactDefaultData,
  '/residential-roofing': residentialDefaultData,
  '/commercial-roofing': commercialDefaultData,
  '/emergency-roofing': emergencyDefaultData,
  '/roof-repair': roofRepairDefaultData,
  '/metal-roofing': metalRoofingDefaultData,
  '/shingle-roofing': shingleRoofingDefaultData,
  '/tpo-roofing': tpoRoofingDefaultData,
  '/gallery': galleryDefaultData,
  '/areas-we-serve': areasDefaultData,
  '/financing': financingDefaultData,
  '/roofing-education': roofingEducationDefaultData
};

/**
 * Get the initial data for a page, checking localStorage first, then falling back to default data
 */
export function getPageData(pagePath: string): Data {
  // First, try to load saved data from localStorage
  const savedData = localStorage.getItem(`puck-data-${pagePath}`);
  if (savedData) {
    try {
      return JSON.parse(savedData);
    } catch (error) {
      console.warn(`Failed to parse saved data for ${pagePath}:`, error);
    }
  }

  // Fall back to default data
  const defaultData = pageDataMap[pagePath];
  if (defaultData) {
    return defaultData;
  }

  // Ultimate fallback for unknown pages
  return {
    root: {
      title: 'Page'
    },
    content: []
  };
}

/**
 * Get the appropriate Puck config for a page
 */
export async function getPageConfig(pagePath: string) {
  // Import configs dynamically to avoid circular dependencies
  switch (pagePath) {
    case '/about':
      return import('../puck/schemas/about').then(m => m.aboutConfig);
    case '/':
      return import('../puck/schemas/home').then(m => m.homeConfig);
    case '/contact':
      return import('../puck/schemas/contact').then(m => m.contactConfig);
    case '/residential-roofing':
      return import('../puck/schemas/residential').then(m => m.residentialConfig);
    case '/commercial-roofing':
      return import('../puck/schemas/commercial').then(m => m.commercialConfig);
    case '/emergency-roofing':
      return import('../puck/schemas/emergency').then(m => m.emergencyConfig);
    case '/roof-repair':
      return import('../puck/schemas/repair').then(m => m.repairConfig);
    case '/metal-roofing':
      return import('../puck/schemas/metal').then(m => m.metalConfig);
    case '/shingle-roofing':
      return import('../puck/schemas/shingle').then(m => m.shingleConfig);
    case '/tpo-roofing':
      return import('../puck/schemas/tpo').then(m => m.tpoConfig);
    case '/gallery':
      return import('../puck/schemas/gallery').then(m => m.galleryConfig);
    case '/areas-we-serve':
      return import('../puck/schemas/areas').then(m => m.areasConfig);
    case '/financing':
      return import('../puck/schemas/financing').then(m => m.financingConfig);
    default:
      // For unknown pages, use the general config
      return import('../puck/schema').then(m => m.config);
  }
}
