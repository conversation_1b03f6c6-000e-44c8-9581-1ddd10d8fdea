import { type Data } from '@measured/puck';

// Import default data from each page
interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface PageData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

// About page default data
const aboutDefaultData: PageData = {
  root: {
    title: 'About Roofers LLC',
    description: 'Your trusted roofing partner in Florida'
  },
  content: [
    {
      type: 'OurStory',
      props: {
        title: 'Our Story',
        content: [
          'With years of experience in the roofing industry, we\'ve built our reputation on quality workmanship and exceptional customer service. Our team of skilled professionals is dedicated to providing the best roofing solutions for homeowners and businesses across Florida.',
          'We understand that your roof is one of the most important investments you\'ll make in your property. That\'s why we use only the highest quality materials and employ the most skilled craftsmen in the industry.'
        ],
        image: '/placeholder.svg'
      }
    },
    {
      type: 'CompanyValues',
      props: {
        title: 'Our Values',
        subtitle: 'What sets us apart from the competition',
        values: [
          {
            title: 'Quality Craftsmanship',
            description: 'We take pride in our work and ensure every project meets our high standards of excellence.'
          },
          {
            title: 'Customer Service',
            description: 'Our clients are our priority, and we strive to exceed their expectations in every interaction.'
          },
          {
            title: 'Professional Excellence',
            description: 'We maintain the highest standards of professionalism in everything we do.'
          },
          {
            title: 'Integrity',
            description: 'We operate with complete transparency and honesty in all our dealings.'
          },
          {
            title: 'Reliability',
            description: 'You can count on us to deliver on our promises and meet deadlines.'
          },
          {
            title: 'Safety First',
            description: 'We prioritize safety in every project to protect our team and customers.'
          }
        ]
      }
    },
    {
      type: 'TeamSection',
      props: {
        title: 'Our Team',
        subtitle: 'Meet the experts behind our success',
        members: [
          {
            name: 'Jorge A Gutierrez',
            role: 'Co-founder',
            bio: 'Jorge brings extensive roofing expertise and leadership to guide our company\'s vision and growth.',
            image: '/placeholder.svg'
          },
          {
            name: 'Yoan Valiente',
            role: 'Co-founder',
            bio: 'Yoan leads our strategic initiatives and ensures operational excellence across all projects.',
            image: '/placeholder.svg'
          },
          {
            name: 'Gabriel Torres',
            role: 'Director of Operations',
            bio: 'Gabriel ensures smooth operations and maintains our high standards of quality in every project.',
            image: '/placeholder.svg'
          }
        ]
      }
    },
    {
      type: 'StatsSection',
      props: {
        title: 'Our Achievements',
        subtitle: 'Numbers that speak for themselves',
        stats: [
          {
            value: '1000+',
            label: 'Projects Completed',
            description: 'Successfully delivered roofing solutions'
          },
          {
            value: '15+',
            label: 'Years Experience',
            description: 'Serving Florida communities'
          },
          {
            value: '100%',
            label: 'Satisfaction',
            description: 'Customer satisfaction guaranteed'
          },
          {
            value: '24/7',
            label: 'Support',
            description: 'Emergency services available'
          }
        ]
      }
    },
    {
      type: 'AboutCTA',
      props: {
        title: 'Ready to Work with Us?',
        description: 'Contact Roofers LLC today to discuss your roofing needs',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

// Home page default data
const homeDefaultData: PageData = {
  root: {
    title: 'Home Page',
    description: 'Main landing page for Roofers LLC'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        tagline: '#1 Trusted Roofing Company in Florida',
        title: 'Transform Your Roof, Protect Your Home',
        subtitle: "Florida's most trusted roofing experts with over 30+ years of experience. Free estimates and financing available.",
        primaryButtonText: 'Get Free Estimate',
        primaryButtonLink: '/contact',
        secondaryButtonText: 'Call (*************',
        secondaryButtonLink: 'tel:+***********',
        features: [
          { icon: 'shield', text: 'Licensed & Insured' },
          { icon: 'star', text: '5-Star Reviews' },
          { icon: 'award', text: '30+ Years Experience' },
          { icon: 'check', text: 'Free Estimates' }
        ]
      }
    },
    {
      type: 'ServicesGrid',
      props: {
        title: 'Our Services',
        subtitle: 'Comprehensive roofing solutions for every need',
        services: [
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Residential Roofing',
            description: 'Complete roofing solutions for your home',
            link: '/services/residential'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Commercial Roofing',
            description: 'Professional roofing for businesses',
            link: '/services/commercial'
          },
          {
            image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
            title: 'Emergency Repairs',
            description: '24/7 emergency roofing services',
            link: '/services/emergency'
          }
        ]
      }
    },
    {
      type: 'CTASection',
      props: {
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        title: 'Ready to Transform Your Roof?',
        description: 'Get a free estimate for your roofing project. Our experts are ready to help you protect your most valuable investment.',
        primaryButtonText: 'Get Free Estimate',
        primaryButtonLink: '/contact',
        secondaryButtonText: 'Call (*************',
        secondaryButtonLink: 'tel:+***********'
      }
    }
  ]
};

// Contact page default data
const contactDefaultData: PageData = {
  root: {
    title: 'Contact Us',
    description: 'Get in touch with Roofers LLC'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Contact Roofers LLC',
        subtitle: 'Get your free roofing estimate today',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Residential services default data
const residentialDefaultData: PageData = {
  root: {
    title: 'Residential Roofing Services',
    description: 'Professional roofing services for homeowners'
  },
  content: [
    {
      type: 'ResidentialHero',
      props: {
        title: 'Residential Roofing Services',
        subtitle: 'Protect your home with quality roofing solutions',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Commercial services default data
const commercialDefaultData: PageData = {
  root: {
    title: 'Commercial Roofing Services',
    description: 'Professional roofing services for businesses'
  },
  content: [
    {
      type: 'CommercialHero',
      props: {
        title: 'Commercial Roofing Services',
        subtitle: 'Reliable roofing solutions for your business',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Emergency services default data
const emergencyDefaultData: PageData = {
  root: {
    title: 'Emergency Roofing Services',
    description: '24/7 emergency roofing repairs'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: '24/7 Emergency Roofing Services',
        subtitle: 'Fast response when you need it most',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Gallery default data
const galleryDefaultData: PageData = {
  root: {
    title: 'Project Gallery',
    description: 'View our completed roofing projects'
  },
  content: [
    {
      type: 'GallerySection',
      props: {
        title: 'Our Work',
        subtitle: 'See the quality of our roofing projects',
        images: []
      }
    }
  ]
};

// Areas we serve default data
const areasDefaultData: PageData = {
  root: {
    title: 'Areas We Serve',
    description: 'Roofing services across Florida'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Areas We Serve',
        subtitle: 'Professional roofing services across Florida',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Financing default data
const financingDefaultData: PageData = {
  root: {
    title: 'Financing Options',
    description: 'Flexible financing for your roofing project'
  },
  content: [
    {
      type: 'HeroSection',
      props: {
        title: 'Financing Options',
        subtitle: 'Make your roofing project affordable',
        backgroundImage: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51'
      }
    }
  ]
};

// Page data mapping
const pageDataMap: Record<string, PageData> = {
  '/': homeDefaultData,
  '/about': aboutDefaultData,
  '/contact': contactDefaultData,
  '/services/residential': residentialDefaultData,
  '/services/commercial': commercialDefaultData,
  '/services/emergency': emergencyDefaultData,
  '/gallery': galleryDefaultData,
  '/areas-we-serve': areasDefaultData,
  '/financing': financingDefaultData
};

/**
 * Get the initial data for a page, checking localStorage first, then falling back to default data
 */
export function getPageData(pagePath: string): Data {
  // First, try to load saved data from localStorage
  const savedData = localStorage.getItem(`puck-data-${pagePath}`);
  if (savedData) {
    try {
      return JSON.parse(savedData);
    } catch (error) {
      console.warn(`Failed to parse saved data for ${pagePath}:`, error);
    }
  }

  // Fall back to default data
  const defaultData = pageDataMap[pagePath];
  if (defaultData) {
    return defaultData;
  }

  // Ultimate fallback for unknown pages
  return {
    root: {
      title: 'Page',
      description: 'Page description'
    },
    content: []
  };
}

/**
 * Get the appropriate Puck config for a page
 */
export function getPageConfig(pagePath: string) {
  // Import configs dynamically to avoid circular dependencies
  switch (pagePath) {
    case '/about':
      return import('../puck/schemas/about').then(m => m.aboutConfig);
    case '/':
      return import('../puck/schemas/home').then(m => m.homeConfig);
    default:
      // For now, use the general config for other pages
      return import('../puck/schema').then(m => m.config);
  }
}
