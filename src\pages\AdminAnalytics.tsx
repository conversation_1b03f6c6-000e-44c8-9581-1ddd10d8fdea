import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { 
  TrendingUp, 
  Users, 
  Eye, 
  Clock, 
  Globe, 
  Smartphone, 
  Monitor,
  BarChart3,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

export default function AdminAnalytics() {
  // Mock analytics data - in a real app, this would come from an API
  const analyticsData = {
    overview: {
      totalVisitors: 12543,
      pageViews: 45678,
      avgSessionDuration: '3m 24s',
      bounceRate: '42%',
      trends: {
        visitors: '+12.5%',
        pageViews: '****%',
        sessionDuration: '****%',
        bounceRate: '-3.1%'
      }
    },
    topPages: [
      { path: '/', views: 8234, title: 'Home Page' },
      { path: '/residential-roofing', views: 3456, title: 'Residential Roofing' },
      { path: '/commercial-roofing', views: 2987, title: 'Commercial Roofing' },
      { path: '/emergency-roofing', views: 2134, title: 'Emergency Roofing' },
      { path: '/contact', views: 1876, title: 'Contact' }
    ],
    devices: {
      mobile: 65,
      desktop: 28,
      tablet: 7
    },
    traffic: [
      { source: 'Organic Search', visitors: 5234, percentage: 42 },
      { source: 'Direct', visitors: 3456, percentage: 28 },
      { source: 'Social Media', visitors: 2134, percentage: 17 },
      { source: 'Referral', visitors: 1234, percentage: 10 },
      { source: 'Email', visitors: 485, percentage: 3 }
    ]
  };

  const getTrendIcon = (trend: string) => {
    return trend.startsWith('+') ? 
      <ArrowUpRight className="h-4 w-4 text-green-600" /> : 
      <ArrowDownRight className="h-4 w-4 text-red-600" />;
  };

  const getTrendColor = (trend: string) => {
    return trend.startsWith('+') ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <AdminPageHeader
        title="Analytics Dashboard"
        description="Monitor your website performance and visitor insights"
      />

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Visitors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.overview.totalVisitors.toLocaleString()}</div>
            <div className={`text-xs flex items-center ${getTrendColor(analyticsData.overview.trends.visitors)}`}>
              {getTrendIcon(analyticsData.overview.trends.visitors)}
              <span className="ml-1">{analyticsData.overview.trends.visitors} from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Page Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.overview.pageViews.toLocaleString()}</div>
            <div className={`text-xs flex items-center ${getTrendColor(analyticsData.overview.trends.pageViews)}`}>
              {getTrendIcon(analyticsData.overview.trends.pageViews)}
              <span className="ml-1">{analyticsData.overview.trends.pageViews} from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Session Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.overview.avgSessionDuration}</div>
            <div className={`text-xs flex items-center ${getTrendColor(analyticsData.overview.trends.sessionDuration)}`}>
              {getTrendIcon(analyticsData.overview.trends.sessionDuration)}
              <span className="ml-1">{analyticsData.overview.trends.sessionDuration} from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.overview.bounceRate}</div>
            <div className={`text-xs flex items-center ${getTrendColor(analyticsData.overview.trends.bounceRate)}`}>
              {getTrendIcon(analyticsData.overview.trends.bounceRate)}
              <span className="ml-1">{analyticsData.overview.trends.bounceRate} from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Top Pages</span>
            </CardTitle>
            <CardDescription>Most visited pages this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topPages.map((page, index) => (
                <div key={page.path} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{page.title}</p>
                      <p className="text-xs text-gray-500">{page.path}</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium">{page.views.toLocaleString()}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Monitor className="h-5 w-5" />
              <span>Device Breakdown</span>
            </CardTitle>
            <CardDescription>Visitor device preferences</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Mobile</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${analyticsData.devices.mobile}%` }}></div>
                  </div>
                  <span className="text-sm font-medium">{analyticsData.devices.mobile}%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Monitor className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Desktop</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: `${analyticsData.devices.desktop}%` }}></div>
                  </div>
                  <span className="text-sm font-medium">{analyticsData.devices.desktop}%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Globe className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">Tablet</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{ width: `${analyticsData.devices.tablet}%` }}></div>
                  </div>
                  <span className="text-sm font-medium">{analyticsData.devices.tablet}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Traffic Sources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Traffic Sources</span>
          </CardTitle>
          <CardDescription>Where your visitors are coming from</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {analyticsData.traffic.map((source, index) => (
              <div key={source.source} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{source.visitors.toLocaleString()}</div>
                <div className="text-sm text-gray-600 mb-2">{source.source}</div>
                <Badge variant="outline">{source.percentage}%</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
