import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, SunMoon, <PERSON>, <PERSON>, Zap } from 'lucide-react';

const RoofingEducation = () => {
  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold text-center mb-8">Understanding Your Roofing Project</h1>
      
      {/* Introduction */}
      <div className="max-w-3xl mx-auto mb-12 text-center">
        <p className="text-xl text-gray-600">
          Thank you for submitting your roofing request. While we prepare your quote,
          here's some valuable information about roofing projects.
        </p>
      </div>

      {/* Key Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
        {/* Signs You Need a New Roof */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex items-center mb-6">
            <AlertTriangle className="w-8 h-8 text-accent mr-4" />
            <h2 className="text-2xl font-semibold">Signs You Need a New Roof</h2>
          </div>
          <ul className="space-y-4 text-gray-600">
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Age of roof is over 20 years</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Curling or missing shingles</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Daylight showing through roof boards</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Sagging roof deck</span>
            </li>
          </ul>
        </div>

        {/* Types of Roofing Materials */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex items-center mb-6">
            <Wrench className="w-8 h-8 text-accent mr-4" />
            <h2 className="text-2xl font-semibold">Popular Roofing Materials</h2>
          </div>
          <ul className="space-y-4 text-gray-600">
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Asphalt Shingles: Most common, cost-effective, 20-30 year lifespan</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>Metal Roofing: Durable, energy-efficient, 50+ year lifespan</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-accent rounded-full mt-2 mr-3"></span>
              <span>TPO: Popular for flat roofs, excellent UV resistance</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Weather Resistance Section */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
        <h2 className="text-2xl font-semibold mb-8 text-center">Florida Weather Considerations</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <SunMoon className="w-12 h-12 text-accent mx-auto mb-4" />
            <h3 className="font-semibold mb-2">UV Protection</h3>
            <p className="text-gray-600">Protection against intense Florida sun</p>
          </div>
          <div className="text-center">
            <Cloud className="w-12 h-12 text-accent mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Rain Resistance</h3>
            <p className="text-gray-600">Superior water-shedding capabilities</p>
          </div>
          <div className="text-center">
            <Wind className="w-12 h-12 text-accent mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Hurricane Ready</h3>
            <p className="text-gray-600">Built to withstand high winds</p>
          </div>
          <div className="text-center">
            <Zap className="w-12 h-12 text-accent mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Lightning Protection</h3>
            <p className="text-gray-600">Optional lightning protection systems</p>
          </div>
        </div>
      </div>

      {/* What to Expect */}
      <div className="bg-gradient-to-r from-primary to-primary-dark text-white rounded-xl shadow-lg p-8">
        <div className="flex items-center mb-6">
          <Info className="w-8 h-8 text-accent mr-4" />
          <h2 className="text-2xl font-semibold">What to Expect Next</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="font-semibold mb-2">1. Initial Contact</h3>
            <p className="text-white/90">
              Our team will contact you within 24 hours to discuss your roofing needs.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">2. Professional Inspection</h3>
            <p className="text-white/90">
              We'll schedule a thorough inspection of your roof to assess its condition.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">3. Detailed Quote</h3>
            <p className="text-white/90">
              You'll receive a comprehensive quote outlining all costs and recommendations.
            </p>
          </div>
        </div>
      </div>

      {/* Contact Banner */}
      <div className="mt-12 text-center">
        <p className="text-xl mb-4">
          Have questions while you wait? Our team is here to help!
        </p>
        <a 
          href="tel:+13053761808"
          className="inline-flex items-center justify-center px-8 py-3 text-lg font-medium text-white bg-accent rounded-full hover:bg-accent-dark transition-colors duration-300"
        >
          Call (*************
        </a>
      </div>
    </div>
  );
};

export default RoofingEducation;