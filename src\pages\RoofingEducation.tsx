import React, { useState } from 'react';
import { Puck, Render, Data } from '@measured/puck';
import { roofingEducationConfig } from '@/puck/schemas/roofing-education';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface RoofingEducationData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const roofingEducationDefaultData: RoofingEducationData = {
  root: {
    title: 'Roofing Education',
    description: 'Learn about roofing projects and what to expect'
  },
  content: [
    {
      type: 'EducationHero',
      props: {
        title: 'Understanding Your Roofing Project',
        description: 'Thank you for submitting your roofing request. While we prepare your quote, here\'s some valuable information about roofing projects.'
      }
    },
    {
      type: 'EducationSections',
      props: {
        sections: [
          {
            icon: 'AlertTriangle',
            title: 'Signs You Need a New Roof',
            items: [
              { text: 'Age of roof is over 20 years' },
              { text: 'Curling or missing shingles' },
              { text: 'Daylight showing through roof boards' },
              { text: 'Sagging roof deck' }
            ]
          },
          {
            icon: 'Wrench',
            title: 'Popular Roofing Materials',
            items: [
              { text: 'Asphalt Shingles: Most common, cost-effective, 20-30 year lifespan' },
              { text: 'Metal Roofing: Durable, energy-efficient, 50+ year lifespan' },
              { text: 'TPO: Popular for flat roofs, excellent UV resistance' }
            ]
          }
        ]
      }
    },
    {
      type: 'WeatherConsiderations',
      props: {
        title: 'Florida Weather Considerations',
        considerations: [
          {
            icon: 'SunMoon',
            title: 'UV Protection',
            description: 'Protection against intense Florida sun'
          },
          {
            icon: 'Cloud',
            title: 'Rain Resistance',
            description: 'Superior water-shedding capabilities'
          },
          {
            icon: 'Wind',
            title: 'Hurricane Ready',
            description: 'Built to withstand high winds'
          },
          {
            icon: 'Zap',
            title: 'Lightning Protection',
            description: 'Optional lightning protection systems'
          }
        ]
      }
    },
    {
      type: 'ProcessSteps',
      props: {
        title: 'What to Expect Next',
        steps: [
          {
            title: '1. Initial Contact',
            description: 'Our team will contact you within 24 hours to discuss your roofing needs.'
          },
          {
            title: '2. Professional Inspection',
            description: 'We\'ll schedule a thorough inspection of your roof to assess its condition.'
          },
          {
            title: '3. Detailed Quote',
            description: 'You\'ll receive a comprehensive quote outlining all costs and recommendations.'
          }
        ]
      }
    },
    {
      type: 'ContactBanner',
      props: {
        message: 'Have questions while you wait? Our team is here to help!',
        buttonText: 'Call (*************',
        phoneNumber: '+13053761808'
      }
    }
  ]
};

const RoofingEducation: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<RoofingEducationData>(roofingEducationDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={roofingEducationConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as RoofingEducationData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={roofingEducationConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-accent text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default RoofingEducation;