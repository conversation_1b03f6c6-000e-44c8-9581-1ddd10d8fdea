# Server Monitoring & Maintenance Guidelines

## 🚨 **CRITICAL MAINTENANCE RULES**

### **STRICT CONSTRAINTS - NEVER MODIFY**
- ❌ **Database Schema**: Never alter existing database tables, columns, or relationships
- ❌ **UI Components**: Never modify existing React components, styling, or user interface elements  
- ❌ **Public Pages**: Never change public-facing website pages or their functionality
- ❌ **Puck CMS Configuration**: Never modify existing Puck editor schemas or component configurations
- ❌ **Authentication System**: Never alter the implemented Auth.js authentication or RBAC system

## 🔧 **SERVER STATUS MONITORING**

### **Current Server Configuration**
- **URL**: http://localhost:8080/
- **Port**: 8080 (development server)
- **Command**: `npm run start` (NOT `npm start`)
- **Status**: ✅ OPERATIONAL

### **Daily Monitoring Checklist**
1. **Server Accessibility**: Verify http://localhost:8080 loads correctly
2. **Admin Login**: Test login at http://localhost:8080/login (<EMAIL> / admin123)
3. **Admin Dashboard**: Confirm http://localhost:8080/admin is accessible after login
4. **Terminal Output**: Check for any compilation errors or warnings
5. **Port Status**: Ensure port 8080 is available and not conflicting

## 🛠 **TROUBLESHOOTING GUIDE**

### **Common Issues & Solutions**

#### **1. Import/Export Errors** (Most Common)
**Symptoms**: 
- Server fails to start with "No matching export" errors
- Compilation errors in terminal

**Solution**:
```bash
# Check import statements match export format
# Example fix: Change default import to named import
# FROM: import config from '@/puck/schema';
# TO:   import { config } from '@/puck/schema';
```

#### **2. Port Conflicts**
**Symptoms**:
- Server starts on port 8081 instead of 8080
- "Port 8080 is in use" message

**Solution**:
```bash
# Check what's using port 8080
netstat -ano | findstr :8080

# Kill the process (replace PID with actual process ID)
taskkill /PID <PID> /F

# Restart server
npm run start
```

#### **3. Dependency Issues**
**Symptoms**:
- Module not found errors
- Package import failures

**Solution**:
```bash
# Reinstall dependencies
npm install

# Clear cache if needed
npm cache clean --force
npm install
```

#### **4. Compilation Errors**
**Symptoms**:
- TypeScript errors
- Syntax errors in terminal

**Solution**:
- Fix syntax errors without modifying existing functionality
- Ensure all imports/exports are correctly formatted
- Do not modify existing component logic or schemas

## 🔄 **SERVER RESTART PROCESS**

### **Standard Restart Procedure**
```bash
# 1. Check current terminal output for errors
# 2. Fix any import/export issues (without modifying schemas)
# 3. Kill existing processes if needed
# 4. Start server with correct command
npm run start

# 5. Verify server is running
# Check: http://localhost:8080

# 6. Test authentication
# Login: http://localhost:8080/login
# Admin: http://localhost:8080/admin
```

### **Emergency Recovery Steps**
1. **Identify Issue**: Read terminal output carefully
2. **Fix Imports**: Correct any import/export mismatches
3. **Clear Port**: Kill any processes using port 8080
4. **Restart Server**: Use `npm run start`
5. **Verify Access**: Test public site and admin login
6. **Document Issue**: Note what was fixed for future reference

## 📊 **MONITORING SCHEDULE**

### **Hourly Checks** (During Development)
- Server accessibility at http://localhost:8080
- Terminal output for new errors

### **Daily Checks**
- Full authentication flow test
- Admin dashboard functionality
- All public pages loading correctly
- Database connection status

### **Weekly Checks**
- Dependency updates (if needed)
- Security audit of authentication system
- Performance monitoring
- Backup verification

## 🔍 **ERROR IDENTIFICATION**

### **Critical Errors** (Immediate Action Required)
- Server won't start
- Import/export errors
- Authentication system failures
- Database connection issues

### **Warning Signs** (Monitor Closely)
- Slow server response times
- Intermittent compilation warnings
- Port conflicts
- Memory usage increases

### **Normal Operations** (No Action Needed)
- Successful server startup on port 8080
- Clean terminal output with no errors
- Fast page loading times
- Successful authentication flows

## 🚀 **PERFORMANCE OPTIMIZATION**

### **Server Performance**
- Monitor startup time (should be under 5 seconds)
- Watch for memory leaks during development
- Ensure hot reload is working correctly

### **Application Performance**
- Page load times under 2 seconds
- Authentication response under 1 second
- Admin dashboard loads quickly
- Puck editor responsive

## 📝 **INCIDENT LOGGING**

### **What to Document**
- Date and time of issue
- Error messages (exact text)
- Steps taken to resolve
- Final solution applied
- Time to resolution

### **Example Log Entry**
```
Date: 2024-12-XX
Issue: Server failed to start - import error in ProtectedPuckEditor.tsx
Error: "No matching export in src/puck/schema.ts for import default"
Solution: Changed default import to named import
Resolution Time: 5 minutes
Status: ✅ Resolved
```

## 🔐 **SECURITY MONITORING**

### **Authentication System**
- Login functionality working
- Session management active
- Role-based access enforced
- Admin area properly protected

### **Database Security**
- Connection secure and encrypted
- No unauthorized access attempts
- Backup systems operational
- Schema integrity maintained

## 📞 **EMERGENCY CONTACTS**

### **System Information**
- **Database**: MySQL roofers01
- **Admin User**: <EMAIL> / admin123
- **Server Port**: 8080
- **Environment**: Development

### **Quick Reference**
- **Start Server**: `npm run start`
- **Check Port**: `netstat -ano | findstr :8080`
- **Kill Process**: `taskkill /PID <PID> /F`
- **Test Login**: http://localhost:8080/login

---

**Remember**: Always follow the strict constraints. Never modify existing schemas, UI components, public pages, Puck configurations, or the authentication system. Focus only on maintaining server uptime and fixing technical issues.
