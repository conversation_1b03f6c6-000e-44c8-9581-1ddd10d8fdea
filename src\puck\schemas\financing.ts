import * as React from 'react';
import { type Config } from '@measured/puck';
import { sharedFields, defaultVerticalPadding } from './shared';
import FinancingForm from '@/components/forms/FinancingForm';
import { financingImages } from '@/lib/image-paths';

interface HeroProps {
  title?: string;
  description?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface Option {
  title: string;
  description: string;
  features: Array<{ text: string }>;
  image: string;
}

interface OptionsProps {
  title?: string;
  options?: Option[];
  verticalPadding?: string;
}

interface BenefitsProps {
  title?: string;
  description?: string;
  benefits?: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
  verticalPadding?: string;
}

interface FormSectionProps {
  title?: string;
  description?: string;
  verticalPadding?: string;
}

export const financingConfig: Config = {
  components: {
    FinancingHero: {
      render: ({
        title = 'Make Your Roofing Project More Affordable',
        description = "With over 30 years of experience, we have helped thousands of homeowners finance their roofing projects",
        backgroundImage = financingImages.hero,
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-primary text-white ${verticalPadding} bg-cover bg-center`,
          style: {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url("${backgroundImage}")`
          }
        }, React.createElement('div', {
          className: 'container mx-auto px-4'
        }, React.createElement('div', {
          className: 'max-w-3xl mx-auto text-center'
        }, [
          React.createElement('h1', {
            className: 'text-4xl md:text-5xl font-bold mb-6',
            key: 'title'
          }, title),
          React.createElement('p', {
            className: 'text-xl mb-8',
            key: 'description'
          }, description)
        ])));
      },
      fields: {
        title: { type: 'text' },
        description: { type: 'textarea' },
        backgroundImage: { type: 'text' },
        verticalPadding: sharedFields.verticalPadding
      }
    },

    FinancingOptions: {
      render: ({
        title = 'Our Financing Options',
        options = [
          {
            title: "Fixed Monthly Payments",
            description: "Predictable monthly payments with competitive interest rates",
            features: [{ text: "Fixed interest rates" }, { text: "Terms up to 12 years" }, { text: "Quick approval process" }, { text: "No prepayment penalties" }],
            image: financingImages.fixed
          },
          {
            title: "Same as Cash",
            description: "No interest if paid in full within promotional period",
            features: [{ text: "0% interest option" }, { text: "6-18 month terms" }, { text: "Flexible payment options" }, { text: "Easy qualification" }],
            image: financingImages.sameAsCash
          },
          {
            title: "Low-Interest Financing",
            description: "Affordable long-term financing solutions",
            features: [{ text: "Competitive rates" }, { text: "Longer terms available" }, { text: "Low monthly payments" }, { text: "Multiple term options" }],
            image: financingImages.lowInterest
          }
        ],
        verticalPadding = defaultVerticalPadding
      }: OptionsProps) => {
        return React.createElement('section', {
          className: `${verticalPadding} bg-gray-50`
        }, React.createElement('div', {
          className: 'container mx-auto px-4'
        }, [
          React.createElement('h2', {
            className: 'text-3xl font-bold text-center mb-12',
            key: 'title'
          }, title),
          React.createElement('div', {
            className: 'grid grid-cols-1 md:grid-cols-3 gap-8',
            key: 'options-grid'
          }, options.map((option, index) => 
            React.createElement('div', {
              className: 'bg-white rounded-lg shadow-lg p-6',
              key: index
            }, [
              React.createElement('div', {
                className: 'mb-4 rounded-md overflow-hidden',
                key: 'image-container'
              }, React.createElement('img', {
                src: option.image,
                alt: option.title,
                className: 'w-full h-40 object-cover'
              })),
              React.createElement('h3', {
                className: 'text-xl font-bold mb-4',
                key: 'option-title'
              }, option.title),
              React.createElement('p', {
                className: 'text-gray-600 mb-6',
                key: 'option-description'
              }, option.description),
              React.createElement('ul', {
                className: 'space-y-2',
                key: 'features-list'
              }, option.features.map((feature, i) => 
                React.createElement('li', {
                  className: 'flex items-center text-gray-600',
                  key: i
                }, [
                  React.createElement('svg', {
                    className: 'w-4 h-4 mr-2 text-primary',
                    viewBox: '0 0 20 20',
                    fill: 'currentColor',
                    key: 'check-icon'
                  }, React.createElement('path', {
                    fillRule: 'evenodd',
                    d: 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z',
                    clipRule: 'evenodd'
                  })),
                  feature.text
                ])
              ))
            ])
          ))
        ]));
      },
      fields: {
        title: { type: 'text' },
        verticalPadding: sharedFields.verticalPadding,
        options: {
          type: 'array',
          arrayFields: {
            title: { type: 'text' },
            description: { type: 'textarea' },
            image: { type: 'text' },
            features: {
              type: 'array',
              arrayFields: {
                text: { type: 'text' }
              }
            }
          }
        }
      }
    },

    FinancingBenefits: {
      render: ({
        title = 'Why Choose Our Financing?',
        description = 'We partner with trusted financial institutions to provide hassle-free financing solutions tailored to your needs.',
        benefits = [
          {
            icon: '⚡',
            title: 'Simple Process',
            description: 'Easy application with minimal paperwork'
          },
          {
            icon: '💳',
            title: 'Flexible Terms',
            description: 'Multiple financing options to choose from'
          },
          {
            icon: '📊',
            title: 'Competitive Rates',
            description: 'Access to favorable interest rates'
          },
          {
            icon: '🤝',
            title: 'Dedicated Support',
            description: 'Guidance throughout the process'
          }
        ],
        verticalPadding = defaultVerticalPadding
      }: BenefitsProps) => {
        return React.createElement('section', {
          className: verticalPadding
        }, React.createElement('div', {
          className: 'container mx-auto px-4'
        }, [
          React.createElement('div', {
            className: 'max-w-3xl mx-auto text-center mb-12',
            key: 'header'
          }, [
            React.createElement('h2', {
              className: 'text-3xl font-bold mb-4',
              key: 'title'
            }, title),
            React.createElement('p', {
              className: 'text-gray-600',
              key: 'description'
            }, description)
          ]),
          React.createElement('div', {
            className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8',
            key: 'benefits-grid'
          }, benefits.map((benefit, index) => (
            React.createElement('div', {
              className: 'text-center',
              key: index
            }, [
              React.createElement('div', {
                className: 'text-4xl mb-4',
                key: 'icon'
              }, benefit.icon),
              React.createElement('h3', {
                className: 'text-xl font-bold mb-2',
                key: 'title'
              }, benefit.title),
              React.createElement('p', {
                className: 'text-gray-600',
                key: 'description'
              }, benefit.description)
            ])
          )))
        ]));
      },
      fields: {
        title: { type: 'text' },
        description: { type: 'textarea' },
        verticalPadding: sharedFields.verticalPadding,
        benefits: {
          type: 'array',
          arrayFields: {
            icon: { type: 'text' },
            title: { type: 'text' },
            description: { type: 'textarea' }
          }
        }
      }
    },

    FinancingFormSection: {
      render: ({
        title = 'Get Started Today',
        description = 'Complete the simple form below and our financing team will contact you to discuss your options within 24-48 hours.',
        verticalPadding = defaultVerticalPadding
      }: FormSectionProps) => {
        return React.createElement('section', {
          className: `${verticalPadding} bg-gray-50`
        }, React.createElement('div', {
          className: 'container mx-auto px-4'
        }, [
          React.createElement('div', {
            className: 'max-w-3xl mx-auto text-center mb-12',
            key: 'header'
          }, [
            React.createElement('h2', {
              className: 'text-3xl font-bold mb-4',
              key: 'title'
            }, title),
            React.createElement('p', {
              className: 'text-gray-600',
              key: 'description'
            }, description)
          ]),
          React.createElement(FinancingForm, { key: 'form' })
        ]));
      },
      fields: {
        title: { type: 'text' },
        description: { type: 'textarea' },
        verticalPadding: sharedFields.verticalPadding
      }
    }
  }
};