import React from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "./components/ui/toaster";
import ScrollToTop from './components/ScrollToTop';
import { TooltipProvider } from "./components/ui/tooltip";
import Navbar from "./components/layout/Navbar";
import Footer from "./components/layout/Footer";
import { AuthProvider, ProtectedRoute } from "./hooks/useAuth";
import AdminLayout from "./components/AdminLayout";
import ProtectedPuckEditor from "./components/ProtectedPuckEditor";
import type { FC } from 'react';

// Pages
import HomePage from "./pages/Home";
import PuckHome from "./pages/PuckHome"; // Add Puck version
import About from "./pages/About";
import ContactPage from "./pages/Contact";
import Financing from "./pages/Financing";
import ResidentialRoofing from "./pages/ResidentialRoofing";
import CommercialRoofing from "./pages/CommercialRoofing";
import RoofRepair from "./pages/RoofRepair";
import Gallery from "./pages/Gallery";
import AreasWeServe from "./pages/AreasWeServe";
import NotFound from "./pages/NotFound";
import TPORoofing from "./pages/TPORoofing";
import MetalRoofing from "./pages/MetalRoofing";
import ShingleRoofing from "./pages/ShingleRoofing";
import EmergencyRoofing from "./pages/EmergencyRoofing";
import RoofingEducation from "./pages/RoofingEducation";
import RequestConsultation from "./pages/RequestConsultation";
import RequestEstimate from "./pages/RequestEstimate";
import RequestEmergencyRepair from "./pages/RequestEmergencyRepair";
import ConsultationSuccess from "./pages/ConsultationSuccess";
import EstimateSuccess from "./pages/EstimateSuccess";
import EmergencySuccess from "./pages/EmergencySuccess";
import FinancingSuccess from "./pages/FinancingSuccess";

// Auth pages
import Login from "./pages/Login";
import AdminDashboard from "./pages/AdminDashboard";

const App: FC = () => (
  <React.StrictMode>
    <TooltipProvider>
      <AuthProvider>
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
          {/* Public Routes */}
          <Route path="/" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <HomePage />
              </main>
              <Footer />
            </div>
          } />
          
          {/* Puck Test Route */}
          <Route path="/puck" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <PuckHome />
              </main>
              <Footer />
            </div>
          } />

          {/* Authentication Routes */}
          <Route path="/login" element={<Login />} />

          {/* Protected Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute
              fallback={<Navigate to="/login?callbackUrl=/admin" replace />}
            >
              <AdminLayout />
            </ProtectedRoute>
          }>
            <Route index element={<AdminDashboard />} />
            {/* Protected Puck Editor Routes */}
            <Route path="edit/*" element={
              <ProtectedRoute
                requiredRoles={['admin', 'editor']}
                fallback={<Navigate to="/admin" replace />}
              >
                <Routes>
                  <Route path="/" element={
                    <ProtectedPuckEditor
                      pagePath="/"
                      pageTitle="Home Page"
                    />
                  } />
                  <Route path="/about" element={
                    <ProtectedPuckEditor
                      pagePath="/about"
                      pageTitle="About Us"
                    />
                  } />
                  <Route path="/contact" element={
                    <ProtectedPuckEditor
                      pagePath="/contact"
                      pageTitle="Contact"
                    />
                  } />
                  <Route path="/services/residential" element={
                    <ProtectedPuckEditor
                      pagePath="/services/residential"
                      pageTitle="Residential Services"
                    />
                  } />
                  <Route path="/services/commercial" element={
                    <ProtectedPuckEditor
                      pagePath="/services/commercial"
                      pageTitle="Commercial Services"
                    />
                  } />
                  <Route path="/services/emergency" element={
                    <ProtectedPuckEditor
                      pagePath="/services/emergency"
                      pageTitle="Emergency Repairs"
                    />
                  } />
                  <Route path="/gallery" element={
                    <ProtectedPuckEditor
                      pagePath="/gallery"
                      pageTitle="Gallery"
                    />
                  } />
                  <Route path="/areas-we-serve" element={
                    <ProtectedPuckEditor
                      pagePath="/areas-we-serve"
                      pageTitle="Areas We Serve"
                    />
                  } />
                  <Route path="/financing" element={
                    <ProtectedPuckEditor
                      pagePath="/financing"
                      pageTitle="Financing"
                    />
                  } />
                </Routes>
              </ProtectedRoute>
            } />
          </Route>

          <Route path="*" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <Routes>
                  <Route path="/about" element={<About />} />
                  <Route path="/contact" element={<ContactPage />} />
                  <Route path="/financing" element={<Financing />} />
                  <Route path="/residential-roofing" element={<ResidentialRoofing />} />
                  <Route path="/commercial-roofing" element={<CommercialRoofing />} />
                  <Route path="/roof-repair" element={<RoofRepair />} />
                  <Route path="/tpo-roofing" element={<TPORoofing />} />
                  <Route path="/metal-roofing" element={<MetalRoofing />} />
                  <Route path="/shingle-roofing" element={<ShingleRoofing />} />
                  <Route path="/emergency-roofing" element={<EmergencyRoofing />} />
                  <Route path="/gallery" element={<Gallery />} />
                  <Route path="/areas-we-serve" element={<AreasWeServe />} />
                  <Route path="/roofing-education" element={<RoofingEducation />} />
                  
                  {/* Form Routes */}
                  <Route path="/request-consultation" element={<RequestConsultation />} />
                  <Route path="/request-estimate" element={<RequestEstimate />} />
                  <Route path="/request-emergency-repair" element={<RequestEmergencyRepair />} />
                  
                  {/* Success Pages */}
                  <Route path="/consultation-success" element={<ConsultationSuccess />} />
                  <Route path="/estimate-success" element={<EstimateSuccess />} />
                  <Route path="/emergency-success" element={<EmergencySuccess />} />
                  <Route path="/financing-success" element={<FinancingSuccess />} />

                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
              <Footer />
            </div>
          } />
          </Routes>
          <Toaster />
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </React.StrictMode>
);

export default App;
