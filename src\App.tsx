import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Toaster } from "./components/ui/toaster";
import ScrollToTop from './components/ScrollToTop';
import { TooltipProvider } from "./components/ui/tooltip";
import Navbar from "./components/layout/Navbar";
import Footer from "./components/layout/Footer";
import type { FC } from 'react';

// Pages
import HomePage from "./pages/Home";
import PuckHome from "./pages/PuckHome"; // Add Puck version
import About from "./pages/About";
import ContactPage from "./pages/Contact";
import Financing from "./pages/Financing";
import ResidentialRoofing from "./pages/ResidentialRoofing";
import CommercialRoofing from "./pages/CommercialRoofing";
import RoofRepair from "./pages/RoofRepair";
import Gallery from "./pages/Gallery";
import AreasWeServe from "./pages/AreasWeServe";
import NotFound from "./pages/NotFound";
import TPORoofing from "./pages/TPORoofing";
import MetalRoofing from "./pages/MetalRoofing";
import ShingleRoofing from "./pages/ShingleRoofing";
import EmergencyRoofing from "./pages/EmergencyRoofing";
import RoofingEducation from "./pages/RoofingEducation";
import RequestConsultation from "./pages/RequestConsultation";
import RequestEstimate from "./pages/RequestEstimate";
import RequestEmergencyRepair from "./pages/RequestEmergencyRepair";
import ConsultationSuccess from "./pages/ConsultationSuccess";
import EstimateSuccess from "./pages/EstimateSuccess";
import EmergencySuccess from "./pages/EmergencySuccess";
import FinancingSuccess from "./pages/FinancingSuccess";

const App: FC = () => (
  <React.StrictMode>
    <TooltipProvider>
      <BrowserRouter>
        <ScrollToTop />
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <HomePage />
              </main>
              <Footer />
            </div>
          } />
          
          {/* Puck Test Route */}
          <Route path="/puck" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <PuckHome />
              </main>
              <Footer />
            </div>
          } />

          <Route path="*" element={
            <div className="flex flex-col min-h-screen">
              <Navbar />
              <main className="flex-grow pt-[120px]">
                <Routes>
                  <Route path="/about" element={<About />} />
                  <Route path="/contact" element={<ContactPage />} />
                  <Route path="/financing" element={<Financing />} />
                  <Route path="/residential-roofing" element={<ResidentialRoofing />} />
                  <Route path="/commercial-roofing" element={<CommercialRoofing />} />
                  <Route path="/roof-repair" element={<RoofRepair />} />
                  <Route path="/tpo-roofing" element={<TPORoofing />} />
                  <Route path="/metal-roofing" element={<MetalRoofing />} />
                  <Route path="/shingle-roofing" element={<ShingleRoofing />} />
                  <Route path="/emergency-roofing" element={<EmergencyRoofing />} />
                  <Route path="/gallery" element={<Gallery />} />
                  <Route path="/areas-we-serve" element={<AreasWeServe />} />
                  <Route path="/roofing-education" element={<RoofingEducation />} />
                  
                  {/* Form Routes */}
                  <Route path="/request-consultation" element={<RequestConsultation />} />
                  <Route path="/request-estimate" element={<RequestEstimate />} />
                  <Route path="/request-emergency-repair" element={<RequestEmergencyRepair />} />
                  
                  {/* Success Pages */}
                  <Route path="/consultation-success" element={<ConsultationSuccess />} />
                  <Route path="/estimate-success" element={<EstimateSuccess />} />
                  <Route path="/emergency-success" element={<EmergencySuccess />} />
                  <Route path="/financing-success" element={<FinancingSuccess />} />

                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
              <Footer />
            </div>
          } />
        </Routes>
        <Toaster />
      </BrowserRouter>
    </TooltipProvider>
  </React.StrictMode>
);

export default App;
