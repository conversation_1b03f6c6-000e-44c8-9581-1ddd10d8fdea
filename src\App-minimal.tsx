import React from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "./components/ui/toaster";
import ScrollToTop from './components/ScrollToTop';
import { TooltipProvider } from "./components/ui/tooltip";
import Navbar from "./components/layout/Navbar";
import Footer from "./components/layout/Footer";
import { AuthProvider } from "./hooks/useAuth";
import { ThemeProvider } from "./contexts/ThemeContext";
import type { FC } from 'react';

// Core Pages (known to work)
import HomePage from "./pages/Home";
import About from "./pages/About";
import ContactPage from "./pages/Contact";
import Financing from "./pages/Financing";
import ResidentialRoofing from "./pages/ResidentialRoofing";
import CommercialRoofing from "./pages/CommercialRoofing";
import RoofRepair from "./pages/RoofRepair";
import Gallery from "./pages/Gallery";
import AreasWeServe from "./pages/AreasWeServe";
import NotFound from "./pages/NotFound";
import TPORoofing from "./pages/TPORoofing";
import MetalRoofing from "./pages/MetalRoofing";
import ShingleRoofing from "./pages/ShingleRoofing";
import EmergencyRoofing from "./pages/EmergencyRoofing";
import RoofingEducation from "./pages/RoofingEducation";

// Layout wrapper for public pages
const PublicLayout: FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="flex flex-col min-h-screen">
    <Navbar />
    <main className="flex-grow pt-[120px]">
      {children}
    </main>
    <Footer />
  </div>
);

const App: FC = () => (
  <React.StrictMode>
    <ThemeProvider>
      <TooltipProvider>
        <AuthProvider>
          <BrowserRouter>
        <ScrollToTop />
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={
            <PublicLayout>
              <HomePage />
            </PublicLayout>
          } />
          
          <Route path="/about" element={
            <PublicLayout>
              <About />
            </PublicLayout>
          } />
          
          <Route path="/contact" element={
            <PublicLayout>
              <ContactPage />
            </PublicLayout>
          } />
          
          <Route path="/financing" element={
            <PublicLayout>
              <Financing />
            </PublicLayout>
          } />
          
          <Route path="/residential-roofing" element={
            <PublicLayout>
              <ResidentialRoofing />
            </PublicLayout>
          } />
          
          <Route path="/commercial-roofing" element={
            <PublicLayout>
              <CommercialRoofing />
            </PublicLayout>
          } />
          
          <Route path="/roof-repair" element={
            <PublicLayout>
              <RoofRepair />
            </PublicLayout>
          } />
          
          <Route path="/tpo-roofing" element={
            <PublicLayout>
              <TPORoofing />
            </PublicLayout>
          } />
          
          <Route path="/metal-roofing" element={
            <PublicLayout>
              <MetalRoofing />
            </PublicLayout>
          } />
          
          <Route path="/shingle-roofing" element={
            <PublicLayout>
              <ShingleRoofing />
            </PublicLayout>
          } />
          
          <Route path="/emergency-roofing" element={
            <PublicLayout>
              <EmergencyRoofing />
            </PublicLayout>
          } />
          
          <Route path="/gallery" element={
            <PublicLayout>
              <Gallery />
            </PublicLayout>
          } />
          
          <Route path="/areas-we-serve" element={
            <PublicLayout>
              <AreasWeServe />
            </PublicLayout>
          } />
          
          <Route path="/roofing-education" element={
            <PublicLayout>
              <RoofingEducation />
            </PublicLayout>
          } />

          {/* 404 Route */}
          <Route path="*" element={
            <PublicLayout>
              <NotFound />
            </PublicLayout>
          } />
        </Routes>
        <Toaster />
      </BrowserRouter>
    </TooltipProvider>
  </React.StrictMode>
);

export default App;
