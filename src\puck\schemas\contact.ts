import React, { useState } from 'react';
import { type Config } from '@measured/puck';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';
import { sharedFields, defaultVerticalPadding } from './shared';
import { useNavigate } from 'react-router-dom';
import { useToast } from "@/components/ui/use-toast";
import { formSettings } from '@/lib/image-paths';
import { api } from '@/lib/api';

interface FormLabels {
  nameLabel?: string;
  emailLabel?: string;
  phoneLabel?: string;
  messageLabel?: string;
  serviceLabel?: string;
  submitButtonText?: string;
  servicePlaceholder?: string;
}

interface FormProps extends FormLabels {
  title?: string;
  serviceOptions?: Array<{
    value: string;
    label: string;
  }>;
  verticalPadding?: string;
}

interface ContactInfoProps {
  title?: string;
  address?: string;
  phone?: string;
  email?: string;
  businessHoursWeekday?: string;
  businessHoursSaturday?: string;
  businessHoursSunday?: string;
  verticalPadding?: string;
}

interface EmergencyProps {
  title?: string;
  description?: string;
  phone?: string;
  verticalPadding?: string;
}

const ContactFormComponent = ({
  title = 'Request Your Free Quote',
  nameLabel = 'Name',
  emailLabel = 'Email',
  phoneLabel = 'Phone',
  messageLabel = 'Additional Details',
  serviceLabel = 'Service Needed',
  submitButtonText = 'Give me a quote',
  servicePlaceholder = 'Select a service',
  serviceOptions = [
    { value: 'residential', label: 'Residential Roofing' },
    { value: 'commercial', label: 'Commercial Roofing' },
    { value: 'repair', label: 'Roof Repair' },
    { value: 'inspection', label: 'Roof Inspection' }
  ]
}: FormProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    service: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[\d\s\-\(\)\+]{10,}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (!formData.service) {
      newErrors.service = 'Please select a service';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Please provide additional details';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Please provide more details (at least 10 characters)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form and try again.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await api.forms.submitContact({
        ...formData,
        recipient: formSettings.contact.recipient,
        subject: formSettings.contact.subject,
        submittedAt: new Date().toISOString()
      });

      if (response.success) {
        toast({
          title: "Quote Request Sent",
          description: "We'll get back to you with a quote as soon as possible!",
        });

        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          service: ''
        });
        setErrors({});

        // Navigate to success page or education page
        navigate('/roofing-education');
      } else {
        throw new Error('Form submission failed');
      }

    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Error",
        description: "There was a problem sending your request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return React.createElement('div', {
    className: 'bg-white p-8 rounded-lg shadow-lg',
    children: [
      React.createElement('h2', {
        key: 'title',
        className: 'text-2xl font-semibold mb-6'
      }, title),
      React.createElement('form', {
        key: 'form',
        onSubmit: handleSubmit,
        className: 'space-y-6',
        children: [
          React.createElement('div', {
            key: 'name-field',
            children: [
              React.createElement('label', {
                key: 'name-label',
                htmlFor: 'name',
                className: 'block text-sm font-medium text-gray-700'
              }, nameLabel),
              React.createElement('input', {
                key: 'name-input',
                type: 'text',
                id: 'name',
                name: 'name',
                value: formData.name,
                onChange: handleChange,
                className: `mt-1 block w-full rounded-md shadow-sm focus:border-primary focus:ring-primary ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`,
                required: true
              }),
              errors.name && React.createElement('p', {
                key: 'name-error',
                className: 'mt-1 text-sm text-red-600'
              }, errors.name)
            ]
          }),
          React.createElement('div', {
            key: 'email-field',
            children: [
              React.createElement('label', {
                key: 'email-label',
                htmlFor: 'email',
                className: 'block text-sm font-medium text-gray-700'
              }, emailLabel),
              React.createElement('input', {
                key: 'email-input',
                type: 'email',
                id: 'email',
                name: 'email',
                value: formData.email,
                onChange: handleChange,
                className: `mt-1 block w-full rounded-md shadow-sm focus:border-primary focus:ring-primary ${
                  errors.email ? 'border-red-300' : 'border-gray-300'
                }`,
                required: true
              }),
              errors.email && React.createElement('p', {
                key: 'email-error',
                className: 'mt-1 text-sm text-red-600'
              }, errors.email)
            ]
          }),
          React.createElement('div', {
            key: 'phone-field',
            children: [
              React.createElement('label', {
                key: 'phone-label',
                htmlFor: 'phone',
                className: 'block text-sm font-medium text-gray-700'
              }, phoneLabel),
              React.createElement('input', {
                key: 'phone-input',
                type: 'tel',
                id: 'phone',
                name: 'phone',
                value: formData.phone,
                onChange: handleChange,
                className: `mt-1 block w-full rounded-md shadow-sm focus:border-primary focus:ring-primary ${
                  errors.phone ? 'border-red-300' : 'border-gray-300'
                }`,
                required: true
              }),
              errors.phone && React.createElement('p', {
                key: 'phone-error',
                className: 'mt-1 text-sm text-red-600'
              }, errors.phone)
            ]
          }),
          React.createElement('div', {
            key: 'service-field',
            children: [
              React.createElement('label', {
                key: 'service-label',
                htmlFor: 'service',
                className: 'block text-sm font-medium text-gray-700'
              }, serviceLabel),
              React.createElement('select', {
                key: 'service-select',
                id: 'service',
                name: 'service',
                value: formData.service,
                onChange: handleChange,
                className: `mt-1 block w-full rounded-md shadow-sm focus:border-primary focus:ring-primary ${
                  errors.service ? 'border-red-300' : 'border-gray-300'
                }`,
                children: [
                  React.createElement('option', {
                    key: 'placeholder',
                    value: ''
                  }, servicePlaceholder),
                  ...serviceOptions.map(option =>
                    React.createElement('option', {
                      key: option.value,
                      value: option.value
                    }, option.label)
                  )
                ]
              }),
              errors.service && React.createElement('p', {
                key: 'service-error',
                className: 'mt-1 text-sm text-red-600'
              }, errors.service)
            ]
          }),
          React.createElement('div', {
            key: 'message-field',
            children: [
              React.createElement('label', {
                key: 'message-label',
                htmlFor: 'message',
                className: 'block text-sm font-medium text-gray-700'
              }, messageLabel),
              React.createElement('textarea', {
                key: 'message-input',
                id: 'message',
                name: 'message',
                value: formData.message,
                onChange: handleChange,
                rows: 4,
                className: `mt-1 block w-full rounded-md shadow-sm focus:border-primary focus:ring-primary ${
                  errors.message ? 'border-red-300' : 'border-gray-300'
                }`,
                required: true
              }),
              errors.message && React.createElement('p', {
                key: 'message-error',
                className: 'mt-1 text-sm text-red-600'
              }, errors.message)
            ]
          }),
          React.createElement('button', {
            key: 'submit',
            type: 'submit',
            disabled: isSubmitting,
            className: `w-full btn-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`
          }, isSubmitting ? 'Sending...' : submitButtonText)
        ]
      })
    ]
  });
};

export const contactConfig: Config = {
  components: {
    RequestForm: {
      render: (props: FormProps) => React.createElement(ContactFormComponent, props),
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        nameLabel: { type: 'text' },
        emailLabel: { type: 'text' },
        phoneLabel: { type: 'text' },
        messageLabel: { type: 'text' },
        serviceLabel: { type: 'text' },
        submitButtonText: { type: 'text' },
        servicePlaceholder: { type: 'text' },
        serviceOptions: {
          type: 'array',
          arrayFields: {
            value: { type: 'text' },
            label: { type: 'text' }
          }
        }
      }
    },

    ContactInfo: {
      render: ({
        title = 'Contact Information',
        address = '123 Main St, Miami, FL 33101',
        phone = '(*************',
        email = '<EMAIL>',
        businessHoursWeekday = '8:00 AM - 6:00 PM',
        businessHoursSaturday = '9:00 AM - 3:00 PM',
        businessHoursSunday = 'Closed',
        verticalPadding = defaultVerticalPadding
      }: ContactInfoProps) => {
        return React.createElement('div', {
          className: 'bg-white p-8 rounded-lg shadow-lg',
          children: [
            React.createElement('h2', {
              key: 'title',
              className: 'text-2xl font-semibold mb-6'
            }, title),
            React.createElement('div', {
              key: 'info',
              className: 'space-y-4',
              children: [
                React.createElement('div', {
                  key: 'address',
                  className: 'flex items-start space-x-4',
                  children: [
                    React.createElement(MapPin, {
                      key: 'icon',
                      className: 'w-6 h-6 text-primary mt-1'
                    }),
                    React.createElement('div', {
                      key: 'content',
                      children: [
                        React.createElement('h3', {
                          key: 'label',
                          className: 'font-medium'
                        }, 'Address'),
                        React.createElement('p', {
                          key: 'value',
                          className: 'text-gray-600'
                        }, address)
                      ]
                    })
                  ]
                }),
                React.createElement('div', {
                  key: 'phone',
                  className: 'flex items-start space-x-4',
                  children: [
                    React.createElement(Phone, {
                      key: 'icon',
                      className: 'w-6 h-6 text-primary mt-1'
                    }),
                    React.createElement('div', {
                      key: 'content',
                      children: [
                        React.createElement('h3', {
                          key: 'label',
                          className: 'font-medium'
                        }, 'Phone'),
                        React.createElement('p', {
                          key: 'value',
                          className: 'text-gray-600'
                        }, [
                          React.createElement('a', {
                            key: 'link',
                            href: `tel:${phone}`,
                            className: 'hover:text-primary'
                          }, phone)
                        ])
                      ]
                    })
                  ]
                }),
                React.createElement('div', {
                  key: 'email',
                  className: 'flex items-start space-x-4',
                  children: [
                    React.createElement(Mail, {
                      key: 'icon',
                      className: 'w-6 h-6 text-primary mt-1'
                    }),
                    React.createElement('div', {
                      key: 'content',
                      children: [
                        React.createElement('h3', {
                          key: 'label',
                          className: 'font-medium'
                        }, 'Email'),
                        React.createElement('p', {
                          key: 'value',
                          className: 'text-gray-600'
                        }, [
                          React.createElement('a', {
                            key: 'link',
                            href: `mailto:${email}`,
                            className: 'hover:text-primary'
                          }, email)
                        ])
                      ]
                    })
                  ]
                }),
                React.createElement('div', {
                  key: 'hours',
                  className: 'flex items-start space-x-4',
                  children: [
                    React.createElement(Clock, {
                      key: 'icon',
                      className: 'w-6 h-6 text-primary mt-1'
                    }),
                    React.createElement('div', {
                      key: 'content',
                      children: [
                        React.createElement('h3', {
                          key: 'label',
                          className: 'font-medium'
                        }, 'Business Hours'),
                        React.createElement('p', {
                          key: 'value',
                          className: 'text-gray-600'
                        }, [
                          `Monday - Friday: ${businessHoursWeekday}`,
                          React.createElement('br', { key: 'br1' }),
                          `Saturday: ${businessHoursSaturday}`,
                          React.createElement('br', { key: 'br2' }),
                          `Sunday: ${businessHoursSunday}`
                        ])
                      ]
                    })
                  ]
                })
              ]
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        address: { type: 'text' },
        phone: { type: 'text' },
        email: { type: 'text' },
        businessHoursWeekday: { type: 'text' },
        businessHoursSaturday: { type: 'text' },
        businessHoursSunday: { type: 'text' }
      }
    },

    EmergencyService: {
      render: ({
        title = 'Emergency Service',
        description = '24/7 emergency roofing services available for urgent situations. Call our emergency hotline:',
        phone = '(*************',
        verticalPadding = defaultVerticalPadding
      }: EmergencyProps) => {
        return React.createElement('div', {
          className: 'bg-white p-8 rounded-lg shadow-lg',
          children: [
            React.createElement('h2', {
              key: 'title',
              className: 'text-2xl font-semibold mb-6'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-gray-600 mb-4'
            }, description),
            React.createElement('a', {
              key: 'phone',
              href: `tel:${phone}`,
              className: 'btn-primary block text-center'
            }, `Emergency Service: ${phone}`)
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        phone: { type: 'text' }
      }
    }
  }
};