import React from 'react';
import { type Config } from '@measured/puck';
import { Link } from 'react-router-dom';
import { Star, Search } from 'lucide-react';
import { sharedFields, defaultVerticalPadding } from './shared';

interface HeroProps {
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  verticalPadding?: string;
}

interface ProjectProps {
  image: string;
  title: string;
  description: string;
  category: string;
}

interface ProjectsProps {
  title?: string;
  subtitle?: string;
  projects?: ProjectProps[];
  verticalPadding?: string;
}

interface ReviewProps {
  name: string;
  rating: number;
  text: string;
  image: string;
  project: string;
}

interface ReviewsProps {
  title?: string;
  subtitle?: string;
  reviews?: ReviewProps[];
  verticalPadding?: string;
}

interface CTAProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  verticalPadding?: string;
}

export const galleryConfig: Config = {
  components: {
    GalleryHero: {
      render: ({
        title = 'Our Project Gallery',
        subtitle = 'Browse through our portfolio of successful roofing projects across Florida',
        backgroundImage = '/placeholder.svg',
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative bg-primary text-white ${verticalPadding}`,
          children: [
            React.createElement('div', {
              key: 'background',
              className: 'absolute inset-0 bg-cover bg-center opacity-20',
              style: { backgroundImage: `url("${backgroundImage}")` }
            }),
            React.createElement('div', {
              key: 'content',
              className: 'container mx-auto px-4 relative text-center',
              children: [
                React.createElement('h1', {
                  key: 'title',
                  className: 'text-5xl font-bold mb-6'
                }, title),
                React.createElement('p', {
                  key: 'subtitle',
                  className: 'text-xl max-w-2xl mx-auto'
                }, subtitle)
              ]
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        backgroundImage: { type: 'text' }
      }
    },

    ProjectsGrid: {
      render: ({
        title = 'Featured Projects',
        subtitle = 'Browse through our portfolio of successful roofing projects',
        projects = [],
        verticalPadding = defaultVerticalPadding
      }: ProjectsProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`,
          children: [
            React.createElement('div', {
              key: 'header',
              className: 'container mx-auto px-4 text-center mb-12',
              children: [
                React.createElement('h2', {
                  key: 'title',
                  className: 'text-3xl font-bold mb-4'
                }, title),
                React.createElement('p', {
                  key: 'subtitle',
                  className: 'text-gray-600'
                }, subtitle)
              ]
            }),
            React.createElement('div', {
              key: 'grid',
              className: 'container mx-auto px-4',
              children: React.createElement('div', {
                className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8',
                children: projects.map((project, index) => 
                  React.createElement('div', {
                    key: index,
                    className: 'rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:scale-105 bg-white',
                    children: [
                      React.createElement('div', {
                        key: 'image',
                        className: 'relative h-64',
                        children: React.createElement('img', {
                          src: project.image,
                          alt: project.title,
                          className: 'w-full h-full object-cover'
                        })
                      }),
                      React.createElement('div', {
                        key: 'content',
                        className: 'p-6',
                        children: [
                          React.createElement('span', {
                            key: 'category',
                            className: 'inline-block px-3 py-1 text-sm text-primary bg-primary/10 rounded-full mb-3'
                          }, project.category),
                          React.createElement('h3', {
                            key: 'title',
                            className: 'font-semibold text-xl mb-2'
                          }, project.title),
                          React.createElement('p', {
                            key: 'description',
                            className: 'text-gray-600'
                          }, project.description)
                        ]
                      })
                    ]
                  })
                )
              })
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        projects: {
          type: 'array',
          arrayFields: {
            image: { type: 'text' },
            title: { type: 'text' },
            description: { type: 'textarea' },
            category: { type: 'text' }
          }
        }
      }
    },

    ReviewsGrid: {
      render: ({
        title = 'Customer Reviews',
        subtitle = 'What our satisfied customers say about Roofers LLC services',
        reviews = [],
        verticalPadding = defaultVerticalPadding
      }: ReviewsProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`,
          children: [
            React.createElement('div', {
              key: 'header',
              className: 'container mx-auto px-4 text-center mb-12',
              children: [
                React.createElement('h2', {
                  key: 'title',
                  className: 'text-3xl font-bold mb-4'
                }, title),
                React.createElement('p', {
                  key: 'subtitle',
                  className: 'text-gray-600'
                }, subtitle)
              ]
            }),
            React.createElement('div', {
              key: 'grid',
              className: 'container mx-auto px-4',
              children: React.createElement('div', {
                className: 'grid grid-cols-1 md:grid-cols-2 gap-8',
                children: reviews.map((review, index) => 
                  React.createElement('div', {
                    key: index,
                    className: 'bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow',
                    children: [
                      React.createElement('div', {
                        key: 'header',
                        className: 'flex items-start gap-4 mb-4',
                        children: [
                          React.createElement('img', {
                            key: 'image',
                            src: review.image,
                            alt: review.name,
                            className: 'w-20 h-20 rounded-full object-cover'
                          }),
                          React.createElement('div', {
                            key: 'info',
                            className: 'flex-1',
                            children: [
                              React.createElement('h3', {
                                key: 'name',
                                className: 'font-semibold text-lg'
                              }, review.name),
                              React.createElement('p', {
                                key: 'project',
                                className: 'text-sm text-gray-500'
                              }, review.project),
                              React.createElement('div', {
                                key: 'stars',
                                className: 'flex text-yellow-400 mt-1',
                                children: Array(review.rating).fill(0).map((_, i) => 
                                  React.createElement(Star, {
                                    key: i,
                                    className: 'w-5 h-5 fill-current'
                                  })
                                )
                              })
                            ]
                          })
                        ]
                      }),
                      React.createElement('p', {
                        key: 'text',
                        className: 'text-gray-600 italic'
                      }, review.text)
                    ]
                  })
                )
              })
            })
          ]
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        subtitle: { type: 'textarea' },
        reviews: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            rating: { type: 'number' },
            text: { type: 'textarea' },
            image: { type: 'text' },
            project: { type: 'text' }
          }
        }
      }
    },

    GalleryCTA: {
      render: ({
        title = 'Ready to Start Your Roofing Project?',
        description = 'Contact Roofers LLC today for a free consultation and estimate',
        buttonText = 'Get Free Estimate',
        buttonLink = '/request-estimate',
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `bg-primary text-white ${verticalPadding}`,
          children: React.createElement('div', {
            className: 'container mx-auto px-4 text-center',
            children: [
              React.createElement('h2', {
                key: 'title',
                className: 'text-3xl font-bold mb-4'
              }, title),
              React.createElement('p', {
                key: 'description',
                className: 'text-xl mb-8 max-w-2xl mx-auto'
              }, description),
              React.createElement(Link, {
                key: 'button',
                to: buttonLink,
                className: 'bg-accent px-8 py-3 rounded-lg font-semibold hover:bg-accent/90 transition-colors inline-block'
              }, buttonText)
            ]
          })
        });
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'textarea' },
        buttonText: { type: 'text' },
        buttonLink: { type: 'text' }
      }
    }
  }
};