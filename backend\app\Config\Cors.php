<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Cors extends BaseConfig
{
    /**
     * --------------------------------------------------------------------------
     * CORS Allowed Origins
     * --------------------------------------------------------------------------
     *
     * Array of allowed origins for CORS requests, processed from CORS_ORIGIN env
     * Multiple origins can be specified using comma separation
     * Example env value: http://localhost:8080,https://roofers.llc
     *
     * @var array
     */
    public array $allowedOrigins = [];

    /**
     * --------------------------------------------------------------------------
     * CORS Exposed Headers
     * --------------------------------------------------------------------------
     *
     * Headers that are allowed to be exposed to the web server
     *
     * @var array
     */
    public array $exposedHeaders = [];

    /**
     * --------------------------------------------------------------------------
     * Constructor
     * --------------------------------------------------------------------------
     *
     * Processes the CORS_ORIGIN environment variable to set allowed origins
     */
    public function __construct()
    {
        parent::__construct();

        // Get CORS origins from environment variable
        $corsOrigin = $_ENV['CORS_ORIGIN'] ?? '';
        
        if (!empty($corsOrigin)) {
            // Split multiple origins and trim whitespace
            $origins = array_map('trim', explode(',', $corsOrigin));
            
            // Filter out empty values and set allowed origins
            $this->allowedOrigins = array_filter($origins);
            
            // If no valid origins found, set development default
            if (empty($this->allowedOrigins) && ENVIRONMENT === 'development') {
                $this->allowedOrigins = ['http://localhost:8080'];
            }
        }
        
        // Log CORS configuration in development
        if (ENVIRONMENT === 'development') {
            log_message('debug', 'CORS Allowed Origins: ' . implode(', ', $this->allowedOrigins));
        }
    }
}
