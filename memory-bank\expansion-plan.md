# Website Expansion Plan

## Database Integration

### Database Credentials
```
Database Name: roofers01
Username: roofers01
Password: RGZ6VrIFyZlRxi
Host: localhost
```

### Existing Database Structure
The database already contains several tables from another application:
1. `_prisma_migrations` - Prisma migration history
2. `estimates` - Estimate records
3. `invoices` - Invoice records
4. `leads` - Lead management
5. `pages` - Content pages
6. `settings` - System settings
7. `users` - User management

### Our Tables (with 'edit_' prefix)
1. `edit_customers` - Our customer management
```sql
edit_customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

2. `edit_form_submissions` - Our form submissions
```sql
edit_form_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_id INT NOT NULL,
    form_type ENUM('estimate', 'emergency', 'financing') NOT NULL,
    project_details TEXT NOT NULL,
    status ENUM('new', 'contacted', 'in_progress', 'completed') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES edit_customers(id)
);
```

### Integration Strategy
1. Keep our tables prefixed with 'edit_' to avoid conflicts
2. Consider future integration with existing tables:
   - Map our `edit_customers` to existing `leads` table
   - Link our `edit_form_submissions` to existing `estimates`
   - Use existing `users` table for admin access

## Service Pages Development

```mermaid
graph LR
    A[Service Pages] --> B[Residential]
    A --> C[Commercial]
    A --> D[Emergency]
    
    subgraph "Page Components"
        B --> E[Hero]
        B --> F[Features]
        B --> G[Gallery]
        B --> H[CTA]
    end
```

### Implementation Phases

1. Phase 1: Service Pages
- Create service page schemas in Puck
- Implement shared components
- Set up routing

2. Phase 2: Form Integration
- Connect forms to database
- Implement form validation
- Set up success/error handling

3. Phase 3: Admin Features
- Create form submission dashboard
- Implement customer management
- Add status tracking

### Service Pages Structure

Each service page will use Puck schemas with:
1. Hero Section
   - Title
   - Description
   - Background image
   - CTA buttons

2. Features Section
   - Service highlights
   - Benefits
   - Process steps

3. Gallery Section
   - Project images
   - Before/after
   - Testimonials

4. Contact Section
   - Relevant form
   - Contact information
   - Emergency notice (if applicable)

## Timeline

### Week 1: Service Pages
- Day 1-2: Residential page
- Day 3-4: Commercial page
- Day 5: Emergency page

### Week 2: Form Integration
- Day 1-2: Database connections
- Day 3-4: Form submissions
- Day 5: Validation and error handling

### Week 3: Admin & Polish
- Day 1-2: Admin dashboard
- Day 3: Form enhancements
- Day 4-5: Testing and refinement

## Success Metrics
- All service pages implemented with Puck
- Forms storing data in properly prefixed tables
- Admin can view and manage submissions
- Response time < 500ms for database operations
- Zero data loss in form submissions
- Clean integration with existing database structure

## Next Steps
1. Implement service pages
2. Create form submission endpoints
3. Build admin interface
4. Add data validation
5. Implement status tracking