import React, { useState } from 'react';
import { Puck, Render, type Data } from '@measured/puck';
import { emergencyConfig } from '../puck/schemas/emergency';
import '@measured/puck/dist/index.css';

interface ComponentData {
  type: string;
  props: Record<string, unknown>;
}

interface EmergencyData extends Data {
  root: {
    title: string;
    description: string;
  };
  content: ComponentData[];
}

export const emergencyDefaultData: EmergencyData = {
  root: {
    title: '24/7 Emergency Roof Repair',
    description: 'Expert emergency roofing services in Florida'
  },
  content: [
    {
      type: 'EmergencyHero',
      props: {
        title: '24/7 Emergency Roof Repair',
        subtitle: 'Immediate response to protect your property from roof emergencies',
        emergencyButtonText: 'Request Emergency Service',
        emergencyButtonLink: '/request-emergency-repair',
        phoneNumber: '(*************',
        backgroundImage: '/placeholder.svg'
      }
    },
    {
      type: 'EmergencyServices',
      props: {
        title: 'Emergency Roofing Services',
        services: [
          {
            title: "Storm Damage",
            description: "Immediate response to storm-related roof damage",
            icon: "⛈️"
          },
          {
            title: "Leak Repair",
            description: "Fast repair of serious roof leaks",
            icon: "💧"
          },
          {
            title: "Tree Damage",
            description: "Emergency repairs for tree-related damage",
            icon: "🌳"
          },
          {
            title: "24/7 Service",
            description: "Round-the-clock emergency response",
            icon: "🔧"
          }
        ]
      }
    },
    {
      type: 'WhyChooseUs',
      props: {
        title: 'Why Choose Our Emergency Service',
        items: [
          {
            name: "Fast Response",
            description: "Available 24/7 for emergency roof repairs",
            points: [
              { text: "Immediate dispatch" },
              { text: "Quick arrival time" },
              { text: "Rapid assessment" },
              { text: "Swift action plan" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Expert Team",
            description: "Skilled emergency roofing specialists",
            points: [
              { text: "Licensed professionals" },
              { text: "Years of experience" },
              { text: "Emergency trained" },
              { text: "Safety certified" }
            ],
            image: "/placeholder.svg"
          },
          {
            name: "Complete Service",
            description: "Comprehensive emergency roofing solutions",
            points: [
              { text: "Temporary repairs" },
              { text: "Permanent solutions" },
              { text: "Insurance assistance" },
              { text: "Follow-up care" }
            ],
            image: "/placeholder.svg"
          }
        ]
      }
    },
    {
      type: 'EmergencyCTA',
      props: {
        title: 'Need a Long-term Solution?',
        description: 'Get a comprehensive estimate for permanent roofing solutions.',
        buttonText: 'Get Free Estimate',
        buttonLink: '/request-estimate'
      }
    }
  ]
};

const EmergencyRoofing: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<EmergencyData>(emergencyDefaultData);

  // Only show edit button if in development
  const showEditButton = process.env.NODE_ENV === 'development';

  if (isEditing) {
    return (
      <div style={{ 
        position: 'fixed',
        inset: 0,
        zIndex: 50,
        height: '100vh',
        width: '100vw'
      }}>
        <Puck
          config={emergencyConfig}
          data={data}
          onPublish={async (newData) => {
            setData(newData as EmergencyData);
            setIsEditing(false);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Render config={emergencyConfig} data={data} />
      {showEditButton && (
        <button
          onClick={() => setIsEditing(true)}
          className="fixed bottom-4 right-4 bg-red-600 text-white px-4 py-2 rounded-full shadow-lg hover:shadow-xl transition-shadow z-10"
        >
          Edit Page
        </button>
      )}
    </div>
  );
};

export default EmergencyRoofing;