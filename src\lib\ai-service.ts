import { config } from '@/config/environment';

export interface AIProvider {
  id: string;
  name: string;
  description: string;
  costPerImage: number;
  maxResolution: string;
  features: string[];
}

export interface AIImageRequest {
  prompt: string;
  provider: string;
  style?: string;
  size?: string;
  quality?: string;
  count?: number;
}

export interface AIImageResponse {
  success: boolean;
  images: string[];
  metadata: {
    prompt: string;
    provider: string;
    generatedAt: string;
    cost: number;
  };
  error?: string;
}

export const AI_PROVIDERS: AIProvider[] = [
  {
    id: 'openai-dalle3',
    name: 'OpenAI DALL-E 3',
    description: 'High-quality, photorealistic images with excellent prompt understanding',
    costPerImage: 0.04,
    maxResolution: '1024x1024',
    features: ['Photorealistic', 'High Detail', 'Prompt Adherence']
  },
  {
    id: 'openrouter',
    name: 'OpenRouter (Multiple Models)',
    description: 'Access to various AI models including Midjourney, Stable Diffusion',
    costPerImage: 0.02,
    maxResolution: '1024x1024',
    features: ['Model Variety', 'Cost Effective', 'Fast Generation']
  },
  {
    id: 'huggingface',
    name: 'Hugging Face Diffusers',
    description: 'Free open-source models with good quality',
    costPerImage: 0.00,
    maxResolution: '512x512',
    features: ['Free', 'Open Source', 'Customizable']
  },
  {
    id: 'replicate',
    name: 'Replicate Stable Diffusion',
    description: 'Stable Diffusion models with various styles',
    costPerImage: 0.01,
    maxResolution: '768x768',
    features: ['Style Variety', 'Affordable', 'Reliable']
  }
];

export const ROOFING_PROMPTS = {
  hero: [
    'Professional roofing team installing shingles on modern residential home, blue sky background, high quality photography',
    'Commercial flat roof installation with TPO membrane, industrial building, professional workers',
    'Emergency roof repair during storm, tarp covering, professional roofer working safely',
    'Metal roofing installation on contemporary house, sleek design, architectural photography',
    'Solar panel installation on residential roof, sustainable energy, modern home'
  ],
  services: [
    'Before and after roof replacement, split image showing old damaged roof vs new installation',
    'Professional roof inspection with drone technology, modern equipment, safety focus',
    'Gutter installation and maintenance, clean professional work, residential setting',
    'Roof ventilation system installation, proper airflow, technical detail',
    'Skylight installation in residential roof, natural lighting, interior view'
  ],
  seasonal: [
    'Storm damage assessment, professional roofer examining hail damage, insurance documentation',
    'Winter roof maintenance, snow removal, ice dam prevention, safety equipment',
    'Summer roof maintenance, heat protection, UV resistance, cooling systems',
    'Fall gutter cleaning, leaf removal, seasonal preparation, maintenance focus',
    'Spring roof inspection, post-winter assessment, repair planning'
  ],
  team: [
    'Professional roofing crew at work, safety equipment, teamwork, construction site',
    'Roofing foreman explaining project to homeowner, consultation, professional interaction',
    'Roofing equipment and tools, organized workspace, professional setup',
    'Safety training session, hard hats, harnesses, professional development',
    'Customer satisfaction, completed project, handshake, professional relationship'
  ],
  testimonials: [
    'Happy homeowner with new roof, residential setting, satisfaction, quality work',
    'Beautiful home exterior with new roofing, curb appeal, architectural beauty',
    'Professional certificate or award, quality recognition, industry excellence',
    'Community involvement, local business, neighborhood improvement',
    'Family home protection, security, peace of mind, quality roofing'
  ]
};

class AIImageService {
  private apiKeys: Record<string, string> = {};
  private defaultProvider = 'openai-dalle3';
  private usageTracking: Record<string, number> = {};

  constructor() {
    // Initialize with environment variables or admin settings
    this.loadAPIKeys();
  }

  private loadAPIKeys() {
    // In a real app, these would come from admin settings or environment variables
    this.apiKeys = {
      'openai-dalle3': process.env.OPENAI_API_KEY || '',
      'openrouter': process.env.OPENROUTER_API_KEY || '',
      'huggingface': process.env.HUGGINGFACE_API_KEY || '',
      'replicate': process.env.REPLICATE_API_KEY || ''
    };
  }

  async generateImages(request: AIImageRequest): Promise<AIImageResponse> {
    const provider = AI_PROVIDERS.find(p => p.id === request.provider);
    if (!provider) {
      return {
        success: false,
        images: [],
        metadata: {
          prompt: request.prompt,
          provider: request.provider,
          generatedAt: new Date().toISOString(),
          cost: 0
        },
        error: 'Provider not found'
      };
    }

    try {
      let images: string[] = [];
      
      switch (request.provider) {
        case 'openai-dalle3':
          images = await this.generateWithOpenAI(request);
          break;
        case 'openrouter':
          images = await this.generateWithOpenRouter(request);
          break;
        case 'huggingface':
          images = await this.generateWithHuggingFace(request);
          break;
        case 'replicate':
          images = await this.generateWithReplicate(request);
          break;
        default:
          // Fallback to mock generation for demo
          images = await this.generateMockImages(request);
      }

      const cost = provider.costPerImage * (request.count || 3);
      this.trackUsage(request.provider, cost);

      return {
        success: true,
        images,
        metadata: {
          prompt: request.prompt,
          provider: request.provider,
          generatedAt: new Date().toISOString(),
          cost
        }
      };

    } catch (error) {
      console.error('AI Image generation error:', error);
      return {
        success: false,
        images: [],
        metadata: {
          prompt: request.prompt,
          provider: request.provider,
          generatedAt: new Date().toISOString(),
          cost: 0
        },
        error: error instanceof Error ? error.message : 'Generation failed'
      };
    }
  }

  private async generateWithOpenAI(request: AIImageRequest): Promise<string[]> {
    const apiKey = this.apiKeys['openai-dalle3'];
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'dall-e-3',
        prompt: this.enhancePromptForRoofing(request.prompt),
        n: Math.min(request.count || 3, 1), // DALL-E 3 only supports 1 image at a time
        size: request.size || '1024x1024',
        quality: request.quality || 'standard'
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data.map((item: any) => item.url);
  }

  private async generateWithOpenRouter(request: AIImageRequest): Promise<string[]> {
    // OpenRouter implementation would go here
    // For now, return mock images
    return this.generateMockImages(request);
  }

  private async generateWithHuggingFace(request: AIImageRequest): Promise<string[]> {
    // Hugging Face implementation would go here
    // For now, return mock images
    return this.generateMockImages(request);
  }

  private async generateWithReplicate(request: AIImageRequest): Promise<string[]> {
    // Replicate implementation would go here
    // For now, return mock images
    return this.generateMockImages(request);
  }

  private async generateMockImages(request: AIImageRequest): Promise<string[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Return high-quality roofing images from Unsplash
    const roofingImages = [
      'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=800&h=600&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1590736969955-71cc94901144?w=800&h=600&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop&auto=format'
    ];

    const count = request.count || 3;
    return roofingImages.slice(0, count);
  }

  private enhancePromptForRoofing(prompt: string): string {
    const roofingKeywords = [
      'professional roofing',
      'high quality',
      'architectural photography',
      'construction industry',
      'safety equipment',
      'modern residential',
      'commercial building'
    ];

    // Add roofing context if not already present
    if (!roofingKeywords.some(keyword => prompt.toLowerCase().includes(keyword.toLowerCase()))) {
      return `${prompt}, professional roofing industry, high quality architectural photography, construction safety`;
    }

    return prompt;
  }

  private trackUsage(provider: string, cost: number) {
    this.usageTracking[provider] = (this.usageTracking[provider] || 0) + cost;
  }

  getUsageStats() {
    return this.usageTracking;
  }

  generateSEOMetadata(prompt: string, imageUrl: string) {
    const keywords = this.extractRoofingKeywords(prompt);
    return {
      alt: `Professional roofing ${keywords.join(', ')} - Roofers LLC`,
      title: `${keywords[0]} | Professional Roofing Services | Roofers LLC`,
      description: `High-quality ${keywords.join(' and ')} services by Roofers LLC. Professional roofing contractors serving Florida with expert installation and repair.`,
      keywords: keywords.join(', '),
      schema: this.generateImageSchema(prompt, imageUrl)
    };
  }

  private extractRoofingKeywords(prompt: string): string[] {
    const roofingTerms = [
      'installation', 'repair', 'maintenance', 'inspection', 'replacement',
      'residential', 'commercial', 'emergency', 'shingle', 'metal', 'TPO',
      'flat roof', 'steep roof', 'gutter', 'skylight', 'ventilation'
    ];

    return roofingTerms.filter(term => 
      prompt.toLowerCase().includes(term.toLowerCase())
    ).slice(0, 3);
  }

  private generateImageSchema(prompt: string, imageUrl: string) {
    return {
      "@context": "https://schema.org",
      "@type": "ImageObject",
      "url": imageUrl,
      "description": prompt,
      "creator": {
        "@type": "Organization",
        "name": "Roofers LLC"
      },
      "copyrightHolder": {
        "@type": "Organization",
        "name": "Roofers LLC"
      }
    };
  }
}

export const aiImageService = new AIImageService();
