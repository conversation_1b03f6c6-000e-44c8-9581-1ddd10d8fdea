import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AdminPageHeader from '@/components/AdminPageHeader';
import { seoService, SEOAnalysis, BlogPost } from '@/lib/seo-service';
import {
  Search,
  TrendingUp,
  FileText,
  BarChart3,
  Target,
  Lightbulb,
  Plus,
  Eye,
  Edit,
  Trash2,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

export default function AdminSEO() {
  const [activeTab, setActiveTab] = useState('overview');
  const [seoAnalysis, setSeoAnalysis] = useState<SEOAnalysis | null>(null);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [showBlogGenerator, setShowBlogGenerator] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState('');
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
  const [generatingBlog, setGeneratingBlog] = useState(false);

  useEffect(() => {
    loadSEOData();
  }, []);

  const loadSEOData = async () => {
    setLoading(true);
    try {
      // Mock data loading
      const mockAnalysis = await seoService.analyzeSEO('/', 'mock content');
      setSeoAnalysis(mockAnalysis);
      
      // Load existing blog posts (mock data)
      setBlogPosts([
        {
          id: '1',
          title: 'Essential Roof Maintenance Tips for Florida Homeowners',
          content: 'Comprehensive guide...',
          excerpt: 'Learn essential roof maintenance tips...',
          keywords: ['roof maintenance', 'Florida roofing', 'home maintenance'],
          category: 'maintenance',
          publishedAt: '2024-01-15T10:00:00Z',
          seoScore: 92
        },
        {
          id: '2',
          title: 'Choosing the Right Roofing Material for Your Climate',
          content: 'Material comparison guide...',
          excerpt: 'Compare different roofing materials...',
          keywords: ['roofing materials', 'roof installation', 'material comparison'],
          category: 'materials',
          publishedAt: '2024-01-10T14:30:00Z',
          seoScore: 88
        }
      ]);
    } catch (error) {
      console.error('Error loading SEO data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateBlog = async () => {
    if (!selectedTopic || selectedKeywords.length === 0) {
      alert('Please select a topic and keywords');
      return;
    }

    setGeneratingBlog(true);
    try {
      const newPost = await seoService.generateBlogPost(selectedTopic, selectedKeywords);
      setBlogPosts(prev => [newPost, ...prev]);
      setShowBlogGenerator(false);
      setSelectedTopic('');
      setSelectedKeywords([]);
      alert('Blog post generated successfully!');
    } catch (error) {
      console.error('Error generating blog post:', error);
      alert('Failed to generate blog post');
    } finally {
      setGeneratingBlog(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info': return <Info className="h-4 w-4 text-blue-500" />;
      default: return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', name: 'SEO Overview', icon: BarChart3 },
    { id: 'keywords', name: 'Keywords', icon: Target },
    { id: 'content', name: 'Content Generator', icon: FileText },
    { id: 'analysis', name: 'Page Analysis', icon: Search }
  ];

  if (loading) {
    return (
      <div className="space-y-8">
        <AdminPageHeader
          title="SEO Management"
          description="Optimize your website for search engines"
        />
        <div className="text-center py-8">
          <p className="text-gray-500">Loading SEO data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <AdminPageHeader
        title="SEO Management"
        description="AI-powered SEO optimization and content generation"
      >
        <Button onClick={() => setShowBlogGenerator(true)} className="flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Generate Content</span>
        </Button>
      </AdminPageHeader>

      {/* SEO Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Overall SEO Score</p>
                <p className={`text-2xl font-bold ${getScoreColor(seoAnalysis?.score || 0)}`}>
                  {seoAnalysis?.score || 0}/100
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Tracked Keywords</p>
                <p className="text-2xl font-bold">{seoAnalysis?.keywords.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Blog Posts</p>
                <p className="text-2xl font-bold">{blogPosts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Monthly Visitors</p>
                <p className="text-2xl font-bold">2.4K</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>SEO Issues</CardTitle>
              <CardDescription>Critical issues that need attention</CardDescription>
            </CardHeader>
            <CardContent>
              {seoAnalysis?.issues.length === 0 ? (
                <div className="text-center py-4">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                  <p className="text-gray-500">No SEO issues found!</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {seoAnalysis?.issues.map((issue, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                      {getIssueIcon(issue.type)}
                      <div className="flex-1">
                        <p className="font-medium">{issue.message}</p>
                        {issue.fix && (
                          <p className="text-sm text-gray-600 mt-1">{issue.fix}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Keywords</CardTitle>
              <CardDescription>Your best performing keywords</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {seoAnalysis?.keywords.slice(0, 5).map((keyword, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{keyword.keyword}</p>
                      <p className="text-sm text-gray-600">
                        Position #{keyword.position} • {keyword.searchVolume} searches/month
                      </p>
                    </div>
                    <Badge variant={keyword.competition === 'high' ? 'destructive' : 'secondary'}>
                      {keyword.competition}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'content' && (
        <Card>
          <CardHeader>
            <CardTitle>AI Content Generator</CardTitle>
            <CardDescription>Generate SEO-optimized blog posts and articles</CardDescription>
          </CardHeader>
          <CardContent>
            {blogPosts.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No blog posts yet</p>
                <Button onClick={() => setShowBlogGenerator(true)} className="mt-4">
                  Generate Your First Post
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {blogPosts.map((post) => (
                  <div key={post.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-lg">{post.title}</h3>
                        <p className="text-gray-600 mt-1">{post.excerpt}</p>
                        <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                          <span>Category: {post.category}</span>
                          <span>Published: {new Date(post.publishedAt).toLocaleDateString()}</span>
                          <span className={`font-medium ${getScoreColor(post.seoScore)}`}>
                            SEO Score: {post.seoScore}/100
                          </span>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {post.keywords.map((keyword, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <Button size="sm" variant="ghost">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Blog Generator Modal */}
      {showBlogGenerator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center space-x-2 mb-4">
              <Sparkles className="h-6 w-6 text-purple-600" />
              <h3 className="text-lg font-semibold">Generate SEO-Optimized Blog Post</h3>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Blog Topic</label>
                <select
                  value={selectedTopic}
                  onChange={(e) => setSelectedTopic(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="">Select a topic...</option>
                  {seoService.getBlogTopics('maintenance').map((topic, index) => (
                    <option key={index} value={topic}>{topic}</option>
                  ))}
                  {seoService.getBlogTopics('materials').map((topic, index) => (
                    <option key={index} value={topic}>{topic}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Target Keywords</label>
                <div className="grid grid-cols-2 gap-2">
                  {seoService.getKeywordSuggestions('primary').map((keyword, index) => (
                    <label key={index} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedKeywords.includes(keyword)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedKeywords(prev => [...prev, keyword]);
                          } else {
                            setSelectedKeywords(prev => prev.filter(k => k !== keyword));
                          }
                        }}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="text-sm">{keyword}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">AI Generation Features</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• SEO-optimized title and meta description</li>
                  <li>• Keyword-rich content with natural placement</li>
                  <li>• Proper heading structure (H1, H2, H3)</li>
                  <li>• Internal linking suggestions</li>
                  <li>• Schema markup generation</li>
                </ul>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowBlogGenerator(false);
                  setSelectedTopic('');
                  setSelectedKeywords([]);
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleGenerateBlog}
                disabled={!selectedTopic || selectedKeywords.length === 0 || generatingBlog}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
              >
                {generatingBlog ? (
                  <>
                    <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Post
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
