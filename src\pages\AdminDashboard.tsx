import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { 
  Edit3, 
  Eye, 
  Users, 
  FileText, 
  BarChart3, 
  Settings,
  ExternalLink,
  Zap
} from 'lucide-react';

// Available pages for editing
const availablePages = [
  { id: 'home', name: 'Home Page', path: '/', description: 'Main landing page with hero section and services overview' },
  { id: 'about', name: 'About Us', path: '/about', description: 'Company information and team details' },
  { id: 'contact', name: 'Contact', path: '/contact', description: 'Contact form and business information' },
  { id: 'services-residential', name: 'Residential Services', path: '/services/residential', description: 'Residential roofing services' },
  { id: 'services-commercial', name: 'Commercial Services', path: '/services/commercial', description: 'Commercial roofing services' },
  { id: 'services-emergency', name: 'Emergency Repairs', path: '/services/emergency', description: 'Emergency roofing repair services' },
  { id: 'gallery', name: 'Gallery', path: '/gallery', description: 'Project photos and portfolio' },
  { id: 'areas', name: 'Areas We Serve', path: '/areas-we-serve', description: 'Service area information' },
  { id: 'financing', name: 'Financing', path: '/financing', description: 'Financing options and information' },
];

export default function AdminDashboard() {
  const { user, canEditPages, canManageUsers } = useAuth();
  const [selectedPage, setSelectedPage] = useState('');
  const navigate = useNavigate();

  const handleEditPage = () => {
    if (selectedPage) {
      const page = availablePages.find(p => p.id === selectedPage);
      if (page) {
        // Navigate to the Puck editor for the selected page
        navigate(`/admin/edit${page.path}`);
      }
    }
  };

  const handleViewPage = () => {
    if (selectedPage) {
      const page = availablePages.find(p => p.id === selectedPage);
      if (page) {
        // Open the page in a new tab
        window.open(page.path, '_blank');
      }
    }
  };

  const stats = [
    {
      title: 'Total Pages',
      value: availablePages.length,
      description: 'Editable pages',
      icon: FileText,
      color: 'text-blue-600',
    },
    {
      title: 'Your Role',
      value: user?.roles[0] || 'Unknown',
      description: 'Current access level',
      icon: Users,
      color: 'text-green-600',
    },
    {
      title: 'Edit Access',
      value: canEditPages() ? 'Enabled' : 'Disabled',
      description: 'Page editing permissions',
      icon: Edit3,
      color: canEditPages() ? 'text-green-600' : 'text-red-600',
    },
    {
      title: 'Admin Access',
      value: canManageUsers() ? 'Full' : 'Limited',
      description: 'Administrative permissions',
      icon: Settings,
      color: canManageUsers() ? 'text-purple-600' : 'text-orange-600',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 mt-2">
          Manage your Roofers LLC website content and settings from this dashboard.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                </div>
                <div className={`p-3 rounded-full bg-gray-100 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Page Editor Section */}
      {canEditPages() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Edit3 className="h-5 w-5" />
              <span>Page Editor</span>
            </CardTitle>
            <CardDescription>
              Select a page to edit using the Puck CMS editor. Changes are saved automatically.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <div className="lg:col-span-2">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Select Page to Edit
                </label>
                <Select value={selectedPage} onValueChange={setSelectedPage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a page to edit..." />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePages.map((page) => (
                      <SelectItem key={page.id} value={page.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{page.name}</span>
                          <Badge variant="outline" className="ml-2">
                            {page.path}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {selectedPage && (
                  <p className="text-sm text-gray-600 mt-2">
                    {availablePages.find(p => p.id === selectedPage)?.description}
                  </p>
                )}
              </div>

              <div className="flex flex-col space-y-2">
                <Button
                  onClick={handleEditPage}
                  disabled={!selectedPage}
                  className="flex items-center space-x-2"
                >
                  <Zap className="h-4 w-4" />
                  <span>Edit with Puck</span>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleViewPage}
                  disabled={!selectedPage}
                  className="flex items-center space-x-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span>Preview Page</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Website Preview</span>
            </CardTitle>
            <CardDescription>
              View the live website as visitors see it
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => window.open('/', '_blank')}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Open Website
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Content Management</span>
            </CardTitle>
            <CardDescription>
              Manage business content and information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => navigate('/admin/content')}
            >
              Manage Content
            </Button>
          </CardContent>
        </Card>

        {canManageUsers() && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>User Management</span>
              </CardTitle>
              <CardDescription>
                Manage user accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate('/admin/users')}
              >
                Manage Users
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Recent Activity</span>
          </CardTitle>
          <CardDescription>
            Latest changes and system activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="text-sm font-medium">Admin user logged in</p>
                <p className="text-xs text-gray-500">Just now</p>
              </div>
              <Badge variant="outline">Login</Badge>
            </div>
            <div className="flex items-center justify-between py-2 border-b">
              <div>
                <p className="text-sm font-medium">Authentication system configured</p>
                <p className="text-xs text-gray-500">Today</p>
              </div>
              <Badge variant="outline">System</Badge>
            </div>
            <div className="flex items-center justify-between py-2">
              <div>
                <p className="text-sm font-medium">Database tables created</p>
                <p className="text-xs text-gray-500">Today</p>
              </div>
              <Badge variant="outline">Setup</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

