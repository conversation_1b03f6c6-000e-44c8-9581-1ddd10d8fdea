import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import {
  Edit3,
  Eye,
  Users,
  FileText,

  Settings,
  ExternalLink,
  Zap,
  Home,
  Building,
  Wrench,
  Shield,
  Camera,
  MapPin,
  CreditCard,
  Clock,
  TrendingUp,
  Activity,
  Globe,
  Palette
} from 'lucide-react';

// Available pages for editing - organized by category
const pageCategories = {
  core: [
    { id: 'home', name: 'Home Page', path: '/', description: 'Main landing page with hero section and services overview', priority: 'high' },
    { id: 'about', name: 'About Us', path: '/about', description: 'Company information and team details', priority: 'high' },
    { id: 'contact', name: 'Contact', path: '/contact', description: 'Contact form and business information', priority: 'high' },
  ],
  services: [
    { id: 'residential-roofing', name: 'Residential Roofing', path: '/residential-roofing', description: 'Residential roofing services and solutions', priority: 'high' },
    { id: 'commercial-roofing', name: 'Commercial Roofing', path: '/commercial-roofing', description: 'Commercial roofing services and solutions', priority: 'high' },
    { id: 'emergency-roofing', name: 'Emergency Roofing', path: '/emergency-roofing', description: '24/7 emergency roofing services', priority: 'medium' },
    { id: 'roof-repair', name: 'Roof Repair', path: '/roof-repair', description: 'Professional roof repair services', priority: 'medium' },
  ],
  materials: [
    { id: 'metal-roofing', name: 'Metal Roofing', path: '/metal-roofing', description: 'Metal roofing installation and services', priority: 'medium' },
    { id: 'shingle-roofing', name: 'Shingle Roofing', path: '/shingle-roofing', description: 'Shingle roofing installation and replacement', priority: 'medium' },
    { id: 'tpo-roofing', name: 'TPO Roofing', path: '/tpo-roofing', description: 'TPO roofing systems and installation', priority: 'medium' },
  ],
  additional: [
    { id: 'gallery', name: 'Gallery', path: '/gallery', description: 'Project gallery and portfolio showcase', priority: 'medium' },
    { id: 'areas-we-serve', name: 'Areas We Serve', path: '/areas-we-serve', description: 'Service areas and locations', priority: 'low' },
    { id: 'financing', name: 'Financing', path: '/financing', description: 'Financing options and payment plans', priority: 'low' },
  ]
};

// Flatten all pages for backward compatibility
const availablePages = [
  ...pageCategories.core,
  ...pageCategories.services,
  ...pageCategories.materials,
  ...pageCategories.additional
];

export default function AdminDashboard() {
  const { user, canEditPages, canManageUsers } = useAuth();
  const [selectedPage, setSelectedPage] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('core');
  const navigate = useNavigate();

  const handleEditPage = () => {
    if (selectedPage) {
      const page = availablePages.find(p => p.id === selectedPage);
      if (page) {
        // Navigate to the Puck editor for the selected page
        navigate(`/admin/edit${page.path}`);
      }
    }
  };

  const handleViewPage = () => {
    if (selectedPage) {
      const page = availablePages.find(p => p.id === selectedPage);
      if (page) {
        // Open the page in a new tab
        window.open(page.path, '_blank');
      }
    }
  };

  const stats = [
    {
      title: 'Total Pages',
      value: availablePages.length,
      description: 'Editable pages',
      icon: FileText,
      color: 'text-blue-600',
    },
    {
      title: 'Your Role',
      value: user?.roles[0] || 'Unknown',
      description: 'Current access level',
      icon: Users,
      color: 'text-green-600',
    },
    {
      title: 'Edit Access',
      value: canEditPages() ? 'Enabled' : 'Disabled',
      description: 'Page editing permissions',
      icon: Edit3,
      color: canEditPages() ? 'text-green-600' : 'text-red-600',
    },
    {
      title: 'Admin Access',
      value: canManageUsers() ? 'Full' : 'Limited',
      description: 'Administrative permissions',
      icon: Settings,
      color: canManageUsers() ? 'text-purple-600' : 'text-orange-600',
    },
  ];

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'core': return Home;
      case 'services': return Building;
      case 'materials': return Wrench;
      case 'additional': return Globe;
      default: return FileText;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              Welcome back, {user?.name}!
            </h1>
            <p className="text-blue-100 text-lg">
              Manage your Roofers LLC website content and settings from this enhanced dashboard.
            </p>
            <div className="flex items-center mt-4 space-x-4">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                {user?.roles[0] || 'User'}
              </Badge>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                {availablePages.length} Pages Available
              </Badge>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
              <Palette className="w-12 h-12 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                </div>
                <div className={`p-3 rounded-full bg-gray-100 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Page Editor Section */}
      {canEditPages() && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Edit3 className="h-6 w-6 text-blue-600" />
                <span>Content Management System</span>
              </CardTitle>
              <CardDescription>
                Edit your website pages using the powerful Puck CMS editor. Changes are saved automatically and can be previewed instantly.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Category Tabs */}
              <div className="flex flex-wrap gap-2">
                {Object.entries(pageCategories).map(([category, pages]) => {
                  const IconComponent = getCategoryIcon(category);
                  return (
                    <Button
                      key={category}
                      variant={selectedCategory === category ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category)}
                      className="flex items-center space-x-2"
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="capitalize">{category}</span>
                      <Badge variant="secondary" className="ml-1">
                        {pages.length}
                      </Badge>
                    </Button>
                  );
                })}
              </div>

              {/* Page Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {pageCategories[selectedCategory as keyof typeof pageCategories]?.map((page) => (
                  <Card
                    key={page.id}
                    className={`cursor-pointer transition-all hover:shadow-md border-2 ${
                      selectedPage === page.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedPage(page.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-gray-900">{page.name}</h3>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getPriorityColor(page.priority)}`}
                        >
                          {page.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{page.description}</p>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {page.path}
                        </Badge>
                        {selectedPage === page.id && (
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditPage();
                              }}
                              className="h-7 px-2"
                            >
                              <Zap className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewPage();
                              }}
                              className="h-7 px-2"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-green-700">
              <Globe className="h-5 w-5" />
              <span>Live Website</span>
            </CardTitle>
            <CardDescription>
              View your website as customers see it
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full border-green-200 hover:bg-green-50"
              onClick={() => window.open('/', '_blank')}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Open Website
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-blue-700">
              <Activity className="h-5 w-5" />
              <span>Analytics</span>
            </CardTitle>
            <CardDescription>
              View website performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full border-blue-200 hover:bg-blue-50"
              onClick={() => navigate('/admin/analytics')}
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              View Analytics
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-purple-700">
              <FileText className="h-5 w-5" />
              <span>Content Library</span>
            </CardTitle>
            <CardDescription>
              Manage images, documents, and media
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full border-purple-200 hover:bg-purple-50"
              onClick={() => navigate('/admin/media')}
            >
              <Camera className="mr-2 h-4 w-4" />
              Media Library
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-indigo-700">
              <FileText className="h-5 w-5" />
              <span>Page Management</span>
            </CardTitle>
            <CardDescription>
              Create and manage website pages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full border-indigo-200 hover:bg-indigo-50"
              onClick={() => navigate('/admin/pages')}
            >
              <Edit3 className="mr-2 h-4 w-4" />
              Manage Pages
            </Button>
          </CardContent>
        </Card>

        {canManageUsers() && (
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-orange-700">
                <Users className="h-5 w-5" />
                <span>User Management</span>
              </CardTitle>
              <CardDescription>
                Manage user accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                className="w-full border-orange-200 hover:bg-orange-50"
                onClick={() => navigate('/admin/users')}
              >
                <Settings className="mr-2 h-4 w-4" />
                Manage Users
              </Button>
            </CardContent>
          </Card>
        )}

        {canManageUsers() && (
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-gray-700">
                <Settings className="h-5 w-5" />
                <span>Settings</span>
              </CardTitle>
              <CardDescription>
                Configure system and application settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="outline"
                className="w-full border-gray-200 hover:bg-gray-50"
                onClick={() => navigate('/admin/settings')}
              >
                <Wrench className="mr-2 h-4 w-4" />
                System Settings
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Enhanced Recent Activity & System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <span>Recent Activity</span>
            </CardTitle>
            <CardDescription>
              Latest changes and system activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-green-900">Admin user logged in</p>
                  <p className="text-xs text-green-600">Just now</p>
                </div>
                <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                  Login
                </Badge>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-900">Puck CMS system active</p>
                  <p className="text-xs text-blue-600">13 pages available for editing</p>
                </div>
                <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                  CMS
                </Badge>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-purple-50 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-purple-900">Authentication system configured</p>
                  <p className="text-xs text-purple-600">Role-based access control active</p>
                </div>
                <Badge variant="outline" className="bg-purple-100 text-purple-700 border-purple-300">
                  System
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span>System Status</span>
            </CardTitle>
            <CardDescription>
              Current system health and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-green-900">CMS Status</span>
                </div>
                <Badge className="bg-green-100 text-green-700 border-green-300">
                  Operational
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-green-900">Authentication</span>
                </div>
                <Badge className="bg-green-100 text-green-700 border-green-300">
                  Active
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-900">Pages Available</span>
                </div>
                <Badge className="bg-blue-100 text-blue-700 border-blue-300">
                  {availablePages.length} Pages
                </Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-purple-900">User Role</span>
                </div>
                <Badge className="bg-purple-100 text-purple-700 border-purple-300">
                  {user?.roles[0] || 'Unknown'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

