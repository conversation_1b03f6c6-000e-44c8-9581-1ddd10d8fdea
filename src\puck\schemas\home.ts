import React from 'react';
import { type Config, type Field } from '@measured/puck';
import { Shield, Star, Award, CheckCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import ImageSlider from '@/components/ui/ImageSlider';
import ReviewCard from '@/components/ui/ReviewCard';
import { sharedFields, defaultVerticalPadding } from './shared';

// Types
type IconType = 'shield' | 'star' | 'award' | 'check';

interface SectionProps {
  verticalPadding?: string;
}

interface Feature {
  icon: IconType;
  text: string;
}

interface Service {
  image: string;
  title: string;
  description: string;
  link: string;
}

interface GalleryImage {
  url: string;
  alt: string;
}

interface Review {
  name: string;
  rating: number;
  review: string;
  date: string;
  location: string;
}

interface HeroProps extends SectionProps {
  backgroundImage?: string;
  tagline?: string;
  title?: string;
  subtitle?: string;
  features?: Feature[];
}

interface ServicesProps extends SectionProps {
  title?: string;
  description?: string;
  services?: Service[];
}

interface GalleryProps extends SectionProps {
  title?: string;
  description?: string;
  images?: GalleryImage[];
  imagesPerView?: number;
}

interface ReviewsProps extends SectionProps {
  title?: string;
  description?: string;
  reviews?: Review[];
}

interface CTAProps extends SectionProps {
  backgroundImage?: string;
  title?: string;
  description?: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  overlayColor?: string;
}

// Default data
const iconComponents = {
  shield: Shield,
  star: Star,
  award: Award,
  check: CheckCircle
};

const defaultGalleryImages: GalleryImage[] = [
  {
    url: 'https://images.unsplash.com/photo-1632778144849-611a9353439c?q=80&w=1920',
    alt: 'Beautiful modern house with new roof installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1620641622503-21c824c15063?q=80&w=1920',
    alt: 'Professional roofers working on a new roof'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778634162-e67c259bad89?q=80&w=1920',
    alt: 'Metal roofing with perfect installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1600585553490-76fb20a32601?q=80&w=1920',
    alt: 'Modern house exterior with premium roofing'
  },
  {
    url: 'https://images.unsplash.com/photo-1638184984605-af1f05249a56?q=80&w=1920',
    alt: 'Residential roofing project completion'
  },
  {
    url: 'https://images.unsplash.com/photo-1632778145681-61e902bb3ba3?q=80&w=1920',
    alt: 'Commercial roofing installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1635424710928-c0201da357b9?q=80&w=1920',
    alt: 'Solar roof panel installation'
  },
  {
    url: 'https://images.unsplash.com/photo-1600047508390-d39f5e147925?q=80&w=1920',
    alt: 'Luxury home with modern roofing'
  }
];

const defaultFeatures: Feature[] = [
  { icon: 'shield', text: 'Licensed & Insured' },
  { icon: 'star', text: '5-Star Service' },
  { icon: 'award', text: 'BBB A+ Rated' },
  { icon: 'check', text: '100% Satisfaction' }
];

const defaultServices: Service[] = [
  {
    image: 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
    title: 'Residential Roofing',
    description: 'Expert roofing solutions for your home, from repairs to complete replacements.',
    link: '/residential-roofing'
  },
  {
    image: 'https://images.unsplash.com/photo-1632778634169-8955cc0ba799',
    title: 'Commercial Roofing',
    description: 'Comprehensive roofing services for businesses and commercial properties.',
    link: '/commercial-roofing'
  },
  {
    image: 'https://images.unsplash.com/photo-1632778634451-1c4a347774ce',
    title: 'Emergency Services',
    description: '24/7 emergency roofing repairs when you need them most.',
    link: '/emergency-roofing'
  }
];

const defaultReviews: Review[] = [
  {
    name: "Michael Rodriguez",
    rating: 5,
    review: "Outstanding service from start to finish! The team was professional, punctual, and did an amazing job with our new roof. They cleaned up thoroughly after the job and the results exceeded our expectations.",
    date: "January 15, 2024",
    location: "Miami, FL"
  },
  {
    name: "Sarah Thompson",
    rating: 5,
    review: "After Hurricane Ian, we needed emergency roof repairs. They responded quickly and fixed everything perfectly. Their team explained everything clearly and made sure we were comfortable with the process.",
    date: "February 1, 2024",
    location: "Fort Lauderdale, FL"
  },
  {
    name: "David Martinez",
    rating: 5,
    review: "The best roofing company in Florida! Their attention to detail and quality of work is unmatched. They installed our metal roof and it looks fantastic. Great value for the investment.",
    date: "December 28, 2023",
    location: "Hollywood, FL"
  },
  {
    name: "Jennifer Clark",
    rating: 5,
    review: "I can't recommend them enough! They were extremely professional and completed our roof replacement ahead of schedule. Their team was courteous and kept us informed throughout the process.",
    date: "March 5, 2024",
    location: "Boca Raton, FL"
  },
  {
    name: "Robert Wilson",
    rating: 5,
    review: "Excellent workmanship and customer service. They handled our insurance claim expertly and made the entire process stress-free. The new roof looks amazing!",
    date: "January 30, 2024",
    location: "Coral Gables, FL"
  },
  {
    name: "Emily Torres",
    rating: 5,
    review: "Very impressed with their work! They were able to repair our complex tile roof with precision. Their attention to detail and professionalism is outstanding.",
    date: "February 15, 2024",
    location: "Kendall, FL"
  },
  {
    name: "William Anderson",
    rating: 5,
    review: "They did an incredible job on our commercial building's roof. The project was completed on time and within budget. Their team's expertise is evident in the quality of work.",
    date: "March 1, 2024",
    location: "Doral, FL"
  },
  {
    name: "Maria Sanchez",
    rating: 5,
    review: "After getting multiple quotes, we chose them for their expertise and competitive pricing. They delivered exactly what they promised - a beautiful, high-quality roof installation.",
    date: "February 20, 2024",
    location: "Aventura, FL"
  },
  {
    name: "James Peterson",
    rating: 5,
    review: "Outstanding experience from start to finish. Their team was professional, efficient, and left our property spotless. The new roof has transformed the look of our home.",
    date: "March 10, 2024",
    location: "Pinecrest, FL"
  }
];

// Export the config
export const homeConfig: Config = {
  components: {
    HeroSection: {
      render: ({ 
        backgroundImage = '',
        tagline = '#1 Trusted Roofing Company in Florida',
        title = 'Transform Your Roof, Protect Your Home',
        subtitle = "Florida's most trusted roofing experts with over 30+ years of experience. Free estimates and financing available.",
        features = defaultFeatures,
        verticalPadding = defaultVerticalPadding
      }: HeroProps) => {
        return React.createElement('section', {
          className: `relative min-h-screen flex items-center bg-cover bg-center bg-fixed overflow-hidden ${verticalPadding}`,
          style: {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url("${backgroundImage}")`
          }
        }, [
          React.createElement('div', {
            key: 'overlay',
            className: 'absolute inset-0 hero-gradient animate-pulse-subtle'
          }),
          
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4 relative'
          }, [
            React.createElement('div', {
              key: 'content',
              className: 'max-w-3xl animate-fade-in'
            }, [
              React.createElement('div', {
                key: 'tagline-wrapper',
                className: 'mb-8 inline-block'
              }, 
                React.createElement('span', {
                  className: 'bg-accent/90 text-white px-6 py-2 rounded-full text-lg font-medium backdrop-blur'
                }, tagline)
              ),

              React.createElement('h1', {
                key: 'title',
                className: 'text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight hero-text-shadow'
              }, title),

              React.createElement('p', {
                key: 'subtitle',
                className: 'text-xl md:text-2xl text-white/90 mb-8 leading-relaxed hero-text-shadow'
              }, subtitle),

              React.createElement('div', {
                key: 'cta-buttons',
                className: 'flex flex-col sm:flex-row gap-4 mb-12'
              }, [
                React.createElement(Link, {
                  key: 'primary-cta',
                  to: '/contact',
                  className: 'btn-accent text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift text-center'
                }, 'Get Free Estimate'),
                React.createElement('a', {
                  key: 'secondary-cta',
                  href: 'tel:+13053761808',
                  className: 'btn-secondary text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift flex items-center justify-center backdrop-blur'
                }, 'Call (*************')
              ]),

              React.createElement('div', {
                key: 'features',
                className: 'grid grid-cols-2 md:grid-cols-4 gap-6 bg-white/10 backdrop-blur rounded-xl p-6 animate-fade-in'
              }, features.map((feature, index) => {
                const IconComponent = iconComponents[feature.icon];
                return React.createElement('div', {
                  key: `feature-${index}`,
                  className: 'flex flex-col items-center text-center text-white hover-lift'
                }, [
                  React.createElement(IconComponent, {
                    key: 'icon',
                    className: 'w-10 h-10 text-accent mb-2'
                  }),
                  React.createElement('span', {
                    key: 'text',
                    className: 'font-bold'
                  }, feature.text)
                ]);
              }))
            ])
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        backgroundImage: { type: 'text' },
        tagline: { type: 'text' },
        title: { type: 'text' },
        subtitle: { type: 'text' },
        features: {
          type: 'array',
          arrayFields: {
            icon: {
              type: 'select',
              options: [
                { label: 'Shield', value: 'shield' },
                { label: 'Star', value: 'star' },
                { label: 'Award', value: 'award' },
                { label: 'Check', value: 'check' }
              ]
            },
            text: { type: 'text' }
          }
        }
      }
    },

    ServicesGrid: {
      render: ({
        title = 'Our Services',
        description = 'Expert roofing solutions tailored to your needs, delivered with unmatched quality and professionalism.',
        services = defaultServices,
        verticalPadding = defaultVerticalPadding
      }: ServicesProps) => {
        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-4xl font-bold text-center mb-4'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto'
            }, description),
            React.createElement('div', {
              key: 'services-grid',
              className: 'grid grid-cols-1 md:grid-cols-3 gap-8'
            }, services.map((service, index) => 
              React.createElement('div', {
                key: `service-${index}`,
                className: 'bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-all duration-300 group'
              }, [
                React.createElement('div', {
                  key: 'image-wrapper',
                  className: 'relative overflow-hidden'
                }, [
                  React.createElement('img', {
                    key: 'image',
                    src: service.image,
                    alt: service.title,
                    className: 'w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110'
                  }),
                  React.createElement('div', {
                    key: 'overlay',
                    className: 'absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'
                  })
                ]),
                React.createElement('div', {
                  key: 'content',
                  className: 'p-8'
                }, [
                  React.createElement('h3', {
                    key: 'title',
                    className: 'text-2xl font-bold mb-4 group-hover:text-primary transition-colors'
                  }, service.title),
                  React.createElement('p', {
                    key: 'description',
                    className: 'text-gray-600 mb-6'
                  }, service.description),
                  React.createElement(Link, {
                    key: 'link',
                    to: service.link,
                    className: 'text-primary font-semibold hover:text-primary-dark flex items-center group'
                  }, [
                    'Learn More',
                    React.createElement('svg', {
                      key: 'arrow',
                      className: 'w-4 h-4 ml-2 transform transition-transform group-hover:translate-x-2',
                      fill: 'none',
                      stroke: 'currentColor',
                      viewBox: '0 0 24 24'
                    }, React.createElement('path', {
                      strokeLinecap: 'round',
                      strokeLinejoin: 'round',
                      strokeWidth: '2',
                      d: 'M9 5l7 7-7 7'
                    }))
                  ])
                ])
              ])
            ))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'text' },
        services: {
          type: 'array',
          arrayFields: {
            image: { type: 'text' },
            title: { type: 'text' },
            description: { type: 'text' },
            link: { type: 'text' }
          }
        }
      }
    },

    GallerySection: {
      render: ({
        title = 'Project Gallery',
        description = 'Some of our completed roofing projects across Florida.',
        images = defaultGalleryImages,
        imagesPerView = 4,
        verticalPadding = defaultVerticalPadding
      }: GalleryProps) => {
        return React.createElement('section', {
          className: `bg-white ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-4xl font-bold text-center mb-4'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto'
            }, description),
            React.createElement('div', {
              key: 'slider-wrapper',
              className: 'max-w-full'
            }, React.createElement(ImageSlider, {
              images,
              imagesPerView
            }))
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'text' },
        imagesPerView: { type: 'number' },
        images: {
          type: 'array',
          arrayFields: {
            url: { type: 'text' },
            alt: { type: 'text' }
          }
        }
      }
    },

    ReviewsSection: {
      render: ((props: ReviewsProps) => {
        const {
          title = 'Customer Reviews',
          description = 'See what our satisfied customers have to say about their experience with us.',
          reviews = defaultReviews,
          verticalPadding = defaultVerticalPadding
        } = props;

        return React.createElement('section', {
          className: `bg-gray-50 ${verticalPadding}`
        }, [
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-4xl font-bold text-center mb-4'
            }, title),
            React.createElement('p', {
              key: 'description',
              className: 'text-xl text-gray-600 text-center mb-12 max-w-2xl mx-auto'
            }, description),
            React.createElement('div', {
              key: 'reviews-grid',
              className: 'grid grid-cols-1 md:grid-cols-3 gap-8'
            }, reviews.map((review, index) => 
              React.createElement(ReviewCard, {
                key: `review-${index}`,
                ...review
              })
            ))
          ])
        ]);
      }) as Config['components']['ReviewsSection']['render'],
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        title: { type: 'text' },
        description: { type: 'text' },
        reviews: {
          type: 'array',
          arrayFields: {
            name: { type: 'text' },
            rating: { type: 'number' },
            review: { type: 'text' },
            date: { type: 'text' },
            location: { type: 'text' }
          }
        }
      }
    },

    CTASection: {
      render: ({
        backgroundImage = 'https://images.unsplash.com/photo-1632778644429-c910be55cc51',
        title = "Ready to Transform Your Roof?",
        description = "Get a free estimate for your roofing project. Our experts are ready to help you protect your most valuable investment.",
        primaryButtonText = "Get Free Estimate",
        primaryButtonLink = "/contact",
        secondaryButtonText = "Call (*************",
        secondaryButtonLink = "tel:+13053761808",
        overlayColor = "rgba(0, 0, 0, 0.65)",
        verticalPadding = defaultVerticalPadding
      }: CTAProps) => {
        return React.createElement('section', {
          className: `relative min-h-[500px] flex items-center bg-cover bg-center overflow-hidden ${verticalPadding}`,
          style: {
            backgroundImage: `linear-gradient(${overlayColor}, ${overlayColor}), url("${backgroundImage}")`
          }
        }, [
          // Content container
          React.createElement('div', {
            key: 'container',
            className: 'container mx-auto px-4 relative z-10'
          }, [
            React.createElement('div', {
              key: 'content',
              className: 'max-w-3xl mx-auto text-center'
            }, [
              React.createElement('h2', {
                key: 'title',
                className: 'text-4xl md:text-5xl font-bold text-white mb-6'
              }, title),
              React.createElement('p', {
                key: 'description',
                className: 'text-xl text-white/90 mb-12'
              }, description),
              React.createElement('div', {
                key: 'buttons',
                className: 'flex flex-col sm:flex-row gap-4 justify-center'
              }, [
                React.createElement(Link, {
                  key: 'primary-button',
                  to: primaryButtonLink,
                  className: 'btn-accent text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift text-center'
                }, primaryButtonText),
                React.createElement('a', {
                  key: 'secondary-button',
                  href: secondaryButtonLink,
                  className: 'btn-secondary text-lg py-4 px-8 rounded-full shadow-lg hover:shadow-xl hover-lift flex items-center justify-center backdrop-blur'
                }, secondaryButtonText)
              ])
            ])
          ])
        ]);
      },
      fields: {
        verticalPadding: sharedFields.verticalPadding,
        backgroundImage: { type: 'text' },
        title: { type: 'text' },
        description: { type: 'text' },
        primaryButtonText: { type: 'text' },
        primaryButtonLink: { type: 'text' },
        secondaryButtonText: { type: 'text' },
        secondaryButtonLink: { type: 'text' },
        overlayColor: { type: 'text' }
      }
    }
  }
};