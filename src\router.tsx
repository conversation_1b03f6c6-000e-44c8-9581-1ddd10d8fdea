import { createBrowserRouter } from "react-router-dom";
import Layout from "./components/Layout";

import Home from "./pages/Home";
import Commercial from "./pages/Commercial";
import Residential from "./pages/Residential";
import Emergency from "./pages/Emergency";
import MetalRoofing from "./pages/MetalRoofing";
import ShingleRoofing from "./pages/ShingleRoofing";
import TPORoofing from "./pages/TPORoofing";
import RoofRepair from "./pages/RoofRepair";
import Gallery from "./pages/Gallery";
import About from "./pages/About";
import Contact from "./pages/Contact";
import AreasWeServe from "./pages/AreasWeServe";
import Financing from "./pages/Financing";

export const router = createBrowserRouter([
  {
    element: <Layout />,
    children: [
      {
        path: "/",
        element: <Home />,
      },
      {
        path: "/commercial",
        element: <Commercial />,
      },
      {
        path: "/residential",
        element: <Residential />,
      },
      {
        path: "/emergency",
        element: <Emergency />,
      },
      {
        path: "/metal-roofing",
        element: <MetalRoofing />,
      },
      {
        path: "/shingle-roofing",
        element: <ShingleRoofing />,
      },
      {
        path: "/tpo-roofing",
        element: <TPORoofing />,
      },
      {
        path: "/repair",
        element: <RoofRepair />,
      },
      {
        path: "/gallery",
        element: <Gallery />,
      },
      {
        path: "/about",
        element: <About />,
      },
      {
        path: "/contact",
        element: <Contact />,
      },
      {
        path: "/areas-we-serve",
        element: <AreasWeServe />,
      },
      {
        path: "/financing",
        element: <Financing />,
      }
    ],
  },
]);