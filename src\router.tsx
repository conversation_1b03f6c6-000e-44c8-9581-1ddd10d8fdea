import { createBrowserRouter, Navigate } from "react-router-dom";
import Layout from "./components/Layout";
import AdminLayout from "./components/AdminLayout";
import { ProtectedRoute } from "./hooks/useAuth";

// Public pages
import Home from "./pages/Home";
import Commercial from "./pages/Commercial";
import Residential from "./pages/Residential";
import Emergency from "./pages/Emergency";
import MetalRoofing from "./pages/MetalRoofing";
import ShingleRoofing from "./pages/ShingleRoofing";
import TPORoofing from "./pages/TPORoofing";
import RoofRepair from "./pages/RoofRepair";
import Gallery from "./pages/Gallery";
import About from "./pages/About";
import Contact from "./pages/Contact";
import AreasWeServe from "./pages/AreasWeServe";
import Financing from "./pages/Financing";

// Auth pages
import Login from "./pages/Login";

// Admin pages
import AdminDashboard from "./pages/AdminDashboard";

export const router = createBrowserRouter([
  // Public routes
  {
    element: <Layout />,
    children: [
      {
        path: "/",
        element: <Home />,
      },
      {
        path: "/commercial",
        element: <Commercial />,
      },
      {
        path: "/residential",
        element: <Residential />,
      },
      {
        path: "/emergency",
        element: <Emergency />,
      },
      {
        path: "/metal-roofing",
        element: <MetalRoofing />,
      },
      {
        path: "/shingle-roofing",
        element: <ShingleRoofing />,
      },
      {
        path: "/tpo-roofing",
        element: <TPORoofing />,
      },
      {
        path: "/repair",
        element: <RoofRepair />,
      },
      {
        path: "/gallery",
        element: <Gallery />,
      },
      {
        path: "/about",
        element: <About />,
      },
      {
        path: "/contact",
        element: <Contact />,
      },
      {
        path: "/areas-we-serve",
        element: <AreasWeServe />,
      },
      {
        path: "/financing",
        element: <Financing />,
      }
    ],
  },
  // Authentication routes
  {
    path: "/login",
    element: <Login />,
  },
  // Protected admin routes
  {
    path: "/admin",
    element: (
      <ProtectedRoute
        fallback={<Navigate to="/login?callbackUrl=/admin" replace />}
      >
        <AdminLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <AdminDashboard />,
      },
      // Add more admin routes here as needed
    ],
  },
]);