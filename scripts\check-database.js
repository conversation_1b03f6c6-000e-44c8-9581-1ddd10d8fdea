import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkDatabase() {
  let connection;
  
  try {
    console.log('🔍 Checking database connection...');
    console.log('Database config:', {
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      database: process.env.DATABASE_NAME,
      port: process.env.DATABASE_PORT,
    });

    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('✅ Successfully connected to MySQL database');

    // Check tables
    console.log('\n📋 Checking database tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    
    if (tables.length === 0) {
      console.log('📝 No tables found in database - this is normal for a new database');
    } else {
      console.log(`📊 Found ${tables.length} table(s):`);
      tables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`  ${index + 1}. ${tableName}`);
      });
    }

    // Check database size
    const [sizeResult] = await connection.execute(`
      SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [process.env.DATABASE_NAME]);
    
    const dbSize = sizeResult[0]['DB Size in MB'] || 0;
    console.log(`💾 Database size: ${dbSize} MB`);

    // Test basic operations
    console.log('\n🧪 Testing basic database operations...');
    
    // Create test table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS test_connection (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_data VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Insert test data
    const testData = `Test at ${new Date().toISOString()}`;
    await connection.execute(
      'INSERT INTO test_connection (test_data) VALUES (?)',
      [testData]
    );
    
    // Read test data
    const [testResults] = await connection.execute(
      'SELECT * FROM test_connection ORDER BY id DESC LIMIT 1'
    );
    
    if (testResults.length > 0) {
      console.log('✅ Database read/write operations working correctly');
    }
    
    // Clean up test table
    await connection.execute('DROP TABLE IF EXISTS test_connection');
    console.log('🧹 Cleaned up test data');

    console.log('\n🎉 Database check completed successfully!');
    console.log('✅ Connection: Working');
    console.log('✅ Read/Write: Working');
    console.log('✅ Database ready for use');

  } catch (error) {
    console.error('\n❌ Database check failed:');
    console.error('Error:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 Check your database credentials in .env file');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure MySQL server is running');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 Database does not exist - it will be created automatically');
    }
    
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkDatabase()
  .then(() => {
    console.log('\n✨ All checks passed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Check failed:', error.message);
    process.exit(1);
  });

export { checkDatabase };
